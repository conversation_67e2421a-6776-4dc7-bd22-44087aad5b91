<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>police-label</artifactId>
    <groupId>com.trs</groupId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.trs.police</groupId>
  <artifactId>police-label-engine</artifactId>
  <build>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <filtering>true</filtering>
        <directory>src/test/resources</directory>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>1.18.24</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>scala-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <finalName>${project.artifactId}_${hadoopVersion}_${sparkVersion}-shaded-${project.version}</finalName>
              <transformers>
                <transformer />
              </transformers>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
              <relocations>
                <relocation>
                  <pattern>com.google.common</pattern>
                  <shadedPattern>com.trs.shaded.com.google.common</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.apache.commons.cli</pattern>
                  <shadedPattern>com.trs.shaded.org.apache.commons.cli</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>redis.clients.jedis</pattern>
                  <shadedPattern>com.trs.shaded.redis.clients.jedis</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M7</version>
        <configuration>
          <useSystemClassLoader>false</useSystemClassLoader>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>engine-dev</id>
      <properties>
        <hadoopVersion>3.2.4</hadoopVersion>
        <icebergVersion>1.3.1</icebergVersion>
        <spring.cloud.nacos.config.namespace>ys-dev</spring.cloud.nacos.config.namespace>
        <spring.cloud.nacos.config.password>nacos</spring.cloud.nacos.config.password>
        <sparkVersion>3.2.2</sparkVersion>
        <spring.cloud.nacos.config.username>nacos</spring.cloud.nacos.config.username>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <hiveVersion>3.1.3</hiveVersion>
        <spring.cloud.nacos.config.server-addr>http://10.18.20.131:8848</spring.cloud.nacos.config.server-addr>
      </properties>
    </profile>
    <profile>
      <id>engine-luzhou</id>
      <properties>
        <hadoopVersion>3.1.1-hw-ei-312032</hadoopVersion>
        <icebergVersion>1.3.0_hw-ei-312032</icebergVersion>
        <spring.cloud.nacos.config.namespace>51085a2c-0510-4a93-a365-46c76811c019</spring.cloud.nacos.config.namespace>
        <spring.cloud.nacos.config.password>trsadmin@028</spring.cloud.nacos.config.password>
        <sparkVersion>3.1.1-hw-ei-312032</sparkVersion>
        <spring.cloud.nacos.config.username>nacos</spring.cloud.nacos.config.username>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <hiveVersion>3.1.0-hw-ei-312032</hiveVersion>
        <spring.cloud.nacos.config.server-addr>80.75.95.18:8848</spring.cloud.nacos.config.server-addr>
      </properties>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>org.scala-lang</groupId>
      <artifactId>scala-reflect</artifactId>
      <version>2.12.17</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.scala-lang</groupId>
      <artifactId>scala-compiler</artifactId>
      <version>2.12.17</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-core_2.12</artifactId>
      <version>3.2.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-sql_2.12</artifactId>
      <version>3.2.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-graphx_2.12</artifactId>
      <version>3.2.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-mllib_2.12</artifactId>
      <version>3.2.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-streaming_2.12</artifactId>
      <version>3.2.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-hive_2.12</artifactId>
      <version>3.2.2</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>hive-llap-client</artifactId>
          <groupId>org.apache.hive</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.8.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-params</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-engine</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-commons</artifactId>
      <version>1.8.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>4.0.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>easyexcel-core</artifactId>
          <groupId>com.alibaba</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.locationtech.spatial4j</groupId>
      <artifactId>spatial4j</artifactId>
      <version>0.8</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jacksonVersion}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jacksonVersion}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_${scalaMajorVersion}</artifactId>
        <version>${jacksonVersion}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <distributionManagement>
    <repository>
      <id>nexus-releases</id>
      <url>https://nexus.trscd.com.cn/repository/releases</url>
    </repository>
    <snapshotRepository>
      <id>nexus-snapshots</id>
      <url>https://nexus.trscd.com.cn/repository/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <geotoolsVersion>24.6</geotoolsVersion>
    <scalaVersion>2.12.17</scalaVersion>
    <guavaVersion>31.1-jre</guavaVersion>
    <flinkVersion>1.17.1</flinkVersion>
    <mapstructVersion>1.5.2.Final</mapstructVersion>
    <jacksonVersion>2.14.2</jacksonVersion>
    <springCloudVersion>2021.0.5</springCloudVersion>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <scalaMajorVersion>2.12</scalaMajorVersion>
    <springBootVersion>2.7.8</springBootVersion>
    <springCloudAlibabaVersion>2021.0.4.0</springCloudAlibabaVersion>
  </properties>
</project>
