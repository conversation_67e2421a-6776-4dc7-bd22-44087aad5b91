<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trs</groupId>
        <artifactId>police-label</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <groupId>com.trs.police</groupId>
    <artifactId>police-label-engine</artifactId>

    <properties>
        <scalaVersion>2.12.17</scalaVersion>
        <scalaMajorVersion>2.12</scalaMajorVersion>
        <flinkVersion>1.17.1</flinkVersion>
        <springBootVersion>2.7.8</springBootVersion>
        <springCloudVersion>2021.0.5</springCloudVersion>
        <springCloudAlibabaVersion>2021.0.4.0</springCloudAlibabaVersion>
        <mapstructVersion>1.5.2.Final</mapstructVersion>
        <guavaVersion>31.1-jre</guavaVersion>
        <jacksonVersion>2.14.2</jacksonVersion>
        <geotoolsVersion>24.6</geotoolsVersion>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- Scala -->
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-reflect</artifactId>
            <version>${scalaVersion}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-compiler</artifactId>
            <version>${scalaVersion}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Spark -->
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_${scalaMajorVersion}</artifactId>
            <version>${sparkVersion}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql_${scalaMajorVersion}</artifactId>
            <version>${sparkVersion}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-graphx_${scalaMajorVersion}</artifactId>
            <version>${sparkVersion}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-mllib_${scalaMajorVersion}</artifactId>
            <version>${sparkVersion}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-streaming_${scalaMajorVersion}</artifactId>
            <version>${sparkVersion}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-hive_${scalaMajorVersion}</artifactId>
            <version>${sparkVersion}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.hive</groupId>
                    <artifactId>hive-llap-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Iceberg -->
        <dependency>
            <groupId>org.apache.iceberg</groupId>
            <artifactId>iceberg-spark-runtime-3.1_${scalaMajorVersion}</artifactId>
            <version>${icebergVersion}</version>
        </dependency>

        <!-- Guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guavaVersion}</version>
        </dependency>

        <!-- Other dependencies -->
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
            <version>1.4.2</version>
        </dependency>
        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>1.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.trs.hybase</groupId>
            <artifactId>trshybase-api</artifactId>
            <version>8.0.8045-20181127-r26510</version>
        </dependency>
        <dependency>
            <groupId>com.trs.hybase</groupId>
            <artifactId>trshybase-hadoop-api</artifactId>
            <version>8.0.8040</version>
        </dependency>

        <!-- GeoTools -->
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-jts-wrapper</artifactId>
            <version>${geotoolsVersion}</version>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-geojson</artifactId>
            <version>${geotoolsVersion}</version>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-geometry</artifactId>
            <version>${geotoolsVersion}</version>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-main</artifactId>
            <version>${geotoolsVersion}</version>
        </dependency>
        <dependency>
            <groupId>org.gavaghan</groupId>
            <artifactId>geodesy</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.trs.spark</groupId>
            <artifactId>media_base_spark</artifactId>
            <version>1.2.7</version>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
        </dependency>


        <!-- TRS dependencies -->
        <dependency>
            <groupId>com.trs</groupId>
            <artifactId>media_base</artifactId>
            <version>1.4.39</version>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>6.3.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.trs</groupId>
            <artifactId>media_base_cache</artifactId>
            <version>3.1.9</version>
            <exclusions>
                <exclusion>
                    <groupId>com.trs</groupId>
                    <artifactId>media_base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.trs.web</groupId>
                    <artifactId>media_base_web_core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-commons</artifactId>
            <version>1.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.locationtech.spatial4j</groupId>
            <artifactId>spatial4j</artifactId>
            <version>0.8</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.0.16</version>
        </dependency>
        <dependency>
            <groupId>com.trs.db</groupId>
            <artifactId>db-sql-core</artifactId>
        </dependency>

        <!-- Sedona 核心依赖 -->
        <dependency>
            <groupId>org.apache.sedona</groupId>
            <artifactId>sedona-spark-shaded-3.0_2.12</artifactId>
            <version>1.6.1</version>
        </dependency>

        <!-- GeoTools 依赖 -->
        <dependency>
            <groupId>org.datasyslab</groupId>
            <artifactId>geotools-wrapper</artifactId>
            <version>1.4.0-28.2</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- Jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jacksonVersion}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jacksonVersion}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-scala_${scalaMajorVersion}</artifactId>
                <version>${jacksonVersion}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>
        <plugins>
            <!-- Compiler plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.24</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            
            <!-- Scala plugin -->
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Shade plugin (equivalent to shadowJar) -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <finalName>${project.artifactId}_${hadoopVersion}_${sparkVersion}-shaded-${project.version}</finalName>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <relocations>
                                <relocation>
                                    <pattern>com.google.common</pattern>
                                    <shadedPattern>com.trs.shaded.com.google.common</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.apache.commons.cli</pattern>
                                    <shadedPattern>com.trs.shaded.org.apache.commons.cli</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>redis.clients.jedis</pattern>
                                    <shadedPattern>com.trs.shaded.redis.clients.jedis</shadedPattern>
                                </relocation>
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Test plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M7</version>
                <configuration>
                    <useSystemClassLoader>false</useSystemClassLoader>
                </configuration>
            </plugin>
            
            <!-- Checkstyle plugin -->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-checkstyle-plugin</artifactId>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>checkstyle-main</id>-->
<!--                        <phase>verify</phase>-->
<!--                        <goals>-->
<!--                            <goal>check</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <includeTestSourceDirectory>false</includeTestSourceDirectory>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                    <execution>-->
<!--                        <id>checkstyle-test</id>-->
<!--                        <phase>verify</phase>-->
<!--                        <goals>-->
<!--                            <goal>check</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <includeTestSourceDirectory>true</includeTestSourceDirectory>-->
<!--                            <includeResources>false</includeResources>-->
<!--                            <includeTestResources>false</includeTestResources>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>engine-dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <sparkVersion>3.2.2</sparkVersion>
                <hadoopVersion>3.2.4</hadoopVersion>
                <hiveVersion>3.1.3</hiveVersion>
                <icebergVersion>1.3.1</icebergVersion>
                <!-- nacos 地址与账号信息 -->
                <spring.cloud.nacos.config.server-addr>http://10.18.20.131:8848</spring.cloud.nacos.config.server-addr>
                <spring.cloud.nacos.config.namespace>ys-dev</spring.cloud.nacos.config.namespace>
                <spring.cloud.nacos.config.username>nacos</spring.cloud.nacos.config.username>
                <spring.cloud.nacos.config.password>nacos</spring.cloud.nacos.config.password>
                <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
            </properties>
        </profile>
        <profile>
            <id>engine-luzhou</id>
            <properties>
                <sparkVersion>3.1.1-hw-ei-312032</sparkVersion>
                <hadoopVersion>3.1.1-hw-ei-312032</hadoopVersion>
                <hiveVersion>3.1.0-hw-ei-312032</hiveVersion>
                <icebergVersion>1.3.0_hw-ei-312032</icebergVersion>
                <spring.cloud.nacos.config.server-addr>80.75.95.18:8848</spring.cloud.nacos.config.server-addr>
                <spring.cloud.nacos.config.namespace>51085a2c-0510-4a93-a365-46c76811c019</spring.cloud.nacos.config.namespace>
                <spring.cloud.nacos.config.username>nacos</spring.cloud.nacos.config.username>
                <spring.cloud.nacos.config.password>trsadmin@028</spring.cloud.nacos.config.password>
                <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
            </properties>
        </profile>
    </profiles>
    
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.trscd.com.cn/repository/releases</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>https://nexus.trscd.com.cn/repository/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>