package com.trs.police.engine.action.formula.function;


import com.trs.police.engine.action.formula.NodeFunction;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

import java.util.List;


/**
 * 字符串转数字
 *
 * <AUTHOR>
 */
public class StringToNumberFunction implements NodeFunction {

    public static final StringToNumberFunction INSTANCE = new StringToNumberFunction();

    @Override
    public Column execute(List<Column> parameters) {
        Column column = parameters.get(0);
        return functions.when(column.like("."),  column.cast("double"))
                .otherwise(column.cast("long"));
    }

    @Override
    public String key() {
        return "string_to_number";
    }
}
