package com.trs.police.engine.action.formula.impl;


import com.trs.common.utils.TimeUtils;
import com.trs.police.engine.action.formula.Expression;
import com.trs.police.engine.action.formula.FormulaContext;
import com.trs.police.engine.constant.DataBaseFieldMappingType;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

import java.util.Objects;


/**
 * 常量解析
 *
 * <AUTHOR>
 */
public class ConstantExpression implements Expression {


    private Column value;

    public ConstantExpression(String value, String type) {
        Objects.requireNonNull(type, "type cannot be null");
        DataBaseFieldMappingType tp = DataBaseFieldMappingType.getType(type);
        Objects.requireNonNull(tp, "type is not supported");
        switch (tp) {
            case NUMBER:
                Column lit = functions.lit(value);
                this.value = functions.when(lit.like("."),  lit.cast("double"))
                        .otherwise(lit.cast("long"));
                break;
            case STRING:
                this.value = functions.lit(value);
                break;
            case DATETIME:
                String date = TimeUtils.dateToString(TimeUtils.stringToDate(value), TimeUtils.YYYYMMDD_HHMMSS);
                this.value = functions.to_timestamp(functions.lit(date), TimeUtils.YYYYMMDD_HHMMSS);
                break;
            case BOOLEAN:
                break;
            case GEOMETRY:
                break;
            default:
                throw new IllegalArgumentException("Unknown type: " + type);
        }
    }

    @Override
    public Column evaluate(FormulaContext context) {
        return value;
    }
}
