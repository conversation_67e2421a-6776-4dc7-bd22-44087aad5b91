package com.trs.police.engine.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 标签结果vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelComputeResultVO implements Serializable {
    /**
     * 标签名称
     */
    private String label_name;

    /**
     * 对象类型（引用GB/T 28181类型编码）
     */
    private String object_type;

    /**
     * 对象编号
     */
    private String object_number;

    /**
     * 频次
     */
    private Integer frequency;

    /**
     * 创建时间
     */
    private String create_time;

    /**
     * 更新时间
     */
    private String update_time;

    /**
     * 过期时间
     */
    private String expire_time;

    /**
     * 来源表
     */
    private String source_table;

    /**
     * 警种
     */
    private String police_kind;

    /**
     * 数据来源id
     */
    private List<SourceDataVO> source_data_ids;

    /**
     * 关联对象编号
     */
    private List<String> related_object_number;

    /**
     * 描述
     */
    private String extra_info;
}