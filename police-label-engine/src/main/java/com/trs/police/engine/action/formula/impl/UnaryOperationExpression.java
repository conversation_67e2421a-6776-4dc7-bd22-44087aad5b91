package com.trs.police.engine.action.formula.impl;


import com.trs.police.engine.action.formula.Expression;
import com.trs.police.engine.action.formula.FormulaContext;
import com.trs.police.engine.constant.Operator;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

/**
 * 一元运算符表达式
 *
 * <AUTHOR>
 */
public class UnaryOperationExpression implements Expression {
    private final Operator operator;
    private final Expression operand;

    public UnaryOperationExpression(Operator operator, Expression operand) {
        this.operator = operator;
        this.operand = operand;
    }

    @Override
    public Column evaluate(FormulaContext context) {
        Column value = operand.evaluate(context);

        switch (operator) {
            case ADD: // +
                return value;
            case SUBTRACT: // -
                return functions.when(value.isNull().or(value.equalTo("")), functions.lit("0"))
                        .otherwise(value.multiply(-1));
            default:
                throw new UnsupportedOperationException("Unsupported unary operator: " + operator);
        }
    }
}
