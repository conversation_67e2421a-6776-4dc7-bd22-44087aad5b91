package com.trs.police.engine.action.formula.impl;


import com.trs.police.engine.action.formula.Expression;
import com.trs.police.engine.action.formula.FormulaContext;
import com.trs.police.engine.action.formula.OperationFactory;
import com.trs.police.engine.constant.Operator;
import org.apache.spark.sql.Column;

/**
 * 二元运算符表达式
 *
 * <AUTHOR>
 */
public class BinaryOperationExpression implements Expression {
    private final Expression left;
    private final Operator operator;
    private final Expression right;

    public BinaryOperationExpression(Expression left, Operator operator, Expression right) {
        this.left = left;
        this.operator = operator;
        this.right = right;
    }

    @Override
    public Column evaluate(FormulaContext context) {
        Column leftValue = left.evaluate(context);
        Column rightValue = right.evaluate(context);

        // 这里应该通过工厂获取实际的计算逻辑
        return OperationFactory.calculate(leftValue, operator, rightValue);
    }
}
