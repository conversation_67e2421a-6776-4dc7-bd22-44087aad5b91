package com.trs.police.engine.constant;

/**
 * 统计方式
 */
public enum StatisticType {

    /**
     * 求平均
     */
    AVG(1),

    /**
     * 求和
     */
    SUM(2),

    /**
     * 计数
     */
    COUNT(3),

    /**
     * 最大值
     */
    MAX(4),

    /**
     * 最小值
     */
    MIN(5);

    private final Integer value;

    StatisticType(Integer value) {
        this.value = value;
    }

    /**
     * 获取value
     *
     * @return value
     */
    public Integer getValue() {
        return value;
    }

    /**
     * 根据value获取枚举
     *
     * @param value v
     * @return en
     */
    public static StatisticType of(Integer value) {
        for (StatisticType type : StatisticType.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}