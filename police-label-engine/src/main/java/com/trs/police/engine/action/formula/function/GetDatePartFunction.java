package com.trs.police.engine.action.formula.function;

import com.trs.police.engine.action.formula.NodeFunction;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

import java.util.List;


/**
 * 提取时间的某个部分
 *
 * <AUTHOR>
 */
public class GetDatePartFunction implements NodeFunction {

    public static final GetDatePartFunction INSTANCE = new GetDatePartFunction();

    @Override
    public Column execute(List<Column> parameters) {
        Column time = parameters.get(0);
        if (time==null) {
            return null;
        }
        Column type = parameters.get(1);
        return functions.when(type.equalTo("日"), functions.dayofmonth(time))
                .when(type.equalTo("月"), functions.month(time))
                .when(type.equalTo("年"), functions.year(time))
                .when(type.equalTo("分"), functions.minute(time))
                .when(type.equalTo("秒"), functions.second(time))
                .when(type.equalTo("毫秒"), functions.second(time).multiply(1000))
                .otherwise(functions.lit(null));
    }

    @Override
    public String key() {
        return "get_date_part";
    }
}
