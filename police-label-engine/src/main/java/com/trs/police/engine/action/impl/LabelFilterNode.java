package com.trs.police.engine.action.impl;

import com.trs.common.utils.StringUtils;
import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.node.properties.FilterProperties;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

/**
 * Spark过滤节点
 *
 * <AUTHOR>
 */
public class LabelFilterNode extends BaseLabelNode<FilterProperties> {

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> dataset, LabelNodeContext labelNodeContext) {
        // 应用Spark过滤条件
        String where = getNodeProperties().getWhere();
        if(StringUtils.isEmpty(where)){
            return dataset;
        }
        return dataset.where(getNodeProperties().getWhere());
    }

    @Override
    public Integer nodeType() {
        return NodeType.FILTER;
    }
}
