package com.trs.police.engine.action.formula.function;


import com.trs.police.engine.action.formula.NodeFunction;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

import java.util.List;

/**
 * 合并经纬度
 *
 * <AUTHOR>
 */
public class MergeLatLonFunction implements NodeFunction {

    public static final MergeLatLonFunction INSTANCE = new MergeLatLonFunction();

    @Override
    public Column execute(List<Column> parameters) {
        return functions.concat_ws(",", parameters.get(0), parameters.get(1));
    }

    @Override
    public String key() {
        return "merge_lat_lon";
    }
}
