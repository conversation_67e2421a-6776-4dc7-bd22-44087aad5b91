package com.trs.police.engine.action;

import com.trs.police.engine.dto.node.properties.NodeProperties;
import com.trs.police.engine.vo.LabelComputeResultVO;
import com.trs.spark.action.BaseConvertAction;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;
import java.util.List;

/**
 * 标签计算
 *
 * @param <IN> 输入数据类型
 */
public class LabelComputeAction<IN extends Serializable> extends BaseConvertAction<JavaRDD<IN>, JavaRDD<LabelComputeResultVO>> {

    private final LabelMultiComputeNode multiComputeNode;

    public LabelComputeAction(List<BaseLabelNode<? extends NodeProperties>> labelNodes) {
        this.multiComputeNode = new LabelMultiComputeNode(labelNodes);
    }

    @Override
    public JavaRDD<LabelComputeResultVO> doAction(SparkSession spark, JavaRDD<IN> in) {
        Dataset<Row> dataset = multiComputeNode.process(spark, spark.emptyDataFrame(), new LabelNodeContext());
        return dataset.as(Encoders.bean(LabelComputeResultVO.class)).toJavaRDD();
    }
}
