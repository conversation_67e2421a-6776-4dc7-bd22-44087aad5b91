package com.trs.police.engine.dto.node.properties.bean;

import com.trs.police.engine.dto.node.ValueMateBase;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分组字段
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class GroupField extends ValueMateBase {

    /**
     * 分组类型 1 秒 2 分 3 时 4 天 5 周 6 月 7 年
     */
    private Integer groupType;

    public GroupField(ValueMateBase filed, Integer groupType) {
        super(filed.getFromNode(), filed.getCnName(), filed.getEnName(), filed.getTypeCode());
        this.groupType = groupType;
    }

    /**
     * 获取字段信息
     *
     * @return 字段信息
     */
    public ValueMateBase getFiled() {
        return this;
    }
}
