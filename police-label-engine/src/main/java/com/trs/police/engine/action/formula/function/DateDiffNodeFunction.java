package com.trs.police.engine.action.formula.function;

import com.trs.police.engine.action.formula.NodeFunction;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

import java.util.List;


/**
 * 时间差计算函数
 *
 * <AUTHOR>
 */
public class DateDiffNodeFunction implements NodeFunction {

    public static final DateDiffNodeFunction INSTANCE = new DateDiffNodeFunction();

    private DateDiffNodeFunction() {
    }

    @Override
    public Column execute(List<Column> parameters) {
        if (parameters.get(0)==null || parameters.get(1)==null) {
            return null;
        }
        return toTime(parameters.get(0)).minus(toTime(parameters.get(1)));
    }

    private Column toTime(Column column){
        return functions.coalesce(
                functions.unix_timestamp(column, "yyyy-MM-dd HH:mm:ss"),
                functions.unix_timestamp(column, "yyyy/MM/dd HH:mm:ss"),
                functions.unix_timestamp(column, "yyyy-MM-dd"),
                functions.unix_timestamp(column, "yyyy/MM/dd"),
                functions.unix_timestamp(column, "yyyyMMdd")
                ).multiply(1000);
    }

    @Override
    public String key() {
        return "date_diff";
    }
}
