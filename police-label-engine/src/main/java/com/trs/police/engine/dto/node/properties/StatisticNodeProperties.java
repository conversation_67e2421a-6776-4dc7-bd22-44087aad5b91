package com.trs.police.engine.dto.node.properties;

import com.trs.police.engine.dto.node.ValueMateBase;
import com.trs.police.engine.dto.node.properties.bean.GroupField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 统计节点配置
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class StatisticNodeProperties extends NodeProperties {

    /**
     * 输出字段信息
     */
    private ValueMateBase outValue;

    /**
     * 统计字段信息
     */
    private ValueMateBase statisticValue;

    /**
     * 统计方式 {@link StatisticType}
     */
    private Integer statisticType;

    /**
     * 分组字段
     */
    private List<GroupField> groupField;
}
