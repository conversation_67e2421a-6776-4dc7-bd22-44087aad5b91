package com.trs.police.engine.action.impl;

import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.LabelMultiComputeNode;
import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.LabelComputeDTO;
import com.trs.police.engine.dto.node.properties.RelevancyNodeProperties;
import com.trs.police.engine.utils.NodeUtils;
import com.trs.police.engine.vo.LabelNodeDTO;
import com.trs.police.engine.vo.RowFieldVo;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.spark.sql.*;
import org.apache.spark.sql.expressions.Window;
import org.apache.spark.sql.expressions.WindowSpec;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.apache.spark.sql.functions.*;

/**
 * Spark集合节点
 *
 * <AUTHOR>
 */
@Slf4j
public class LabelUnionNode extends BaseLabelNode<RelevancyNodeProperties> {

    @Setter
    private List<LabelNodeDTO> flow;

    @Setter
    private String lastNodeId;

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> dataset, LabelNodeContext context) {
        RelevancyNodeProperties nodeProperties = getNodeProperties();

        LabelNodeContext otherContext = new LabelNodeContext();
        final Dataset<Row> otherDateset = new LabelMultiComputeNode(NodeUtils.getLabelNodes(new LabelComputeDTO(flow)))
                .process(spark, spark.emptyDataFrame(), otherContext);
        otherContext.getIdCols().stream().forEach(rowFieldVo -> {
            if (!context.getIdCols().contains(rowFieldVo)) {
                context.getIdCols().add(rowFieldVo);
            }
        });
        Dataset<Row> datasetA = nodeProperties.getTableA().equals(lastNodeId) ?  dataset : otherDateset;
        Dataset<Row> datasetB = nodeProperties.getTableB().equals(lastNodeId) ?  dataset : otherDateset;
        List<RowFieldVo> outputRow = getOutputRow();
        for (RowFieldVo rowFieldVo : outputRow) {
            datasetA = datasetA.withColumnRenamed(rowFieldVo.getName(), rowFieldVo.getTargetName());
            datasetB = datasetB.withColumnRenamed(rowFieldVo.getName(), rowFieldVo.getTargetName());
        }
        // 获取重复的id列，并重命名
        final List<String> duplicateIdColList = getDuplicateIdCol(datasetA, datasetB, context);
        for (String idCol : duplicateIdColList) {
            datasetA = datasetA.withColumnRenamed(idCol, "A_" + idCol);
            datasetB = datasetB.withColumnRenamed(idCol, "B_" + idCol);
        }
        if(nodeProperties.getType().equals(1)){
            Column joinContion = compare(datasetA, datasetB, nodeProperties.getMatch().get(0));
            for (int i = 1; i < nodeProperties.getMatch().size(); i++) {
                joinContion = joinContion.and(compare(datasetA, datasetB, nodeProperties.getMatch().get(i)));
            }
            dataset = datasetA.join(datasetB, joinContion, "inner").distinct();
        } else if(nodeProperties.getType().equals(2)){
            dataset = datasetA.unionByName(datasetB, true);
        } else if(nodeProperties.getType().equals(3)){
            dataset = unionWithDistinct(datasetA, datasetB, nodeProperties);
        }

        // 如果两个数据集的id是来源同一张表，需要根据id排重，然后合并重复的id列
        if(!duplicateIdColList.isEmpty()){
            List<Column> idCols = new ArrayList<>();
            for (String idCol : duplicateIdColList) {
                idCols.add(functions.col("A_"+idCol));
                idCols.add(functions.col("B_"+idCol));
                dataset = dataset.withColumn(idCol, functions.concat(functions.col("A_"+idCol), functions.col("B_"+idCol)));
            }
            WindowSpec windowSpec = Window.partitionBy(idCols.stream().toArray(Column[]::new))
                    .orderBy(idCols.get(0));
            dataset = dataset.withColumn("rowNum", row_number().over(windowSpec));
            dataset = dataset.filter(col("rowNum").equalTo(1)).drop("rowNum");
        }

        return dataset;
    }

    private Column compare(Dataset<Row> datasetA, Dataset<Row> datasetB, RelevancyNodeProperties.MatchConfig matchConfig){
        String colA = matchConfig.getA().getCol();
        String colB = matchConfig.getB().getCol();
        String matchType = matchConfig.getMatchType();
        if ("eq".equals(matchType)){
            return datasetA.col(colA).equalTo(datasetB.col(colB));
        } else if ("gt".equals(matchType)){
            return datasetA.col(colA).gt(datasetB.col(colB));
        } else if ("lt".equals(matchType)){
            return datasetA.col(colA).lt(datasetB.col(colB));
        } else if ("gte".equals(matchType)){
            return datasetA.col(colA).geq(datasetB.col(colB));
        } else if ("lte".equals(matchType)){
            return datasetA.col(colA).leq(datasetB.col(colB));
        } else if ("neq".equals(matchType)){
            return datasetA.col(colA).notEqual(datasetB.col(colB));
        }
        throw new RuntimeException("不支持的匹配类型");
    }

    private Dataset<Row> unionWithDistinct(Dataset<Row> datasetA, Dataset<Row> datasetB, RelevancyNodeProperties nodeProperties){
        String sourceColumn = "source_col";
        datasetA = datasetA.withColumn(sourceColumn, lit("A"));
        datasetB = datasetB.withColumn(sourceColumn, lit("B"));
        if(nodeProperties.getWeight().equals(3) || nodeProperties.getWeight().equals(4)){
            for (RelevancyNodeProperties.CompareFieldConfig compareFieldConfig : nodeProperties.getCompareField()) {
                String compareCol = compareFieldConfig.getA().getCompareCol();
                datasetA = datasetA.withColumn(compareCol, col(compareFieldConfig.getA().getCol()));
                datasetB = datasetB.withColumn(compareCol, col(compareFieldConfig.getB().getCol()));
            }
        }
        Dataset<Row> compareDateSet = datasetA.unionByName(datasetB, true);
        WindowSpec window = null;
        Column orderExpr;
        Column groupColumn = col(nodeProperties.getMatch().get(0).getA().getCol());
        for (int i = 1; i < nodeProperties.getMatch().size(); i++) {
            groupColumn.and(col(nodeProperties.getMatch().get(i).getA().getCol()));
        }
        switch (nodeProperties.getWeight()){
            case 1: // 取数据A
                orderExpr = when(col(sourceColumn).equalTo("A"), 1)
                        .otherwise(2); // A优先
                window = Window.partitionBy(groupColumn).orderBy(orderExpr);
                break;
            case 2: // 取数据B
                orderExpr = when(col(sourceColumn).equalTo("B"), 1)
                        .otherwise(2); // B优先
                window = Window.partitionBy(groupColumn).orderBy(orderExpr);
                break;
            case 3: // 取较大值
                Column[] descArr = nodeProperties.getCompareField().stream()
                        .map(compareFieldConfig -> desc("`"+compareFieldConfig.getA().getCompareCol()+"`"))
                        .toArray(Column[]::new);
                window = Window.partitionBy(groupColumn).orderBy(descArr);
                break;
            case 4: // 取较小值
                Column[] ascArr = nodeProperties.getCompareField().stream()
                        .map(compareFieldConfig -> asc("`"+compareFieldConfig.getA().getCompareCol()+"`"))
                        .toArray(Column[]::new);
                window = Window.partitionBy(groupColumn).orderBy(ascArr);
                break;
            default:
                break;
        }
        return compareDateSet.withColumn("row_num", row_number().over(window))
                .filter(col("row_num").equalTo(1))
                .drop("row_num");
    }

    private List<String> getDuplicateIdCol(Dataset<Row> datasetA, Dataset<Row> datasetB, LabelNodeContext context) {
        String[] columnAs = datasetA.columns();
        String[] columnBs = datasetB.columns();
        return context.getIdCols().stream().map(idCol -> {
            if (ArrayUtils.contains(columnAs, idCol.getName()) && ArrayUtils.contains(columnBs, idCol.getName())) {
                return idCol.getName();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public BaseLabelNode<RelevancyNodeProperties> loadNode(LabelNodeDTO dto) {
        LabelUnionNode loadNode = (LabelUnionNode) super.loadNode(dto);
        loadNode.setFlow(dto.getFlow());
        loadNode.setLastNodeId(dto.getLastNodeId());
        return loadNode;
    }

    @Override
    public Integer nodeType() {
        return NodeType.RELEVANCY;
    }
}
