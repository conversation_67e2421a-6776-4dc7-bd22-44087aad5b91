package com.trs.police.engine.action.formula;

import com.trs.police.engine.constant.Operator;
import org.apache.spark.sql.Column;

/**
 * 操作符号工厂
 *
 * <AUTHOR>
 */
public class OperationFactory {

    /**
     * 计算
     *
     * @param left   左侧
     * @param operator 操作符号
     * @param right   右侧
     * @return 结果
     */
    public static Column calculate(Column left, Operator operator, Column right) {
        switch (operator) {
            case ADD:
                return left.plus(right);
            case SUBTRACT:
                return left.minus(right);
            case MULTIPLY:
                return left.multiply(right);
            case DIVIDE:
                return left.divide(right);
            default:
                throw new IllegalArgumentException("Unsupported operator: " + operator);

        }
    }
}
