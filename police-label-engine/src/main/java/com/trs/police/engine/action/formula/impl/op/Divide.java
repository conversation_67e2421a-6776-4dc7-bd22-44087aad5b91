package com.trs.police.engine.action.formula.impl.op;

import com.trs.police.engine.action.formula.Operator;
import com.trs.police.engine.constant.DataBaseFieldMappingType;
import com.trs.police.engine.vo.FieldValue;

import java.math.BigDecimal;

import static com.trs.police.engine.constant.NodeConstants.NUMBER;


/**
 * 除法
 *
 * <AUTHOR>
 */
public class Divide implements Operator {

    public static final Divide INSTANCE = new Divide();

    private Divide() {
    }

    @Override
    public FieldValue calculate(FieldValue left, FieldValue right) {
        if (!DataBaseFieldMappingType.NUMBER.getFieldType().equals(left.getTypeCode())
                || !DataBaseFieldMappingType.NUMBER.getFieldType().equals(right.getTypeCode())) {
            throw new RuntimeException("除法运算要求两个参数都是数字");
        }
        Double v = BigDecimal.valueOf(Double.valueOf(left.getValue()))
                .divide(BigDecimal.valueOf(Double.valueOf(right.getValue())))
                .doubleValue();
        return new FieldValue(v.toString(), NUMBER);
    }
}
