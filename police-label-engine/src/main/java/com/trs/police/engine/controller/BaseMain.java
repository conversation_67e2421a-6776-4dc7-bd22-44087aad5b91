package com.trs.police.engine.controller;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.spark.SparkContextHelper;
import com.trs.spark.configuration.ConfigInMap;
import com.trs.spark.configuration.ConfigInNacos;
import com.trs.spark.constant.RunMode;
import com.trs.police.engine.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.sedona.sql.utils.SedonaSQLRegistrator;
import org.apache.spark.sql.SparkSession;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Slf4j
public class BaseMain implements Serializable {

    private static final String REDIS_PARAMS_DB = System.getProperty("engine.spark.redis.params.db", "0");

    /**
     * 获取SparkSession
     *
     * @param appName 应用名称
     * @return SparkSession
     */
    public static SparkSession getSession(String appName) {
        SparkSession session = getSession(appName, false);
        // 注册经纬度相关函数
        SedonaSQLRegistrator.registerAll(session);
        return session;
    }

    /**
     * 获取SparkSession
     *
     * @param appName 应用名称
     * @param fromFile 是否从文件中读取配置
     * @return SparkSession
     */
    public static SparkSession getSession(String appName, boolean fromFile) {
        if (fromFile) {
            return getSessionInMap(appName);
        }
        return getSessionInNacos(appName);
    }

    private static SparkSession getSessionInNacos(String appName) {
        return SparkContextHelper.getSparkSession(
            appName,
            new ConfigInNacos("spark.properties"),
            RunMode.DEVELOPMENT,
            Optional.empty()
        );
    }

    private static SparkSession getSessionInMap(String appName) {
        return SparkContextHelper.getSparkSession(appName,
            new ConfigInMap(getProperties()),
            RunMode.DEVELOPMENT,
            Optional.empty()
        );
    }

    /**
     * 从文件中读取配置
     *
     * @return 配置
     */
    public static Map<String, Object> getProperties() {
        Properties properties = new Properties();
        try {
            InputStream in = BaseMain.class.getClassLoader().getResourceAsStream("spark.properties");
            properties.load(in);
        } catch (IOException e) {
            log.error("获取文件异常", e);
        }
        Map<String, Object> map = new HashMap<>(properties.size());
        properties.forEach((key, value) -> map.put(key.toString(), value));
        return map;
    }

    /**
     * 从Redis中获取数据
     *
     * @param key key
     * @return 数据
     * @throws ServiceException 异常
     */
    public static String getData(String key) throws ServiceException {
        log.info("请求key为{}", key);
        PreConditionCheck.checkNotEmpty(key, "请求参数key不能为空");
        String data = RedisUtils.getInstance().getByKey(key, Integer.valueOf(REDIS_PARAMS_DB));
        log.info("请求参数data为{}", data);
        if (StringUtils.isEmpty(data)) {
            log.warn("根据key={}获取请求参数为空", key);
            throw new ServiceException("根据key=" + key + "获取请求参数为空");
        }
        return data;
    }
}
