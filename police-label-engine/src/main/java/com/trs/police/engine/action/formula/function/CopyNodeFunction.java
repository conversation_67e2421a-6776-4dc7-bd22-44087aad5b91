package com.trs.police.engine.action.formula.function;


import com.trs.police.engine.action.formula.NodeFunction;
import org.apache.spark.sql.Column;

import java.util.List;

/**
 * 复制
 *
 * <AUTHOR>
 */
public class CopyNodeFunction implements NodeFunction {

    public static final CopyNodeFunction INSTANCE = new CopyNodeFunction();

    @Override
    public Column execute(List<Column> parameters) {
        return parameters.get(0);
    }

    @Override
    public String key() {
        return "copy";
    }
}
