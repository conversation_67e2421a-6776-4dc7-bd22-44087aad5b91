package com.trs.police.engine.dto.node.properties;

import com.trs.police.engine.dto.node.ValueMateBase;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 关联配置类
 */
@Data
@NoArgsConstructor
public class RelevancyNodeProperties extends NodeProperties {

    /**
     * 来源表格A
     */
    private String tableA;

    /**
     * 来源表格B
     */
    private String tableB;

    /**
     * 关联类型：1 交集 2 并集（不去重） 3 并集（去重）4 差集
     */
    private Integer type;

    /**
     * 匹配字段配置
     */
    private List<MatchConfig> match;

    /**
     * 权重设置：1 选a 2 选b 3 执行比较取大 4 执行比大取消
     */
    private Integer weight;

    /**
     * 比较字段配置
     */
    private List<CompareFieldConfig> compareField;

    /**
     * Match字段配置项
     */
    @Data
    public static class MatchConfig {

        /**
         * 表A字段信息
         */
        private ValueMateBase a;

        /**
         * 表B字段信息
         */
        private ValueMateBase b;

        /**
         * 输出字段信息
         */
        private ValueMateBase out;

        /**
         * 比较类型 等于 eq 不等于 neq 大于 gt 大于等于 gte 小于 le 小于等于 lte
         */
        private String matchType;
    }

    /**
     * Compare字段配置项
     */
    @Data
    public static class CompareFieldConfig {

        /**
         * 表A字段信息
         */
        private ValueMateBase a;

        /**
         * 表B字段信息
         */
        private ValueMateBase b;
    }
}
