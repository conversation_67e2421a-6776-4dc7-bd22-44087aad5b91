package com.trs.police.engine.action;

import com.trs.police.engine.dto.node.properties.NodeProperties;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.util.List;

/**
 * 标签多节点计算
 *
 */
public class LabelMultiComputeNode {

    private final List<BaseLabelNode<? extends NodeProperties>> labelNodes;

    public LabelMultiComputeNode(List<BaseLabelNode<? extends NodeProperties>> labelNodes) {
        this.labelNodes = labelNodes;
    }

    /**
     * 执行多节点计算
     *
     * @param spark spark
     * @param inData 输入数据
     * @param context 标签节点上下文
     * @return Dataset
     */
    public Dataset<Row> process(SparkSession spark, Dataset<Row> inData, LabelNodeContext context) {
        for (BaseLabelNode<? extends NodeProperties> labelNode : labelNodes) {
            inData = labelNode.process(spark, inData, context);
        }
        return inData;
    }
}
