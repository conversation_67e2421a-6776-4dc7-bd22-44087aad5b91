package com.trs.police.engine.vo;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.pojo.BaseVO;
import com.trs.common.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/26 20:14
 * @since 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RowFieldVo extends BaseVO {

    private String name;
    private String targetName;

    /**
     * of<BR>
     *
     * @param name 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/26 20:20
     */
    public static RowFieldVo of(String name) {
        return of(name, name);
    }

    /**
     * of<BR>
     *
     * @param name       参数
     * @param targetName 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/26 20:20
     */
    public static RowFieldVo of(String name, String targetName) {
        PreConditionCheck.checkNotEmpty(name, "name不能为空");
        RowFieldVo vo = new RowFieldVo();
        vo.setName(name);
        vo.setTargetName(StringUtils.showEmpty(targetName, name));
        return vo;
    }
}
