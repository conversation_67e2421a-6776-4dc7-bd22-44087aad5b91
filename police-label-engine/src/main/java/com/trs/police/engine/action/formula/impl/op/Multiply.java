package com.trs.police.engine.action.formula.impl.op;


import com.trs.police.engine.action.formula.Operator;
import com.trs.police.engine.constant.DataBaseFieldMappingType;
import com.trs.police.engine.vo.FieldValue;

import java.math.BigDecimal;

import static com.trs.police.engine.constant.NodeConstants.NUMBER;


/**
 * 乘法
 *
 * <AUTHOR>
 */
public class Multiply implements Operator {

    public static final Multiply INSTANCE = new Multiply();

    private Multiply() {
    }

    @Override
    public FieldValue calculate(FieldValue left, FieldValue right) {
        if (!DataBaseFieldMappingType.NUMBER.getFieldType().equals(left.getTypeCode())
                || !DataBaseFieldMappingType.NUMBER.getFieldType().equals(right.getTypeCode())) {
            throw new RuntimeException("乘法运算要求两个参数都是数字");
        }
        Double v = BigDecimal.valueOf(Double.valueOf(left.getValue()))
                .multiply(BigDecimal.valueOf(Double.valueOf(right.getValue())))
                .doubleValue();
        return new FieldValue(v.toString(), NUMBER);
    }
}
