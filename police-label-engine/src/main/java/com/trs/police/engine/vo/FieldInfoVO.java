package com.trs.police.engine.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FieldInfoVO {

    /**
     * 列索引
     */
    @Deprecated
    private Integer colIndex;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 字段类型 类型编码 {@link DataBaseFieldMappingType#getFieldType()}
     */
    private String typeCode;

    /**
     * 来源节点 uuid
     */
    private String fromNode;

    public FieldInfoVO(String cnName, String enName, String typeCode, String fromNode) {
        this.cnName = cnName;
        this.enName = enName;
        this.typeCode = typeCode;
        this.fromNode = fromNode;
    }

    /**
     * 唯一值
     *
     * @return 唯一值id
     */
    public String getId() {
        return String.format("%s.%s", fromNode, enName);
    }

    /**
     * 唯一值
     *
     * @return 唯一值id
     */
    public String getColId() {
        return "`"+String.format("%s.%s", fromNode, enName)+"`";
    }

    /**
     * 拷贝
     *
     * @return 拷贝对象
     */
    public FieldInfoVO copy() {
        return new FieldInfoVO(cnName, enName, typeCode, fromNode);
    }
}
