package com.trs.police.engine.action.impl;

import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.node.properties.OrderNodeProperties;
import com.trs.police.engine.dto.node.properties.bean.OrderItem;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

/**
 * Spark排序节点
 *
 * <AUTHOR>
 */
public class LabelOrderNode extends BaseLabelNode<OrderNodeProperties> {

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> dataset, LabelNodeContext context) {
        // 应用排序
        return applySparkSort(dataset, getNodeProperties());
    }

    @Override
    public Integer nodeType() {
        return NodeType.ORDER;
    }

    /**
     * 应用Spark排序
     *
     * @param dataset  数据集
     * @param property 排序属性
     * @return 排序后的数据集
     */
    private Dataset<Row> applySparkSort(Dataset<Row> dataset, OrderNodeProperties property) {
        if (property.getOrderItems() == null || property.getOrderItems().isEmpty()) {
            return dataset;
        }

        // 构建排序表达式
        org.apache.spark.sql.Column[] sortColumns = property.getOrderItems().stream()
                .map(this::buildSortColumn)
                .toArray(org.apache.spark.sql.Column[]::new);

        return dataset.orderBy(sortColumns);
    }

    /**
     * 构建排序列
     *
     * @param orderItem 排序项
     * @return Spark排序列
     */
    private org.apache.spark.sql.Column buildSortColumn(OrderItem orderItem) {
        org.apache.spark.sql.Column column = org.apache.spark.sql.functions.col(orderItem.getEnName());
        // 根据排序方向设置升序或降序，与原始OrderNode逻辑保持一致
        if ("desc".equalsIgnoreCase(orderItem.getOrder())) {
            return column.desc();
        } else {
            // 默认升序，与原始逻辑一致
            return column.asc();
        }
    }
}
