package com.trs.police.engine.utils;

import com.trs.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.HostAndPort;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <AUTHOR>
 */
@Slf4j
public class RedisUtils {

    public static final String REDIS_TYPE_SENTINEL = "sentinel";
    public static final String REDIS_TYPE_CLUSTER = "cluster";
    public static final String REDIS_TYPE_JEDIS = "jedis";
    public static final String REDIS_TYPE_LETTUCE = "lettuce";

    public static final String REDIS_URL = System.getProperty("redis.host", "************");
    public static final String REDIS_PORT = System.getProperty("redis.port", "22400");
    public static final String REDIS_PASSWORD = System.getProperty("redis.pwd", null);
    public static final String REDIS_MASTER_NAME = System.getProperty("redis.masterName");

    private static final RedisUtils REDIS_UTILS = init();

    /**
     * makeNodes<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/23 15:47
     */

    protected Set<HostAndPort> makeNodes() {
        String nodes = System.getProperty("redis.nodes");
        if (StringUtils.isEmpty(nodes)) {
            if (StringUtils.isNotEmpty(REDIS_URL) && StringUtils.isNotEmpty(REDIS_PORT)) {
                nodes = String.format("%s:%s", REDIS_URL, REDIS_PORT);
            }
        }
        checkNotEmpty(nodes, "redis.nodes或redis.host，redis.port不能同时为空");
        return Arrays.stream(nodes.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
            .map(it -> it.split(":", 2))
            .map(it -> new HostAndPort(it[0], Integer.parseInt(it[1])))
            .collect(Collectors.toSet());
    }

    protected RedisUtils() {
    }

    private static RedisUtils init() {
        String type = System.getProperty("redis.type", REDIS_TYPE_CLUSTER);
        if (REDIS_TYPE_CLUSTER.equals(type)) {
            return new RedisClusterUtils();
        } else if (Objects.equals(REDIS_TYPE_JEDIS, type)) {
            return new JedisUtils();
        } else if (Objects.equals(REDIS_TYPE_LETTUCE, type)) {
            return new LettuceRedisUtils();
        } else {
            return new RedisNormalUtils(type);
        }
    }

    /**
     * 获取RedisUtils实例
     *
     * @return RedisUtils
     */
    public static RedisUtils getInstance() {
        return REDIS_UTILS;
    }

    /**
     * 根据key获取value
     *
     * @param key   key
     * @param index index
     * @return value
     */
    public String getByKey(String key, Integer index) {
        return null;
    }

    /**
     * 根据key获取value
     *
     * @param key key
     * @return value
     */
    public String getByKey(String key) {
        return getByKey(key, 0);
    }
}
