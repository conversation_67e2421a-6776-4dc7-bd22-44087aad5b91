package com.trs.police.engine.action.impl;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.node.properties.LabelOutputProperties;
import com.trs.police.engine.vo.RowFieldVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.*;
import org.apache.spark.sql.types.DataTypes;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Spark去重节点
 *
 * <AUTHOR>
 */
@Slf4j
public class LabelOutputNode extends BaseLabelNode<LabelOutputProperties> {

    @Override
    public Dataset<Row> process(SparkSession spark, Dataset<Row> inData, LabelNodeContext context) {
        return doProcess(spark, inData, context);
    }

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> dataset, LabelNodeContext context) {
        LabelOutputProperties property = getNodeProperties();
        List<String> idCols = context.getIdCols().stream().map(RowFieldVo::getName).collect(Collectors.toList());
        List<Column> aggRemainColumn = new ArrayList<>();
        // 聚合每个表的id
        Column aggFirstColumn =functions.array_distinct(functions.flatten(functions.collect_list(idCols.get(0)))).alias(idCols.get(0));
        if(context.getIdCols().size()>1){
            for (int i = 1; i < idCols.size(); i++) {
                aggRemainColumn.add(functions.array_distinct(functions.flatten(functions.collect_list(idCols.get(i)))).alias(idCols.get(i)));
            }
        }
        // 聚合客体
        String relatedObjectNumberCol = property.getRelatedObjectNumberCol();
        if(StringUtils.isNotEmpty(relatedObjectNumberCol)){
            aggRemainColumn.add(functions.collect_set(relatedObjectNumberCol.contains(".")?relatedObjectNumberCol:"`"+relatedObjectNumberCol+"`")
                    .alias("related_object_number"));
        }
        // 聚焦上一个节点的输出字段，排除主体字段
        List<RowFieldVo> outputRow = getOutputRow();
        Column[] remainColumn = outputRow.stream()
                .filter(rowFieldVo -> !property.getObjectNumberCol().equals("`"+rowFieldVo.getName()+"`"))
                .map(rowFieldVo -> functions.col("`"+rowFieldVo.getName()+"`").alias(rowFieldVo.getTargetName()))
                .toArray(Column[]::new);
        aggRemainColumn.add(functions.collect_list(functions.struct(remainColumn)).as("extra_info"));
        if(aggRemainColumn.isEmpty()){
            dataset = dataset.groupBy(property.getObjectNumberCol())
                    .agg(aggFirstColumn);
        }else {
            dataset = dataset.groupBy(property.getObjectNumberCol())
                    .agg(aggFirstColumn, aggRemainColumn.stream().toArray(Column[]::new));
        }
        if(StringUtils.isEmpty(relatedObjectNumberCol)){
            dataset = dataset.withColumn("related_object_number", functions.lit(null).cast(DataTypes.createArrayType(DataTypes.StringType)));
        }

        // 计算某个表的的id个数最大值
        Column[] idNameSizeColumns = idCols.stream().map(col -> functions.size(functions.col(col))).toArray(Column[]::new);
        if (idNameSizeColumns.length==1){
            dataset = dataset.withColumn("frequency", idNameSizeColumns[0]);
        }else {
            dataset = dataset.withColumn("frequency", functions.greatest(idNameSizeColumns));
        }

        // 合并每个表的id为以下格式
        //[
        //    {
        //        "source_table": "trajectory_night_act",
        //        "table_id": 12,
        //        "ids": [
        //            "1231231",
        //            "1231423"
        //        ]
        //    }
        //]
        Column[] idColArr = idCols.stream().map(idCol-> {
            String tableId = idCol.substring(0, idCol.indexOf("__"));
            String strRemovedTableId = idCol.substring(tableId.length() + 2);
            String tableName = strRemovedTableId.substring(0, strRemovedTableId.indexOf("__"));
            return functions.struct(
                    functions.lit(tableName).as("source_table"),
                    functions.lit(Long.valueOf(tableId)).as("table_id"),
                    functions.col(idCol).as("ids"));
        }).toArray(Column[]::new);

        dataset = dataset.select(
                functions.col(property.getObjectNumberCol()),
                functions.array(idColArr).as("source_data_ids"),
                functions.col("frequency"),
                functions.col("related_object_number"),
                functions.to_json(functions.col("extra_info")).alias("extra_info")
        );

        // 补充其他标签属性
        String currentTime = TimeUtils.dateToString(new Date(), TimeUtils.YYYYMMDD_HHMMSS);
        dataset = dataset
                .withColumn("object_number", functions.col(property.getObjectNumberCol()))
                .withColumn("object_type", functions.lit(property.getObjectType()).cast("string"))
                .withColumn("category", functions.lit(property.getCategoryCode()).cast("string"))
                .withColumn("police_kind", functions.lit(property.getPoliceKind()).cast("string"))
                .withColumn("label_name", functions.lit(property.getName()))
                .withColumn("expire_time", functions.lit(property.getExpireTime()))
                .withColumn("create_time", functions.lit(currentTime))
                .withColumn("update_time", functions.lit(currentTime))
                .withColumn("source_table", functions.lit(idCols.stream().collect(Collectors.joining(","))))
                .withColumn("uuid", functions.md5(functions.concat_ws("+",
                        functions.col("label_name"),
                        functions.col("police_kind"),
                        functions.col("object_number"))));

        return dataset;
    }

    @Override
    public Integer nodeType() {
        return NodeType.LABEL_OUTPUT;
    }

}
