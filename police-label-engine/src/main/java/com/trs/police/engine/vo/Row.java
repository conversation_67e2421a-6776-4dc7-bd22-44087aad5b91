package com.trs.police.engine.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * 行
 */
@Data
@NoArgsConstructor
public class Row {

    private List<FieldValue> rowData;

    public Row(List<FieldValue> rowData) {
        this.rowData = rowData;
    }

    /**
     * 获取字段值
     *
     * @param fieldInfoVO 字段信息
     * @return 字段值
     */
    public Optional<FieldValue> getValue(FieldInfoVO fieldInfoVO) {
        return rowData
                .stream()
                .filter(v -> v.getId().equals(fieldInfoVO.getId()))
                .findAny();
    }
}
