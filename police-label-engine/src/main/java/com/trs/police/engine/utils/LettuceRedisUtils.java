package com.trs.police.engine.utils;

import com.trs.common.utils.StringUtils;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.sync.RedisCommands;
import redis.clients.jedis.HostAndPort;

import java.util.NoSuchElementException;

/**
 * LettuceRedisUtils
 *
 */
public class LettuceRedisUtils extends RedisUtils {

    private final HostAndPort info;
    private final RedisClient redisClient;

    public LettuceRedisUtils() {
        this.info = makeNodes()
            .stream()
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("redis.nodes或redis.host，redis.port不能同时为空"));

        RedisURI.Builder builder = RedisURI.builder().withHost(info.getHost()).withPort(info.getPort());
        if (StringUtils.isNotEmpty(REDIS_PASSWORD)) {
            builder.withPassword(REDIS_PASSWORD.toCharArray());
        }
        this.redisClient = RedisClient.create(builder.build());
    }

    private StatefulRedisConnection<String, String> connect() {
        return redisClient.connect();
    }

    @Override
    public String getByKey(String key, Integer index) {
        try (StatefulRedisConnection<String, String> connection = connect()) {
            RedisCommands<String, String> conn = connection.sync();
            conn.select(index);
            return conn.get(key);
        }
    }

}
