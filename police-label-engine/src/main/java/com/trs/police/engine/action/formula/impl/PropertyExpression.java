package com.trs.police.engine.action.formula.impl;


import com.trs.police.engine.action.formula.Expression;
import com.trs.police.engine.action.formula.FormulaContext;
import com.trs.police.engine.vo.FieldInfoVO;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

import java.util.Map;

/**
 * 输入字段
 *
 * <AUTHOR>
 */
public class PropertyExpression implements Expression {

    private final FieldInfoVO fieldInfo;

    /**
     * 缓存hash值
     */
    private String cacheUuid;

    /**
     * 缓存字段索引
     */
    private Map<String, Integer> cache;

    public PropertyExpression(FieldInfoVO fieldInfo) {
        this.fieldInfo = fieldInfo;
    }

    @Override
    public Column evaluate(FormulaContext context) {
        return functions.col(fieldInfo.getColId());
    }
}
