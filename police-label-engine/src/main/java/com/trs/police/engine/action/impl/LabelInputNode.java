package com.trs.police.engine.action.impl;

import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.node.properties.LabelInputProperties;
import com.trs.police.engine.vo.RowFieldVo;
import com.trs.spark.action.impl.from.BaseSourceReadAction;
import com.trs.spark.action.impl.from.HiveReadAction;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;

import java.util.stream.Collectors;

/**
 * Spark排序节点
 *
 * <AUTHOR>
 */
public class LabelInputNode extends BaseLabelNode<LabelInputProperties> {

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> inData, LabelNodeContext labelNodeContext) {
        LabelInputProperties property = getNodeProperties();
        // 重命名id字段为：表名_id列名
        String newIdCol =  property.getTableId() + "__" + property.getTable() + "__" + property.getIdCol();
        RowFieldVo rowFieldVo = RowFieldVo.of(newIdCol, newIdCol);
        if (!labelNodeContext.getIdCols().contains(rowFieldVo)) {
            labelNodeContext.getIdCols().add(rowFieldVo);
        }
        BaseSourceReadAction<Row> rowBaseSourceReadAction = buildReadAction(property.getIsHive());
        return rowBaseSourceReadAction.getDataSet(spark).withColumn(newIdCol, functions.array(property.getIdCol()));
    }

    protected BaseSourceReadAction<Row> buildReadAction(Boolean isHave) {
        LabelInputProperties property = getNodeProperties();
        getOutputRow().add(RowFieldVo.of(property.getIdCol(), property.getIdCol()));
        String select = getOutputRow().stream()
                .map(RowFieldVo::getName)
                .collect(Collectors.joining(","));
        if (isHave) {
            return new HiveReadAction<>(
                    property.getTable(),
                    property.getWhere(),
                    select,
                    null,
                    r -> r
            );
        } else {
            return new BaseSourceReadAction<>(
                    property.getUrl(),
                    property.getTable(),
                    property.getUsername(),
                    property.getPassword(),
                    property.getWhere(),
                    select,
                    r -> r
            );
        }
    }

    @Override
    public Integer nodeType() {
        return NodeType.LABEL_INPUT;
    }
}
