package com.trs.police.engine.service;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.common.utils.StringUtils;
import com.trs.spark.action.impl.peek.BaseSourceWriteAction;
import com.trs.spark.action.impl.peek.HiveWriteAction;
import com.trs.spark.action.impl.peek.KafkaPeekAction;
import com.trs.spark.function.CheckedFunction;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Objects;

/**
 * 基础服务
 *
 * @param <D>             请求参数
 * @param <IN>            输入数据类型
 * @param <ConvertResult> 转换结果类型
 * @param <SAVE>          保存数据类型
 */
@Slf4j
public abstract class BaseService<D extends BaseDTO, IN extends Serializable, ConvertResult extends Serializable, SAVE extends Serializable>
        implements Serializable {

    @Getter
    @Setter
    private boolean openLog;

    public BaseService() {
        this("true".equals(System.getProperty("isDebug", "false")));
    }

    public BaseService(boolean openLog) {
        this.openLog = openLog;
    }

    protected String getOutputUrl() {
        return System.getProperty("output.databases.url", "es://10.18.20.131:9200?es.net.ssl=false");

    }

    protected String getOutputUsername() {
        return System.getProperty("output.databases.username", "");
    }

    protected String getOutputPassword() {
        return System.getProperty("output.databases.password", "");
    }

    protected String getKafkaUrl() {
        return System.getProperty("output.kafka.url", "");
    }

    protected String getKafkaTopic() {
        return System.getProperty("output.kafka.topic", "");
    }

    protected boolean kafkaEnableKerberos() {
        return Objects.equals("true", System.getProperty("output.kafka.enableKerberos"));
    }

    /**
     * buildKafkaPeekAction<BR>
     *
     * @param key         参数
     * @param messageType 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/3 10:02
     */
    protected KafkaPeekAction<String> buildKafkaPeekAction(String key, String messageType) {
        HashMap<String, Object> configMap = new HashMap<>(3);
        configMap.put("bootstrap.servers", getKafkaUrl());
        configMap.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        configMap.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        if (kafkaEnableKerberos()) {
            configMap.put("security.protocol", "SASL_PLAINTEXT");
            configMap.put("sasl.kerberos.service.name", "kafka");
            configMap.put(
                    "kerberos.domain.name",
                    StringUtils.showEmpty(System.getProperty("output.kafka.kerberosDomain"), "hadoop.hadoop.com")
            );
        }
        return new KafkaPeekAction<>(
                configMap,
                getKafkaTopic(),
                key,
                messageType
        );
    }

    protected String getLocalAreaCodePrefix() {
        return System.getProperty("local.area.code.prefix", "5105");
    }

    /**
     * 运行任务<BR>
     *
     * @param session SparkSession
     * @param data    输入的内容
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/24 16:14
     */
    public abstract String runJob(SparkSession session, String data) throws ServiceException;

    /**
     * 解析数据为相关对象<BR>
     *
     * @param data 输入的内容
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/24 16:14
     */
    public D parseInputData(String data) throws ServiceException {
        return Try.of(() -> {
                    String reqData = data;
                    if (reqData.startsWith("\"")) {
                        reqData = reqData.substring(1, reqData.length() - 1)
                                .replaceAll("\\\\\"", "\"")
                                .replaceAll("\\\\\"", "\"");
                    }
                    String req = reqData.startsWith("{") ? reqData : URLDecoder.decode(reqData, "UTF-8");
                    return JSONObject.parseObject(req,
                            (Class<D>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0]);
                }).filterTry(BaseDTO::isValid)
                .getOrElseThrow(err -> new ServiceException("任务执行参数: \n"+data+"\n输入数据转换异常:" + err.getMessage(), err));
    }

    /**
     * 构造保存数据action<BR>
     *
     * @param dto 请求参数
     * @return 保存数据action
     * <AUTHOR> lan.xin E-mail: <EMAIL> 创建时间：2023/3/23 15:54
     */
    protected BaseSourceWriteAction<ConvertResult, SAVE> buildWriteAction(D dto) {
        log.info("OUTPUT_URL=[{}],OUTPUT_USERNAME=[{}],OUTPUT_PASSWORD=[{}]", getOutputUrl(), getOutputUsername(),
                getOutputPassword());
        if (isUseHive() && StringUtils.isEmpty(getOutputUrl())) {
            return new HiveWriteAction<>(
                    getOutputTableName(),
                    SaveMode.Append,
                    saveConvertor(dto),
                    (Class<SAVE>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[3]);
        } else {
            SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver(getOutputUrl(), getOutputUsername(),
                            getOutputPassword())
                    .get();
            return new BaseSourceWriteAction<>(
                    sourceDriver,
                    getOutputTableName(),
                    SaveMode.Append,
                    saveConvertor(dto),
                    (Class<SAVE>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[3]
            );
        }
    }

    /**
     * getOutputTableName<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/9/13 19:55
     */
    protected String getOutputTableName() {
        String databaseName = System.getProperty("output.database", "");
        return (StringUtils.isEmpty(databaseName) ? "" : databaseName + ".") + outputTableName();
    }

    protected String outputTableName() {
        throw new RuntimeException("请重写该方法!");
    }

    /**
     * saveConvertor<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/9/13 19:55
     */
    protected CheckedFunction<ConvertResult, SAVE> saveConvertor(D dto) {
        return item -> (SAVE) item;
    }

    protected boolean isUseHive() {
        return "hive".equalsIgnoreCase(System.getProperty("read.Type", "iceberg"));
    }
}
