package com.trs.police.engine.action.formula.function;


import com.trs.police.engine.action.formula.NodeFunction;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

import java.util.List;

/**
 * 绝对值
 *
 * <AUTHOR>
 */
public class AbsNodeFunction implements NodeFunction {

    public static final AbsNodeFunction INSTANCE = new AbsNodeFunction();

    @Override
    public Column execute(List<Column> parameters) {
        return functions.abs(parameters.get(0));
    }

    @Override
    public String key() {
        return "abs";
    }
}
