package com.trs.police.engine.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class LabelNodeDTO implements Serializable {
    /**
     * 节点类型
     */
    private final Integer nodeType;
    /**
     * 节点nodeProperties转json
     */
    private final String nodeProperties;
    /**
     * 输出的字段
     */
    private final List<RowFieldVo> outputRow;

    /**
     * flow
     */
    private List<LabelNodeDTO> flow;

    /**
     * 上一个节点id
     */
    private String lastNodeId;
}
