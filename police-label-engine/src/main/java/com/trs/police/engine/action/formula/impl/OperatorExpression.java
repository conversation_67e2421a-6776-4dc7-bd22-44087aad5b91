package com.trs.police.engine.action.formula.impl;


import com.trs.police.engine.action.formula.Expression;
import com.trs.police.engine.action.formula.FormulaContext;
import com.trs.police.engine.constant.Operator;
import org.apache.spark.sql.Column;

/**
 * 操作符
 *
 * <AUTHOR>
 */
public class OperatorExpression implements Expression {
    private final Operator operator;

    public OperatorExpression(Operator operator) {
        this.operator = operator;
    }

    /**
     * 获取操作符
     *
     * @return 操作符
     */
    public Operator getOperator() {
        return operator;
    }

    @Override
    public Column evaluate(FormulaContext context) {
        // 运算符本身不直接计算结果，它需要与左右操作数结合
        throw new UnsupportedOperationException("Operator cannot be evaluated alone");
    }
}
