package com.trs.police.engine.action.impl;

import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.action.formula.FormulaContext;
import com.trs.police.engine.action.formula.FormulaParser;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.node.properties.NewFieldProperties;
import com.trs.police.engine.dto.node.properties.bean.NewField;
import org.apache.spark.sql.*;

/**
 * Spark新字段节点
 *
 * <AUTHOR>
 */
public class LabelNewFieldNode extends BaseLabelNode<NewFieldProperties> {

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> dataset, LabelNodeContext context) {
        // 应用新字段操作
        return applySparkNewFields(dataset, getNodeProperties());
    }

    @Override
    public Integer nodeType() {
        return NodeType.NEW_FIELD;
    }

    /**
     * 应用Spark新字段操作
     *
     * @param dataset  数据集
     * @param property 新字段属性
     * @return 添加新字段后的数据集
     */
    private Dataset<Row> applySparkNewFields(Dataset<Row> dataset, NewFieldProperties property) {
        if (property.getOutValue() == null || property.getOutValue().isEmpty()) {
            return dataset;
        }

        Dataset<Row> result = dataset;

        // 为每个新字段添加列
        for (NewField newField : property.getOutValue()) {
            result = addNewField(result, newField);
        }

        return result;
    }

    /**
     * 添加新字段
     *
     * @param dataset  数据集
     * @param newField 新字段定义
     * @return 添加字段后的数据集
     */
    private Dataset<Row> addNewField(Dataset<Row> dataset, NewField newField) {
        String fieldName = newField.getFromNode()+"."+newField.getEnName();
        String string = newField.getValue();
        try {
            Column column = FormulaParser.parse(string)
                    .evaluate(new FormulaContext(null, null));
            return dataset.withColumn(fieldName, column);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
