package com.trs.police.engine.action.impl;

import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.constant.DataBaseFieldMappingType;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.node.properties.ConvertNodeProperties;
import com.trs.police.engine.dto.node.properties.bean.ConvertItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;
import org.apache.spark.sql.types.DataTypes;

/**
 * Spark转换节点
 *
 * <AUTHOR>
 */
@Slf4j
public class LabelConvertNode extends BaseLabelNode<ConvertNodeProperties> {

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> dataset, LabelNodeContext labelNodeContext) {
        // 应用转换操作 - 按照原本ConvertNode的逻辑
        return applySparkConvertWithOriginalLogic(dataset, getNodeProperties());
    }

    @Override
    public Integer nodeType() {
        return NodeType.TRANSFORM;
    }

    /**
     * 按照原本ConvertNode的逻辑应用Spark转换操作
     *
     * @param dataset  数据集
     * @param property 转换属性
     * @return 转换后的数据集
     */
    private Dataset<Row> applySparkConvertWithOriginalLogic(Dataset<Row> dataset, ConvertNodeProperties property) {
        if (property.getConvertField() == null || property.getConvertField().isEmpty()) {
            return dataset;
        }

        Dataset<Row> result = dataset;

        // 应用每个转换规则 - 按照原本ConvertNode的逻辑
        for (ConvertItem convertItem : property.getConvertField()) {
            result = applyConvertItemWithOriginalLogic(result, convertItem);
        }

        return result;
    }

    /**
     * 按照原本ConvertNode的逻辑应用单个转换项
     *
     * @param dataset     数据集
     * @param convertItem 转换项
     * @return 转换后的数据集
     */
    private Dataset<Row> applyConvertItemWithOriginalLogic(Dataset<Row> dataset, ConvertItem convertItem) {
        String fromField = convertItem.getFrom().getCol();
        String toField = convertItem.getTo().getCol();
        String fromTypeCode = convertItem.getFrom().getTypeCode();
        String toTypeCode = convertItem.getTo().getTypeCode();

        // 如果类型相同，直接重命名字段
        if (fromTypeCode.equalsIgnoreCase(toTypeCode)) {
            return dataset.withColumnRenamed(fromField, toField);
        }

        // 根据目标类型执行转换 - 完全按照原本ConvertNode的逻辑
        DataBaseFieldMappingType targetType = DataBaseFieldMappingType.getType(toTypeCode);
        DataBaseFieldMappingType sourceType = DataBaseFieldMappingType.getType(fromTypeCode);

        if (targetType == null || sourceType == null) {
            log.warn("不支持的类型转换: {} -> {}", fromTypeCode, toTypeCode);
            return dataset.withColumn(toField, functions.col(fromField));
        }

        return applyTypeConversion(dataset, fromField, toField, sourceType, targetType);
    }

    /**
     * 应用类型转换 - 完全按照原本ConvertNode的逻辑
     *
     * @param dataset    数据集
     * @param fromField  源字段
     * @param toField    目标字段
     * @param sourceType 源类型
     * @param targetType 目标类型
     * @return 转换后的数据集
     */
    private Dataset<Row> applyTypeConversion(Dataset<Row> dataset, String fromField, String toField,
                                             DataBaseFieldMappingType sourceType, DataBaseFieldMappingType targetType) {
        switch (targetType) {
            case STRING:
                // 转换为字符串 - 直接转换
                return dataset.withColumn(toField, functions.col(fromField).cast(DataTypes.StringType));

            case NUMBER:
                return applyNumberConversion(dataset, fromField, toField, sourceType);

            case DATETIME:
                return applyDateTimeConversion(dataset, fromField, toField, sourceType);

            case BOOLEAN:
                return applyBooleanConversion(dataset, fromField, toField, sourceType);

            default:
                log.warn("不支持的目标类型: {}", targetType);
                return dataset.withColumn(toField, functions.col(fromField));
        }
    }

    /**
     * 应用数字类型转换
     *
     * @param dataset    数据集
     * @param fromField  源字段
     * @param toField    目标字段
     * @param sourceType 源类型
     * @return 转换后的数据集
     */
    private Dataset<Row> applyNumberConversion(Dataset<Row> dataset, String fromField, String toField,
                                               DataBaseFieldMappingType sourceType) {
        switch (sourceType) {
            case NUMBER:
                // 数字到数字，直接转换
                return dataset.withColumn(toField, functions.col(fromField).cast(DataTypes.DoubleType));

            case STRING:
                // 字符串到数字，需要处理转换异常
                return dataset.withColumn(toField,
                        functions.when(functions.col(fromField).isNull().or(functions.col(fromField).equalTo("")),
                                        functions.lit(null))
                                .otherwise(
                                        functions.when(functions.col(fromField).rlike("^-?\\d+(\\.\\d+)?$"),
                                                        functions.col(fromField).cast(DataTypes.DoubleType))
                                                .otherwise(functions.lit(null))
                                ));

            case DATETIME:
                // 日期到数字（时间戳）
                return dataset.withColumn(toField,
                        functions.when(functions.col(fromField).isNull().or(functions.col(fromField).equalTo("")),
                                        functions.lit(null))
                                .otherwise(functions.unix_timestamp(functions.col(fromField))));

            default:
                log.warn("不支持的源类型到数字的转换: {}", sourceType);
                return dataset.withColumn(toField, functions.lit(null));
        }
    }

    /**
     * 应用日期时间类型转换
     *
     * @param dataset    数据集
     * @param fromField  源字段
     * @param toField    目标字段
     * @param sourceType 源类型
     * @return 转换后的数据集
     */
    private Dataset<Row> applyDateTimeConversion(Dataset<Row> dataset, String fromField, String toField,
                                                 DataBaseFieldMappingType sourceType) {
        switch (sourceType) {
            case NUMBER:
                // 数字（时间戳）到日期
                return dataset.withColumn(toField,
                        functions.when(functions.col(fromField).isNull().or(functions.col(fromField).equalTo("")),
                                        functions.lit(null))
                                .otherwise(functions.from_unixtime(functions.col(fromField).cast(DataTypes.LongType),
                                        "yyyy-MM-dd HH:mm:ss")));

            case STRING:
                // 字符串到日期
                return dataset.withColumn(toField,
                        functions.when(functions.col(fromField).isNull().or(functions.col(fromField).equalTo("")),
                                        functions.lit(null))
                                .otherwise(functions.col(fromField))); // 保持原字符串格式

            case DATETIME:
                // 日期到日期，直接复制
                return dataset.withColumn(toField, functions.col(fromField));

            default:
                log.warn("不支持的源类型到日期的转换: {}", sourceType);
                return dataset.withColumn(toField, functions.lit(null));
        }
    }

    /**
     * 应用布尔类型转换
     *
     * @param dataset    数据集
     * @param fromField  源字段
     * @param toField    目标字段
     * @param sourceType 源类型
     * @return 转换后的数据集
     */
    private Dataset<Row> applyBooleanConversion(Dataset<Row> dataset, String fromField, String toField,
                                                DataBaseFieldMappingType sourceType) {
        switch (sourceType) {
            case BOOLEAN:
                // 布尔到布尔，直接复制
                return dataset.withColumn(toField, functions.col(fromField));

            case STRING:
                // 字符串到布尔
                return dataset.withColumn(toField,
                        functions.when(functions.lower(functions.col(fromField)).equalTo("true"), functions.lit("true"))
                                .otherwise(functions.lit("false")));

            case NUMBER:
                // 数字到布尔
                return dataset.withColumn(toField,
                        functions.when(functions.col(fromField).equalTo(1), functions.lit("true"))
                                .otherwise(functions.lit("false")));

            default:
                log.warn("不支持的源类型到布尔的转换: {}", sourceType);
                return dataset.withColumn(toField, functions.lit("false"));
        }
    }
}
