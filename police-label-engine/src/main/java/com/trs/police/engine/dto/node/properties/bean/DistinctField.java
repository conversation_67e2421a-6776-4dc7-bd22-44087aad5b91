package com.trs.police.engine.dto.node.properties.bean;

import com.trs.police.engine.dto.node.ValueMateBase;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 去重字段
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DistinctField extends ValueMateBase {

    /**
     * 去重类型
     */
    private Integer distinctType;

    /**
     * 获取字段信息
     *
     * @return 字段信息
     */
    public ValueMateBase getFiled() {
        return this;
    }

}
