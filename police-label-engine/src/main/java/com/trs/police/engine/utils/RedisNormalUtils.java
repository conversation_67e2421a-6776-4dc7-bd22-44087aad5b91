package com.trs.police.engine.utils;

import com.trs.common.utils.StringUtils;
import com.trs.tcache.util.redis.JedisPoolUtils;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.HostAndPort;

import java.util.NoSuchElementException;
import java.util.Set;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
public class RedisNormalUtils extends RedisUtils {

    private final JedisPoolUtils jedisClient;

    public RedisNormalUtils(String type) {
        Set<HostAndPort> nodes = makeNodes();
        if (RedisUtils.REDIS_TYPE_SENTINEL.equalsIgnoreCase(type)) {
            checkNotEmpty(REDIS_MASTER_NAME, "redis.masterName不能为空");
            this.jedisClient = Try.of(() -> JedisPoolUtils.newSentinel(
                    REDIS_MASTER_NAME,
                    nodes.stream().map(HostAndPort::toString).collect(Collectors.joining(StringUtils.SEPARATOR_COMMA)),
                    REDIS_PASSWORD)
                ).onFailure(e -> log.error("redis初始化异常", e))
                .getOrElseThrow(error -> new NoSuchElementException(String.format(
                    "无法初始redis,请确认是否已经配置了redis所需的连接信息,当前错误信息为[%s],具体错误详细信息参见:https://wiki.trscd.com"
                        +
                        ".cn/display/CDBIGDATA/media_base_cache", error.getMessage())));
        } else {
            HostAndPort info = nodes.stream()
                .findFirst()
                .orElseThrow(() -> new NoSuchElementException("redis.nodes或redis.host，redis.port不能同时为空"));
            this.jedisClient = Try.of(
                    () -> JedisPoolUtils.newBuilder(info.getHost(), Integer.toString(info.getPort()), REDIS_PASSWORD))
                .onFailure(e -> log.error("redis初始化异常", e))
                .getOrElseThrow(error -> new NoSuchElementException(String.format(
                    "无法初始redis,请确认是否已经配置了redis所需的连接信息,当前错误信息为[%s],具体错误详细信息参见:https://wiki.trscd.com"
                        +
                        ".cn/display/CDBIGDATA/media_base_cache", error.getMessage())));
        }
    }

    @Override
    public String getByKey(String key, Integer index) {
        return getJedisClient().get(key, index);
    }

    @Override
    public String getByKey(String key) {
        return getJedisClient().get(key);
    }
}
