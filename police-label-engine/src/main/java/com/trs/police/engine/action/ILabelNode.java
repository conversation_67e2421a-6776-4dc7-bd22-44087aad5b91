package com.trs.police.engine.action;

import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.node.properties.NodeProperties;
import com.trs.police.engine.vo.LabelNodeDTO;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * @param <T> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/26 18:17
 * @since 1.0
 */
public interface ILabelNode<T extends NodeProperties> extends Serializable {

    /**
     * loadNode<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/26 18:18
     */
    BaseLabelNode<T> loadNode(LabelNodeDTO dto);

    /**
     * 节点类型 {@link NodeType}
     *
     * @return 节点类型
     */
    Integer nodeType();

    /**
     * 根据输入数据获取到输出数据（Spark实现）
     *
     * @param spark  sparkSession
     * @param inData 输入节点
     * @param context 上下文
     * @return 数据
     */
    Dataset<Row> doProcess(SparkSession spark, Dataset<Row> inData, LabelNodeContext context);

    /**
     * 根据输入数据获取到输出数据（Spark实现）
     *
     * @param spark  sparkSession
     * @param inData 输入节点
     * @param context 上下文
     * @return 数据
     */
    Dataset<Row> process(SparkSession spark, Dataset<Row> inData, LabelNodeContext context);
}
