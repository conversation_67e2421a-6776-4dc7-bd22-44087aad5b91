package com.trs.police.engine.utils;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.PreConditionCheck;
import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.ILabelNode;
import com.trs.police.engine.dto.LabelComputeDTO;
import com.trs.police.engine.dto.node.properties.NodeProperties;
import com.trs.police.engine.vo.LabelNodeDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/26 15:47
 * @since 1.0
 */
@Slf4j
public class NodeUtils {

    private static final Map<Integer, ILabelNode<?>> SOURCES = new HashMap<>();

    static {
        log.info("开始加载com.trs.police.engine.action.ILabelNode.class");
        for (ILabelNode<?> source : ServiceLoader.load(ILabelNode.class)) {
            log.info("加载了[{}]", source.getClass().getName());
            PreConditionCheck.checkNotNull(source.nodeType(), source.getClass().getName() + "的nodeType不能为空");
            if (SOURCES.containsKey(source.nodeType())) {
                log.error("nodeType[{}]已经存在", source.nodeType());
                throw new RuntimeException("nodeType[" + source.nodeType() + "]已经存在");
            }
            SOURCES.put(source.nodeType(), source);
        }
        log.info("结束加载com.trs.police.engine.action.ILabelNode.class");
    }

    /**
     * 获取标签节点
     *
     * @param dto 数据
     * @return 标签节点
     */
    public static List<BaseLabelNode<? extends NodeProperties>> getLabelNodes(LabelComputeDTO dto) {
        List<BaseLabelNode<? extends NodeProperties>> labelNodes = new ArrayList<>();
        for (LabelNodeDTO labelNodeDTO : dto.getLabelNodeDtos()) {
            BaseLabelNode<? extends NodeProperties> load = loadNode(labelNodeDTO);
            if (load != null) {
                labelNodes.add(load);
            }
        }
        return labelNodes;
    }

    /**
     * loadNode<BR>
     *
     * @param labelNodeDTO 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/6/26 15:45
     */
    public static BaseLabelNode<? extends NodeProperties> loadNode(LabelNodeDTO labelNodeDTO) {
        Integer nodeType = labelNodeDTO.getNodeType();
        if (SOURCES.containsKey(nodeType)) {
            return SOURCES.get(nodeType).loadNode(labelNodeDTO);
        }
        return null;
    }

    /**
     * 获取属性
     *
     * @param nodeProperties 属性
     * @param clazz          类型
     * @param <T>            属性
     * @return 属性
     */
    public static <T extends NodeProperties> T getPropertyAs(String nodeProperties, Class<T> clazz) {
        nodeProperties = nodeProperties.replaceAll("\\\\\"", "\"");
        return JSONObject.parseObject(nodeProperties, clazz);
    }
}
