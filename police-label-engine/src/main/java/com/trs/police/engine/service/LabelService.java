package com.trs.police.engine.service;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.LabelComputeAction;
import com.trs.police.engine.dto.LabelComputeDTO;
import com.trs.police.engine.dto.node.properties.NodeProperties;
import com.trs.police.engine.utils.NodeUtils;
import com.trs.police.engine.vo.LabelComputeResultVO;
import com.trs.police.engine.vo.LableDataVO;
import com.trs.spark.action.impl.peek.PrintRddAction;
import com.trs.spark.action.impl.peek.PrintRddCountAction;
import com.trs.spark.process.SparkProcessBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;

import java.util.List;

/**
 * 标签计算
 *
 * <AUTHOR>
 */
@Slf4j
public class LabelService extends BaseService<LabelComputeDTO, LableDataVO, LabelComputeResultVO, LabelComputeResultVO> {

    public LabelService() {
        super();
    }

    public LabelService(boolean openLog) {
        super(openLog);
    }

    @Override
    public String runJob(SparkSession session, String data) throws ServiceException {
        LabelComputeDTO dto = parseInputData(data);
        dto.isValid();

        List<BaseLabelNode<? extends NodeProperties>> labelNodes = NodeUtils.getLabelNodes(dto);
        SparkProcessBuilder<JavaRDD<LabelComputeResultVO>> builder = SparkProcessBuilder.newProcess()
                .convert(
                        new LabelComputeAction(labelNodes))
                .peek(new PrintRddCountAction(
                        rdd -> "计算结果的数量为：" + rdd,
                        isOpenLog()
                ))
                .peek(new PrintRddAction<LabelComputeResultVO>(
                        rdd -> "计算结果为：" + JsonUtils.toOptionalJson(rdd).get(),
                        isOpenLog()
                ))
                .peek(buildWriteAction(dto));
        builder.doRun(session, JavaRDD.class);
        return "SUCCESS";
    }

    @Override
    protected String outputTableName() {
        return "tb_label_compute_result";
    }

}
