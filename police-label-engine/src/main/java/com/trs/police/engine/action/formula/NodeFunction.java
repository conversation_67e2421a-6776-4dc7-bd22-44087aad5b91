package com.trs.police.engine.action.formula;

import com.trs.police.engine.vo.IKey;
import org.apache.spark.sql.Column;

import java.util.List;

/**
 * 函数
 *
 * <AUTHOR>
 */
public interface NodeFunction extends IKey {

    /**
     * 执行计算 的到计算结果
     *
     * @param parameters 参数
     * @return 计算结果
     */
    Column execute(List<Column> parameters);

    @Override
    default String desc() {
        return "";
    }
}
