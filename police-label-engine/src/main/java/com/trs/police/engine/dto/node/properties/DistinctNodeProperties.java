package com.trs.police.engine.dto.node.properties;

import com.trs.police.engine.dto.node.properties.bean.DistinctField;
import com.trs.police.engine.dto.node.properties.bean.DistinctRule;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 去重节点配置
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DistinctNodeProperties extends NodeProperties {

    private List<DistinctField> groupField;

    private List<DistinctRule> distinctRule;
}
