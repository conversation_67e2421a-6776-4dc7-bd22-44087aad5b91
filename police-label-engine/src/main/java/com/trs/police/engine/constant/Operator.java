package com.trs.police.engine.constant;

/**
 * 操作符号枚举
 *
 * <AUTHOR>
 */
public enum Operator {
    ADD("+"),
    SUBTRACT("-"),
    MULTIPLY("*"),
    DIVIDE("/");

    private final String symbol;

    /**
     * 构造函数
     *
     * @param symbol sb
     */
    Operator(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 通过符号获取操作符
     *
     * @param symbol sb
     * @return 操作符
     */
    public static Operator fromSymbol(String symbol) {
        for (Operator op : values()) {
            if (op.symbol.equals(symbol)) {
                return op;
            }
        }
        throw new IllegalArgumentException("Unknown operator: " + symbol);
    }
}
