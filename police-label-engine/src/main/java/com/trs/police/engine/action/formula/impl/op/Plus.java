package com.trs.police.engine.action.formula.impl.op;

import com.trs.police.engine.action.formula.Operator;
import com.trs.police.engine.constant.DataBaseFieldMappingType;
import com.trs.police.engine.vo.FieldValue;

import java.math.BigDecimal;

import static com.trs.police.engine.constant.NodeConstants.NUMBER;
/**
 * 加
 *
 * <AUTHOR>
 */
public class Plus implements Operator {

    public static final Plus INSTANCE = new Plus();

    private Plus() {
    }

    @Override
    public FieldValue calculate(FieldValue left, FieldValue right) {
        if (!DataBaseFieldMappingType.NUMBER.getFieldType().equals(left.getTypeCode())
                || !DataBaseFieldMappingType.NUMBER.getTypeName().equals(right.getTypeCode())) {
            throw new RuntimeException("加法运算要求两个参数都是数字");
        }
        Double v = BigDecimal.valueOf(Double.valueOf(left.getValue()))
                .add(BigDecimal.valueOf(Double.valueOf(right.getValue())))
                .doubleValue();
        return new FieldValue(v.toString(), NUMBER);
    }
}
