package com.trs.police.engine.dto;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.police.engine.vo.LabelNodeDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *  标签计算DTO
 *
 *
 */
@Data
@NoArgsConstructor
public class LabelComputeDTO extends BaseDTO {

    private List<LabelNodeDTO> labelNodeDtos;

    @Override
    protected boolean checkParams() throws ServiceException {
        return true;
    }

    public LabelComputeDTO(List<LabelNodeDTO> labelNodeDtos) {
        this.labelNodeDtos = labelNodeDtos;
    }
}
