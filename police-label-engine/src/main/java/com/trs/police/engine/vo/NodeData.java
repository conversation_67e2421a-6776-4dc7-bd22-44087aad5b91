package com.trs.police.engine.vo;

import com.trs.police.engine.dto.node.NodeMeta;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 节点数据
 */
@Data
@NoArgsConstructor
public class NodeData {

    private Long totalCount;

    /**
     * 节点信息
     */
    private NodeMeta nodeMeta;

    /**
     *  表头
     */
    private List<FieldInfoVO> header;

    /**
     * 行数据
     */
    private List<Row> rowList;



    /**
     * 获取数据
     *
     * @return 数据
     */
    public List<List<FieldValue>> getData() {
        return rowList.stream()
                .map(Row::getRowData)
                .collect(Collectors.toList());
    }

    /**
     * 设置数据
     *
     * @param data 数据
     */
    public void setData(List<List<FieldValue>> data) {
        this.rowList = data.stream()
                .map(rowData -> new Row(rowData))
                .collect(Collectors.toList());
    }

    /**
     * 通过enName定位到列下标
     *
     * @return 列下标
     */
    public Map<String, List<Integer>> findColumnIndex() {
        Map<String, List<Integer>> result = new HashMap<>();
        for (int i = 0; i < header.size(); i++) {
            FieldInfoVO fieldInfoVO = header.get(i);
            String columnName = fieldInfoVO.getEnName();
            if (!result.containsKey(columnName)) {
                result.put(columnName, new ArrayList<>());
            }
            result.get(columnName).add(i);
        }
        return result;
    }

    /**
     * 根据字段id查找字段索引
     *
     * @return 字段索引
     */
    public Map<String, Integer> findColumnIndexById() {
        Map<String, Integer> result = new HashMap<>();
        for (int i = 0; i < header.size(); i++) {
            FieldInfoVO fieldInfoVO = header.get(i);
            String columnName = fieldInfoVO.getId();
            if (!result.containsKey(columnName)) {
                result.put(columnName, i);
            }
        }
        return result;
    }
}
