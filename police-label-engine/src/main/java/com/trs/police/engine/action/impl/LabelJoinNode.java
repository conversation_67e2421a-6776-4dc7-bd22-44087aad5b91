package com.trs.police.engine.action.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.action.LabelMultiComputeNode;
import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.LabelComputeDTO;
import com.trs.police.engine.dto.node.properties.LabelJoinProperties;
import com.trs.police.engine.utils.NodeUtils;
import com.trs.police.engine.vo.LabelNodeDTO;
import lombok.Setter;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：join行为
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/26 15:03
 * @since 1.0
 */
@Setter
public class LabelJoinNode extends BaseLabelNode<LabelJoinProperties> {

    private List<LabelNodeDTO> flow;
    private String tableA;
    private String fieldA;
    private String tableB;
    private String fieldB;
    private String joinType;

    private String getJoinType(Integer joinType) {
        if (joinType == null) {
            return "inner";
        }
        switch (joinType) {
            case 4:
                return "right";
            case 3:
                return "full";
            case 2:
                return "left";
            case 1:
            default:
                return "inner";
        }
    }

    private String findTableName(String fieldName) {
        PreConditionCheck.checkNotEmpty(fieldName, "fieldName不能为空");
        PreConditionCheck.checkArgument(fieldName.contains("."), fieldName + "无法解析出表名");
        return fieldName.substring(0, fieldName.indexOf("."));
    }

    private String findFieldName(String fieldName) {
        PreConditionCheck.checkNotEmpty(fieldName, "fieldName不能为空");
        return fieldName.substring(fieldName.indexOf(".") + 1);
    }

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> inData, LabelNodeContext context) {
        final LabelComputeDTO dto = new LabelComputeDTO();
        dto.setLabelNodeDtos(flow);
        final Dataset<Row> load = new LabelMultiComputeNode(NodeUtils.getLabelNodes(dto))
                .process(spark, spark.emptyDataFrame(), context);
        Dataset<Row> row = inData.as(tableA).join(
                load.as(tableB),
                inData.col(fieldA).equalTo(load.col(fieldB)),
                joinType
        );
        if (StringUtils.isNotEmpty(getNodeProperties().getWhere())) {
            row = row.where(getNodeProperties().getWhere());
        }
        return row;
    }

    @Override
    public Integer nodeType() {
        return NodeType.JOIN;
    }

    @Override
    public BaseLabelNode<LabelJoinProperties> loadNode(LabelNodeDTO dto) {
        LabelJoinNode load = new LabelJoinNode();
        final LabelJoinProperties nodeProperties = NodeUtils.getPropertyAs(dto.getNodeProperties(), LabelJoinProperties.class);
        load.setNodeProperties(nodeProperties);
        load.setOutputRow(dto.getOutputRow());
        load.setFlow(PreConditionCheck.checkNotEmpty(dto.getFlow(), "flow不能为空"));
        load.setTableA(findTableName(nodeProperties.getFieldA()));
        load.setFieldA(findFieldName(nodeProperties.getFieldA()));
        load.setTableB(findTableName(nodeProperties.getFieldB()));
        load.setFieldB(findFieldName(nodeProperties.getFieldB()));
        load.setJoinType(getJoinType(nodeProperties.getJoinType()));
        return load;
    }
}
