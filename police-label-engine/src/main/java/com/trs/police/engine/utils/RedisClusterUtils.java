package com.trs.police.engine.utils;

import com.trs.common.utils.StringUtils;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.JedisCluster;

import java.util.NoSuchElementException;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
public class RedisClusterUtils extends RedisUtils {

    private JedisCluster jedisCluster;

    public RedisClusterUtils() {
        this.jedisCluster = Try.of(() -> {
                if (StringUtils.isEmpty(REDIS_PASSWORD)) {
                    return new JedisCluster(makeNodes());
                }
                return new JedisCluster(
                    makeNodes(),
                    2000,
                    2000,
                    5,
                    REDIS_PASSWORD,
                    new GenericObjectPoolConfig<>()
                );
            })
            .onFailure(e -> log.error("redis cluster初始化异常", e))
            .getOrElseThrow(error -> new NoSuchElementException(String.format(
                "无法初始redis,请确认是否已经配置了redis所需的连接信息,当前错误信息为[%s],具体错误详细信息参见:https://wiki.trscd.com"
                    +
                    ".cn/display/CDBIGDATA/media_base_cache", error.getMessage())));
    }

    @Override
    public String getByKey(String key, Integer index) {
        return getJedisCluster().get(key);
    }

    @Override
    public String getByKey(String key) {
        return getJedisCluster().get(key);
    }

}
