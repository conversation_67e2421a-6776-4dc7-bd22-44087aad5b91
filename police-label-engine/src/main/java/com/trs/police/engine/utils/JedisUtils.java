package com.trs.police.engine.utils;

import com.trs.common.utils.StringUtils;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;

import java.util.NoSuchElementException;

/**
 * JedisUtils
 *
 */
public class JedisUtils extends RedisUtils {

    private final HostAndPort info;

    public JedisUtils() {
        this.info = makeNodes()
            .stream()
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("redis.nodes或redis.host，redis.port不能同时为空"));
    }

    private Jedis getJedis() {
        return new Jedis(info);
    }

    @Override
    public String getByKey(String key, Integer index) {
        try (Jedis jedis = getJedis()) {
            if (StringUtils.isNotEmpty(REDIS_PASSWORD)) {
                jedis.auth(REDIS_PASSWORD);
            }
            jedis.select(index);
            return jedis.get(key);
        }
    }

}
