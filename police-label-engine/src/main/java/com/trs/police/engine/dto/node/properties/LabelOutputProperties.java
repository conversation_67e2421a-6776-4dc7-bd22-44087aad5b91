package com.trs.police.engine.dto.node.properties;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 标签输出节点属性
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelOutputProperties extends NodeProperties {

    /**
     * 打标类型
     */
    private Integer objectType;

    /**
     * 过期时间
     */
    private String expireTime;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签英文名
     */
    private String enName;

    /**
     * 警种
     */
    private Long policeKind;

    /**
     * 分类
     */
    private Long categoryCode;

    /**
     * 打标对象列名
     */
    private String objectNumberCol;

    /**
     * 数据源id列名
     */
    private String sourceDataIdCol;

    /**
     * 来源表名
     */
    private String sourceTabel;

    /**
     * 关联对象编号列名
     */
    private String relatedObjectNumberCol;
}
