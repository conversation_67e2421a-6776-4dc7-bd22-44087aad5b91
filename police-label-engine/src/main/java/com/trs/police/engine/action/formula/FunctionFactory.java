package com.trs.police.engine.action.formula;


import com.trs.police.engine.action.formula.function.*;
import org.apache.spark.sql.Column;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 函数工厂
 *
 * <AUTHOR>
 */
public class FunctionFactory {

    private static Map<String, NodeFunction> functions = Arrays.asList(
                    AbsNodeFunction.INSTANCE,
                    DateDiffNodeFunction.INSTANCE,
                    GetDatePartFunction.INSTANCE,
                    CurdateNodeFunction.INSTANCE,
                    MergeLatLonFunction.INSTANCE,
                    StringToNumberFunction.INSTANCE,
                    CopyNodeFunction.INSTANCE
    )
            .stream()
            .collect(Collectors.toMap(NodeFunction::key, f -> f));


    /**
     * 执行函数
     *
     * @param functionName 函数名称
     * @param parameters   参数
     * @return 函数执行结果
     */
    public static Column execute(String functionName, List<Column> parameters) {
        return functions.get(functionName).execute(parameters);
    }
}
