package com.trs.police.engine.action;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.engine.dto.node.properties.NodeProperties;
import com.trs.police.engine.utils.NodeUtils;
import com.trs.police.engine.vo.LabelNodeDTO;
import com.trs.police.engine.vo.RowFieldVo;
import io.vavr.control.Try;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.*;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;

/**
 * Spark节点基类
 *
 * @param <T> 节点属性类型
 * <AUTHOR>
 */
@Data
@Slf4j
public abstract class BaseLabelNode<T extends NodeProperties> implements ILabelNode<T> {

    /**
     * 节点属性 json字符串
     */
    private T nodeProperties;

    private List<RowFieldVo> outputRow;

    @Override
    public BaseLabelNode<T> loadNode(LabelNodeDTO dto) {
        return Try.of(() -> {
                    final Class<T> clazz = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
                    final BaseLabelNode<T> d = getClass().getDeclaredConstructor().newInstance();
                    d.setNodeProperties(NodeUtils.getPropertyAs(dto.getNodeProperties(), clazz));
                    d.setOutputRow(dto.getOutputRow());
                    return d;
                }).onFailure(e -> log.error("根据参数[{}]构建对象失败", dto, e))
                .getOrElseThrow(e -> new RuntimeException(e.getMessage()+"\n"+dto.getNodeProperties()));
    }

    /**
     * 根据输入数据获取到输出数据（Spark实现）
     *
     * @param spark  sparkSession
     * @param inData 输入节点
     * @return 数据
     */
    @Override
    public Dataset<Row> process(SparkSession spark, Dataset<Row> inData, LabelNodeContext context) {
        log.info("开始执行节点[{}]", this.nodeType());
        log.info("节点参数：\n{}", JSONObject.toJSONString(nodeProperties));
        Dataset<Row> dataset = doProcess(spark, inData, context);
        List<RowFieldVo> newOutputRow = CollectionUtils.isEmpty(outputRow) ? new ArrayList<RowFieldVo>() : outputRow;
        newOutputRow.addAll(context.getIdCols());
        log.info("过滤字段：\n{}", JSONObject.toJSONString(newOutputRow));
        if (!CollectionUtils.isEmpty(newOutputRow)) {
            // 过滤字段
            Dataset<Row> newDataset = dataset.select(
                    outputRow.stream()
                            .map(it -> dataset.col("`"+it.getName()+"`").alias(it.getTargetName()))
                            .toArray(Column[]::new)
            );
            String[] columns = newDataset.columns();
            for (String column : columns) {
                if(column.contains(".")){
                    newDataset = newDataset.withColumn(column.substring(column.indexOf(".") + 1), functions.col("`"+column+"`"));
                }
            }
            return newDataset;
        } else {
            return dataset;
        }
    }

}
