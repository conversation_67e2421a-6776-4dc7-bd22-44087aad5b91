package com.trs.police.engine.action.impl;

import com.trs.police.engine.action.LabelNodeContext;
import com.trs.police.engine.action.BaseLabelNode;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.node.properties.DistinctNodeProperties;
import com.trs.police.engine.dto.node.properties.bean.DistinctRule;
import com.trs.police.engine.dto.node.properties.bean.GroupField;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.*;
import org.apache.spark.sql.expressions.Window;
import org.apache.spark.sql.expressions.WindowSpec;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Spark去重节点
 *
 * <AUTHOR>
 */
@Slf4j
public class LabelDistinctNode extends BaseLabelNode<DistinctNodeProperties> {

    @Override
    public Dataset<Row> doProcess(SparkSession spark, Dataset<Row> dataset, LabelNodeContext labelNodeContext) {
        // 应用去重操作 - 按照原本DistinctNode的逻辑
        return applySparkDistinctWithOriginalLogic(dataset, getNodeProperties());
    }

    @Override
    public Integer nodeType() {
        return NodeType.DISTINCT;
    }

    /**
     * 按照原本DistinctNode的逻辑应用Spark去重操作
     *
     * @param dataset  数据集
     * @param property 去重属性
     * @return 去重后的数据集
     */
    private Dataset<Row> applySparkDistinctWithOriginalLogic(Dataset<Row> dataset, DistinctNodeProperties property) {
        if (property.getGroupField() == null || property.getGroupField().isEmpty()) {
            // 全字段去重
            return dataset.distinct();
        }

        // 1. 构建分组字段列表 - 使用DistinctField转换为GroupField
        List<GroupField> groupFieldList = property.getGroupField().stream()
                .map(field -> new GroupField(field.getFiled(), field.getDistinctType()))
                .collect(Collectors.toList());

        // 2. 构建排序字段列表 - 使用DistinctRule
        List<Column> orderColumns = buildOrderColumns(property.getDistinctRule());

        // 3. 使用窗口函数实现分组去重
        return applyWindowBasedDistinctWithSort(dataset, groupFieldList, orderColumns);
    }

    /**
     * 构建排序列
     *
     * @param distinctRules 去重规则列表
     * @return 排序列列表
     */
    private List<Column> buildOrderColumns(List<DistinctRule> distinctRules) {
        if (distinctRules == null || distinctRules.isEmpty()) {
            // 如果没有排序规则，使用单调递增ID作为默认排序
            return Collections.singletonList(functions.monotonically_increasing_id().asc());
        }

        return distinctRules.stream()
                .map(rule -> {
                    Column column = functions.col(rule.getCol());
                    return rule.isDesc() ? column.desc() : column.asc();
                })
                .collect(Collectors.toList());
    }

    /**
     * 使用窗口函数实现带排序的分组去重
     *
     * @param dataset      数据集
     * @param groupFields  分组字段列表
     * @param orderColumns 排序列列表
     * @return 去重后的数据集
     */
    private Dataset<Row> applyWindowBasedDistinctWithSort(Dataset<Row> dataset,
                                                          List<GroupField> groupFields,
                                                          List<Column> orderColumns) {
        // 构建分组列 - 使用字段名
        Column[] partitionColumns = groupFields.stream()
                .map(field -> functions.col(field.getCol()))
                .toArray(Column[]::new);

        // 构建窗口规范
        WindowSpec windowSpec = Window.partitionBy(partitionColumns);

        // 添加排序
        if (!orderColumns.isEmpty()) {
            windowSpec = windowSpec.orderBy(orderColumns.toArray(new Column[0]));
        }

        // 使用row_number()窗口函数，每组保留第一条记录
        return dataset.withColumn("rn", functions.row_number().over(windowSpec))
                .filter(functions.col("rn").equalTo(1))
                .drop("rn");
    }

}
