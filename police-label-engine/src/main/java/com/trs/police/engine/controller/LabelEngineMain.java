package com.trs.police.engine.controller;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.engine.service.LabelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.SparkSession;

/**
 * 标签计算引擎主类
 */
@Slf4j
public class LabelEngineMain extends BaseMain{

    /**
     * 主函数
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) throws ServiceException {
        if (args.length == 0 || StringUtils.isEmpty(args[0])) {
            throw new RuntimeException("获取请求参数的Key不能为空!");
        }
        SparkSession session = getSession("labelEngine");
        LabelService service = new LabelService();
        service.runJob(session, LabelEngineMain.getData(args[0]));
    }
} 