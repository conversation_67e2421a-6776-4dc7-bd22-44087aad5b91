package com.trs.police.engine.action.formula.function;

import com.trs.police.engine.action.formula.NodeFunction;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;

import java.util.List;

/**
 * 当前时间
 *
 * <AUTHOR>
 */
public class CurdateNodeFunction implements NodeFunction {

    public static final CurdateNodeFunction INSTANCE = new CurdateNodeFunction();

    private CurdateNodeFunction() {
    }

    @Override
    public Column execute(List<Column> parameters) {
        return functions.current_date();
    }

    @Override
    public String key() {
        return "curdate";
    }
}
