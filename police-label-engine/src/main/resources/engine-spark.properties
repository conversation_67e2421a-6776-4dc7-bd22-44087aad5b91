engine.spark.redis.params.db=0
isDebug=false
mapreduce.iceberg.url=
mapreduce.iceberg.username=
mapreduce.iceberg.password=
mapreduce.iceberg.tableName=iceberg.trs.gjxxb
output.databases.url=es://************:9200?es.net.ssl=false
output.databases.username=
output.databases.password=
output.kafka.url=
output.kafka.topic=
output.kafka.enableKerberos=false
output.kafka.kerberosDomain=
local.area.code.prefix=5105
redis.host=************
redis.port=22400
redis.pwd=
redis.masterName=
redis.nodes=
redis.type=normal
output.database=
read.database=