{"labelNodeDtos": [{"nodeProperties": "{\"idCol\":\"zjlid\",\"password\":\"\",\"table\":\"theme_gjxxb-000003\",\"tableId\":972,\"url\":\"es://10.18.20.131:9200\",\"username\":\"\",\"where\":\"tzlx='身份证'\"}", "nodeType": 10, "outputRow": [{"name": "jdwgs84", "targetName": "4459ece6_db08_469d_b967_8342cb484226.jdwgs84"}, {"name": "wdwgs84", "targetName": "4459ece6_db08_469d_b967_8342cb484226.wdwgs84"}, {"name": "gjlx", "targetName": "4459ece6_db08_469d_b967_8342cb484226.gjlx"}, {"name": "tzzhm", "targetName": "4459ece6_db08_469d_b967_8342cb484226.tzzhm"}, {"name": "hdsj", "targetName": "4459ece6_db08_469d_b967_8342cb484226.hdsj"}, {"name": "tzlx", "targetName": "4459ece6_db08_469d_b967_8342cb484226.tzlx"}]}, {"lastNodeId": "4459ece6_db08_469d_b967_8342cb484226", "nodeProperties": "{\"outValue\":[{\"cnName\":\"经纬度\",\"enName\":\"f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8\",\"typeCode\":\"geo\",\"value\":[{\"type\":\"function\",\"name\":\"合并经纬度\",\"metadata\":{\"id\":\"merge_lat_lon\",\"parameters\":[{\"name\":\"经度\",\"type\":\"number\",\"description\":\"经度\"},{\"name\":\"纬度\",\"type\":\"number\",\"description\":\"纬度\"}],\"returnType\":\"geo\"},\"params\":[[{\"type\":\"prop\",\"name\":\"地球经度\",\"metadata\":{\"fromNode\":\"4459ece6_db08_469d_b967_8342cb484226\",\"enName\":\"jdwgs84\",\"cnName\":\"地球经度\",\"type\":\"number\"},\"from\":174,\"to\":290,\"raw\":\"prop(\\\"地球经度\\\",{\\\"fromNode\\\":\\\"4459ece6_db08_469d_b967_8342cb484226\\\",\\\"enName\\\":\\\"jdwgs84\\\",\\\"cnName\\\":\\\"地球经度\\\",\\\"type\\\":\\\"number\\\"})\"}],[{\"type\":\"prop\",\"name\":\"地球纬度\",\"metadata\":{\"fromNode\":\"4459ece6_db08_469d_b967_8342cb484226\",\"enName\":\"wdwgs84\",\"cnName\":\"地球纬度\",\"type\":\"number\"},\"from\":291,\"to\":407,\"raw\":\"prop(\\\"地球纬度\\\",{\\\"fromNode\\\":\\\"4459ece6_db08_469d_b967_8342cb484226\\\",\\\"enName\\\":\\\"wdwgs84\\\",\\\"cnName\\\":\\\"地球纬度\\\",\\\"type\\\":\\\"number\\\"})\"}]],\"from\":0,\"to\":173,\"raw\":\"function(\\\"合并经纬度\\\",{\\\"id\\\":\\\"merge_lat_lon\\\",\\\"parameters\\\":[{\\\"name\\\":\\\"经度\\\",\\\"type\\\":\\\"number\\\",\\\"description\\\":\\\"经度\\\"},{\\\"name\\\":\\\"纬度\\\",\\\"type\\\":\\\"number\\\",\\\"description\\\":\\\"纬度\\\"}],\\\"returnType\\\":\\\"geo\\\"})\"}],\"fromNode\":\"173a00f3_73b5_444d_98d2_e1c1b2e10d31\"}]}", "nodeType": 12, "outputRow": [{"name": "173a00f3_73b5_444d_98d2_e1c1b2e10d31.f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8", "targetName": "173a00f3_73b5_444d_98d2_e1c1b2e10d31.f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.jdwgs84", "targetName": "4459ece6_db08_469d_b967_8342cb484226.jdwgs84"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.wdwgs84", "targetName": "4459ece6_db08_469d_b967_8342cb484226.wdwgs84"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.gjlx", "targetName": "4459ece6_db08_469d_b967_8342cb484226.gjlx"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.tzzhm", "targetName": "4459ece6_db08_469d_b967_8342cb484226.tzzhm"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.hdsj", "targetName": "4459ece6_db08_469d_b967_8342cb484226.hdsj"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.tzlx", "targetName": "4459ece6_db08_469d_b967_8342cb484226.tzlx"}]}, {"lastNodeId": "173a00f3_73b5_444d_98d2_e1c1b2e10d31", "nodeProperties": "{\"where\":\"f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 IS NOT NULL AND f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 != '' AND ST_Contains(ST_GeomFromText('POLYGON((104.12912750732768 30.671660827737938,104.11455858595897 30.66780442920594,104.11648253476794 30.663307719888746,104.11958715085129 30.664297939265765,104.12132599911206 30.66263532500853,104.12561992049537 30.657685050792526,104.12893966454915 30.658764392655712,104.12870822030816 30.66421334145967,104.12670707771503 30.667243643100704,104.13033038666784 30.66880739995406,104.12912750732768 30.671660827737938))'), ST_Point(CAST(CASE     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 LIKE 'POINT%' THEN         regexp_extract(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, 'POINT\\\\\\\\s*\\\\\\\\(([-+]?[0-9]*\\\\\\\\.?[0-9]+)', 1)     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 RLIKE '^[-+]?[0-9]*\\\\\\\\.?[0-9]+[ ,][-+]?[0-9]*\\\\\\\\.?[0-9]+$' THEN         split(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '[ ,]')[0]     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 LIKE '{%\\\"lon\\\"%:%}' THEN         regexp_extract(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '\\\"lon\\\"\\\\\\\\s*:\\\\\\\\s*([-+]?[0-9]*\\\\\\\\.?[0-9]+)', 1)     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 LIKE '{%\\\"longitude\\\"%:%}' THEN         regexp_extract(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '\\\"longitude\\\"\\\\\\\\s*:\\\\\\\\s*([-+]?[0-9]*\\\\\\\\.?[0-9]+)', 1)     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 LIKE '{%\\\"x\\\"%:%}' THEN         regexp_extract(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '\\\"x\\\"\\\\\\\\s*:\\\\\\\\s*([-+]?[0-9]*\\\\\\\\.?[0-9]+)', 1)     ELSE         NULL END AS DOUBLE), CAST(CASE     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 LIKE 'POINT%' THEN         regexp_extract(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, 'POINT\\\\\\\\s*\\\\\\\\([-+]?[0-9]*\\\\\\\\.?[0-9]+[ ,]([-+]?[0-9]*\\\\\\\\.?[0-9]+)\\\\\\\\)', 1)     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 RLIKE '^[-+]?[0-9]*\\\\\\\\.?[0-9]+[ ,][-+]?[0-9]*\\\\\\\\.?[0-9]+$' THEN         split(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '[ ,]')[size(split(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '[ ,]'))-1]     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 LIKE '{%\\\"lon\\\"%:%}' THEN         regexp_extract(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '\\\"lat\\\"\\\\\\\\s*:\\\\\\\\s*([-+]?[0-9]*\\\\\\\\.?[0-9]+)', 1)     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 LIKE '{%\\\"longitude\\\"%:%}' THEN         regexp_extract(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '\\\"latitude\\\"\\\\\\\\s*:\\\\\\\\s*([-+]?[0-9]*\\\\\\\\.?[0-9]+)', 1)     WHEN f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8 LIKE '{%\\\"x\\\"%:%}' THEN         regexp_extract(f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8, '\\\"y\\\"\\\\\\\\s*:\\\\\\\\s*([-+]?[0-9]*\\\\\\\\.?[0-9]+)', 1)     ELSE         NULL END AS DOUBLE)))\"}", "nodeType": 2, "outputRow": [{"name": "173a00f3_73b5_444d_98d2_e1c1b2e10d31.f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8", "targetName": "173a00f3_73b5_444d_98d2_e1c1b2e10d31.f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.gjlx", "targetName": "4459ece6_db08_469d_b967_8342cb484226.gjlx"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.tzzhm", "targetName": "4459ece6_db08_469d_b967_8342cb484226.tzzhm"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.hdsj", "targetName": "4459ece6_db08_469d_b967_8342cb484226.hdsj"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.tzlx", "targetName": "4459ece6_db08_469d_b967_8342cb484226.tzlx"}]}, {"lastNodeId": "e5f9fd74_f539_4722_9090_172ae0c20aa7", "nodeProperties": "{\"groupField\":[{\"cnName\":\"特征值\",\"enName\":\"tzzhm\",\"typeCode\":\"string\",\"fromNode\":\"4459ece6_db08_469d_b967_8342cb484226\",\"groupType\":\"\"}],\"statisticType\":3,\"outValue\":{\"enName\":\"e63ffa16_5b67_4d33_8234_8434a3def20a\",\"cnName\":\"总数\",\"fromNode\":\"2628b224_d8f8_4c37_9d32_0f1bc71323bb\"}}", "nodeType": 6, "outputRow": [{"name": "2628b224_d8f8_4c37_9d32_0f1bc71323bb.e63ffa16_5b67_4d33_8234_8434a3def20a", "targetName": "2628b224_d8f8_4c37_9d32_0f1bc71323bb.e63ffa16_5b67_4d33_8234_8434a3def20a"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.tzzhm", "targetName": "4459ece6_db08_469d_b967_8342cb484226.tzzhm"}]}, {"lastNodeId": "2628b224_d8f8_4c37_9d32_0f1bc71323bb", "nodeProperties": "{\"where\":\"e63ffa16_5b67_4d33_8234_8434a3def20a>='1'\"}", "nodeType": 2, "outputRow": [{"name": "2628b224_d8f8_4c37_9d32_0f1bc71323bb.e63ffa16_5b67_4d33_8234_8434a3def20a", "targetName": "2628b224_d8f8_4c37_9d32_0f1bc71323bb.e63ffa16_5b67_4d33_8234_8434a3def20a"}, {"name": "4459ece6_db08_469d_b967_8342cb484226.tzzhm", "targetName": "4459ece6_db08_469d_b967_8342cb484226.tzzhm"}]}, {"nodeProperties": "{\"where\":\"\"}", "nodeType": 2, "outputRow": [{"name": "e63ffa16_5b67_4d33_8234_8434a3def20a", "targetName": "c653914e_de77_4533_9259_36460c27f6fd.e63ffa16_5b67_4d33_8234_8434a3def20a"}, {"name": "tzzhm", "targetName": "c653914e_de77_4533_9259_36460c27f6fd.tzzhm"}]}, {"lastNodeId": "c653914e_de77_4533_9259_36460c27f6fd", "nodeProperties": "{\"categoryCode\":301,\"color\":\"#F7961A\",\"cycleTime\":1,\"cycleTimeType\":3,\"effectiveTime\":60,\"effectiveTimeType\":1,\"labelId\":119,\"labelType\":0,\"mainObject\":[{\"enName\":\"tzzhm\",\"fromNode\":\"c653914e_de77_4533_9259_36460c27f6fd\",\"mainObjectTypeCode\":1}],\"name\":\"轨迹-常在杉板桥活动\",\"objectNumberCol\":\"`c653914e_de77_4533_9259_36460c27f6fd.tzzhm`\",\"objectType\":1,\"policeKind\":-1,\"relatedObject\":[{\"fromNode\":\"c653914e_de77_4533_9259_36460c27f6fd\"}]}", "nodeType": 11, "outputRow": [{"name": "c653914e_de77_4533_9259_36460c27f6fd.e63ffa16_5b67_4d33_8234_8434a3def20a", "targetName": "总数"}, {"name": "c653914e_de77_4533_9259_36460c27f6fd.tzzhm", "targetName": "特征值"}]}]}