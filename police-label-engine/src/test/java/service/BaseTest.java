package service;

import com.trs.common.datasource.PreLoads;
import com.trs.spark.SparkContextHelper;
import com.trs.spark.configuration.ConfigInNacos;
import com.trs.spark.constant.RunMode;
import org.apache.sedona.sql.utils.SedonaSQLRegistrator;
import org.apache.spark.sql.SparkSession;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class BaseTest implements Serializable {

    public static SparkSession spark;

    @BeforeAll
    static void init() {
        System.setProperty("spark.master", "local");
        System.setProperty("mapreduce.iceberg.url", "iceberg:thrift://master.hadoop.test:9083?catalogName=cd_dev_iceberg");
        System.setProperty("mapreduce.iceberg.tableName", "cd_dev_iceberg.iceberg_dev.gjxxb");
        System.setProperty("output.databases.url", "es://10.18.20.131:9200?es.net.ssl=false");

        System.out.println("开始初始化SparkSession");
        if (spark == null) {
            Map<String, Object> conf = new HashMap<>();
            conf.put("spark.serializer", "org.apache.spark.serializer.KryoSerializer");
            conf.put("spark.kryo.registrator", "org.apache.sedona.core.serde.SedonaKryoRegistrator");
            spark = SparkContextHelper.getSparkSession("xx", new ConfigInNacos("spark.properties"), RunMode.DEVELOPMENT,
                    Optional.empty());
        }
        // 注册Sedona空间函数
        SedonaSQLRegistrator.registerAll(spark);
    }

    @AfterAll
    static void close() {
        System.out.println("开始销毁SparkSession");
        if (spark != null) {
            spark.stop();
        }
    }
}
