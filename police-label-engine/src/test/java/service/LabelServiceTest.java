package service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ServiceException;
import com.trs.police.engine.action.impl.LabelInputNode;
import com.trs.police.engine.constant.NodeType;
import com.trs.police.engine.dto.LabelComputeDTO;
import com.trs.police.engine.dto.node.properties.LabelInputProperties;
import com.trs.police.engine.dto.node.properties.LabelJoinProperties;
import com.trs.police.engine.dto.node.properties.LabelOutputProperties;
import com.trs.police.engine.service.LabelService;
import com.trs.police.engine.vo.LabelNodeDTO;
import com.trs.police.engine.vo.RowFieldVo;
import org.apache.commons.io.IOUtils;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;
import org.junit.jupiter.api.Test;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class LabelServiceTest extends BaseTest {

    LabelService service = new LabelService(true);

    @Test
    void doAction() throws ServiceException {
        String s = service.runJob(spark, JSON.toJSONString(new LabelComputeDTO()));
    }

    @Test
    void test2() throws ServiceException {
        String s = "\"{\\\"labelNodeDtos\\\":[{\\\"nodeProperties\\\":\\\"{\\\\\\\"idCol\\\\\\\":\\\\\\\"zjlid\\\\\\\",\\\\\\\"password\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"table\\\\\\\":\\\\\\\"theme_gjxxb-000003\\\\\\\",\\\\\\\"url\\\\\\\":\\\\\\\"es://10.18.20.131:9200\\\\\\\",\\\\\\\"username\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"where\\\\\\\":\\\\\\\"(tzlx='身份证' AND (hour>='20' OR hour<='8'))\\\\\\\"}\\\",\\\"nodeType\\\":10,\\\"outputRow\\\":[{\\\"name\\\":\\\"gzybh\\\",\\\"targetName\\\":\\\"gzybh\\\"},{\\\"name\\\":\\\"tzzhm\\\",\\\"targetName\\\":\\\"tzzhm\\\"},{\\\"name\\\":\\\"hdsj\\\",\\\"targetName\\\":\\\"hdsj\\\"},{\\\"name\\\":\\\"zjlid\\\",\\\"targetName\\\":\\\"zjlid\\\"}]},{\\\"nodeProperties\\\":\\\"{\\\\\\\"where\\\\\\\":\\\\\\\"hdsj>='2025-01-01 20:23:18'\\\\\\\"}\\\",\\\"nodeType\\\":2,\\\"outputRow\\\":[{\\\"name\\\":\\\"gzybh\\\",\\\"targetName\\\":\\\"gzybh\\\"},{\\\"name\\\":\\\"tzzhm\\\",\\\"targetName\\\":\\\"tzzhm\\\"},{\\\"name\\\":\\\"hdsj\\\",\\\"targetName\\\":\\\"hdsj\\\"},{\\\"name\\\":\\\"zjlid\\\",\\\"targetName\\\":\\\"zjlid\\\"}]},{\\\"nodeProperties\\\":\\\"{\\\\\\\"groupField\\\\\\\":[{\\\\\\\"cnName\\\\\\\":\\\\\\\"特征值\\\\\\\",\\\\\\\"enName\\\\\\\":\\\\\\\"tzzhm\\\\\\\",\\\\\\\"typeCode\\\\\\\":\\\\\\\"string\\\\\\\",\\\\\\\"groupType\\\\\\\":\\\\\\\"\\\\\\\"}],\\\\\\\"statisticValue\\\\\\\":{\\\\\\\"cnName\\\\\\\":\\\\\\\"值\\\\\\\",\\\\\\\"enName\\\\\\\":\\\\\\\"tzzhm\\\\\\\",\\\\\\\"typeCode\\\\\\\":\\\\\\\"string\\\\\\\"},\\\\\\\"outValue\\\\\\\":{\\\\\\\"cnName\\\\\\\":\\\\\\\"数量\\\\\\\",\\\\\\\"enName\\\\\\\":\\\\\\\"count\\\\\\\",\\\\\\\"typeCode\\\\\\\":\\\\\\\"number\\\\\\\"},\\\\\\\"statisticType\\\\\\\":3}\\\",\\\"nodeType\\\":6,\\\"outputRow\\\":[{\\\"name\\\":\\\"count\\\",\\\"targetName\\\":\\\"count\\\"},{\\\"name\\\":\\\"tzzhm\\\",\\\"targetName\\\":\\\"tzzhm\\\"}]},{\\\"nodeProperties\\\":\\\"{\\\\\\\"where\\\\\\\":\\\\\\\"count>=3\\\\\\\"}\\\",\\\"nodeType\\\":2,\\\"outputRow\\\":[{\\\"name\\\":\\\"count\\\",\\\"targetName\\\":\\\"count\\\"},{\\\"name\\\":\\\"tzzhm\\\",\\\"targetName\\\":\\\"tzzhm\\\"}]},{\\\"nodeProperties\\\":\\\"{\\\\\\\"businessRule\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"categoryCode\\\\\\\":7,\\\\\\\"color\\\\\\\":\\\\\\\"#F7961A\\\\\\\",\\\\\\\"cycleTime\\\\\\\":1,\\\\\\\"cycleTimeType\\\\\\\":4,\\\\\\\"effectiveTimeType\\\\\\\":0,\\\\\\\"enName\\\\\\\":\\\\\\\"zfyc0625\\\\\\\",\\\\\\\"labelId\\\\\\\":49,\\\\\\\"labelType\\\\\\\":1,\\\\\\\"mainObject\\\\\\\":[{\\\\\\\"enName\\\\\\\":\\\\\\\"tzzhm\\\\\\\",\\\\\\\"fromNode\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"mainObjectTypeCode\\\\\\\":1}],\\\\\\\"name\\\\\\\":\\\\\\\"昼伏夜出0625\\\\\\\",\\\\\\\"objectNumberCol\\\\\\\":\\\\\\\"tzzhm\\\\\\\",\\\\\\\"policeKind\\\\\\\":-1}\\\",\\\"nodeType\\\":11,\\\"outputRow\\\":[]}]}\"";
        System.out.println(s);
        LabelComputeDTO dto = service.parseInputData(s);
        System.out.println();
    }

    @Test
    public void test() throws ServiceException {
        LabelInputProperties inputProperties = new LabelInputProperties();
        inputProperties.setUrl("es://10.18.20.131:9200;10.18.20.131:9200?es.net.ssl=false");
        inputProperties.setUsername("admin");
        inputProperties.setPassword("trsadmin");
        inputProperties.setTable("theme_gjxxb-000003");
        LabelInputNode inputNode = new LabelInputNode();
        inputNode.setNodeProperties(inputProperties);
        inputNode.setOutputRow(new ArrayList<>());
        LabelOutputProperties labelOutputProperties = new LabelOutputProperties();
        labelOutputProperties.setName("测试");
        labelOutputProperties.setPoliceKind(3L);
        labelOutputProperties.setCategoryCode(1L);
        labelOutputProperties.setObjectNumberCol("tzzhm");
        labelOutputProperties.setSourceDataIdCol("zjlid");
        labelOutputProperties.setSourceTabel("theme_gjxxb-000003");
        List<LabelNodeDTO> labelNodes = new ArrayList<>();
        labelNodes.add(new LabelNodeDTO(NodeType.LABEL_INPUT, JSONObject.toJSONString(inputNode), null));
        labelNodes.add(new LabelNodeDTO(NodeType.FILTER, "city is not null", null));
        labelNodes.add(new LabelNodeDTO(NodeType.LABEL_OUTPUT, JSONObject.toJSONString(labelOutputProperties), null));

        LabelComputeDTO dto = new LabelComputeDTO();
        dto.setLabelNodeDtos(labelNodes);

        service.runJob(spark, JSONObject.toJSONString(labelNodes));
    }

    @Test
    public void testJoin() throws ServiceException {
        List<RowFieldVo> ids = Arrays.asList(
                RowFieldVo.of("zjlid"),
                RowFieldVo.of("tzzhm")
        );

        LabelInputProperties one = new LabelInputProperties();
        one.setUrl("es://10.18.20.131:9200?es.net.ssl=false");
        one.setUsername("admin");
        one.setPassword("trsadmin");
        one.setTable("theme_gjxxb");
        one.setWhere("(gjlx = '网吧上网' or gjlx = '民航离港') and tzlx='身份证' and (tzzhm = '513022199109082934' or tzzhm = '217582198506017576')");

        LabelInputProperties two = new LabelInputProperties();
        two.setUrl("es://10.18.20.131:9200?es.net.ssl=false");
        two.setUsername("admin");
        two.setPassword("trsadmin");
        two.setTable("theme_gjxxb");
        two.setWhere("gjlx='国外旅客入住' and tzlx='身份证' and tzzhm='513022199109082934'");

        List<LabelNodeDTO> labelNodes = new ArrayList<>();
        labelNodes.add(new LabelNodeDTO(
                NodeType.LABEL_INPUT,
                JSON.toJSONString(one),
                ids
        ));
        labelNodes.add(new LabelNodeDTO(
                NodeType.JOIN,
                JSON.toJSONString(LabelJoinProperties.builder()
                        .fieldA("one.tzzhm")
                        .fieldB("two.tzzhm")
                        .joinType(2)
                        .where("two.zjlid is not null")
                        .build()),
                Arrays.asList(
                        RowFieldVo.of("one.zjlid", "zjlid"),
                        RowFieldVo.of("two.zjlid", "zjlid2"),
                        RowFieldVo.of("one.tzzhm", "tzzhm")
                ),
                Collections.singletonList(new LabelNodeDTO(
                        NodeType.LABEL_INPUT,
                        JSON.toJSONString(two),
                        ids
                )),null
        ));
        labelNodes.add(new LabelNodeDTO(
                NodeType.JOIN,
                JSON.toJSONString(LabelJoinProperties.builder()
                        .fieldA("one.tzzhm")
                        .fieldB("two.tzzhm")
                        .joinType(2)
                        .where("two.zjlid is not null")
                        .build()),
                Arrays.asList(
                        RowFieldVo.of("one.zjlid", "zjlid"),
                        RowFieldVo.of("one.zjlid2", "zjlid2"),
                        RowFieldVo.of("two.zjlid", "zjlid3"),
                        RowFieldVo.of("one.tzzhm", "tzzhm")
                ),
                Collections.singletonList(new LabelNodeDTO(
                        NodeType.LABEL_INPUT,
                        JSON.toJSONString(two),
                        ids
                )),null
        ));

        LabelOutputProperties out = new LabelOutputProperties();
        out.setObjectNumberCol("tzzhm");
        out.setSourceDataIdCol("zjlid");

        labelNodes.add(new LabelNodeDTO(
                NodeType.LABEL_OUTPUT,
                JSON.toJSONString(out),
                Collections.emptyList()
        ));

        LabelComputeDTO dto = new LabelComputeDTO();
        dto.setLabelNodeDtos(labelNodes);
        service.runJob(spark, JSON.toJSONString(dto));
    }

    @Test
    public void labelServiceTest() throws ServiceException, Exception {
        LabelService service = new LabelService();
        String params = params("params");
        System.out.println(params);
        service.runJob(spark, params);
    }

    protected String params(String name) throws Exception {
        try (InputStream inputStream = BaseTest.class.getClassLoader().getResourceAsStream(name + ".txt")) {
            return IOUtils.toString(inputStream);
        }
    }
}
