package service;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.RowFactory;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.Metadata;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class GeoTest extends BaseTest {

    @Test
    void testSpatialFunction() {
        String sql = "SELECT ST_Contains(ST_GeomFromText('POLYGON((0 0, 0 1, 1 1, 1 0, 0 0))'), ST_Point(0.5, 0.5))";
        spark.sql(sql).show();
        // 预期输出：true
    }

    @Test
    public void testGeoFunction() {
        // Create mock data for testing
        Dataset<Row> mockData = createMockData();

        // Create a temporary view for SQL queries
        mockData.createOrReplaceTempView("test_data");
        spark.sql("SELECT * FROM test_data").show();

        // 使用CASE WHEN处理不同格式的坐标数据
        String where = getString(
                "f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8",
                "POLYGON((104.12912750732768 30.671660827737938,104.11455858595897 30.66780442920594,104.11648253476794 30.663307719888746,104.11958715085129 30.664297939265765,104.12132599911206 30.66263532500853,104.12561992049537 30.657685050792526,104.12893966454915 30.658764392655712,104.12870822030816 30.66421334145967,104.12670707771503 30.667243643100704,104.13033038666784 30.66880739995406,104.12912750732768 30.671660827737938))",
                false);

        Dataset<Row> result = spark.sql("SELECT * FROM test_data WHERE " + where);
        result.show();
        // 注意 4过 5 不过
        Assert.assertEquals(7, result.count());
    }




    private Dataset<Row> createMockData() {
        // Define schema
        StructType schema = new StructType(new StructField[]{
                new StructField("id", DataTypes.IntegerType, false, Metadata.empty()),
                new StructField("name", DataTypes.StringType, false, Metadata.empty()),
                new StructField("age", DataTypes.IntegerType, false, Metadata.empty()),
                new StructField("department", DataTypes.StringType, false, Metadata.empty()),
                new StructField("salary", DataTypes.DoubleType, false, Metadata.empty()),
                new StructField("f1ab856f_ff78_4ae4_b33e_37e52f2e5bf8", DataTypes.StringType, false, Metadata.empty())
        });

        // Create mock data - 包含所有可能的坐标格式
        java.util.List<Row> rows = java.util.Arrays.asList(
                // WKT格式 - 空格分隔
                RowFactory.create(1, "John Doe", 32, "Engineering", 75000.0, "POINT (104.115977 30.666326)"),

                // WKT格式 - 逗号分隔
                RowFactory.create(2, "Jane Smith", 28, "Marketing", 68000.0, "POINT (104.115977,30.666326)"),

                // 纯数值格式 - 空格分隔
                RowFactory.create(3, "Mike Johnson", 45, "Sales", 92000.0, "104.115977 30.666326"),

                // 纯数值格式 - 逗号分隔 (经度,纬度)
                RowFactory.create(4, "Sarah Williams", 24, "Engineering", 65000.0, "104.115977,30.666326"),

                // ES格式 - 逗号分隔 (纬度,经度) 理论上失败
                RowFactory.create(5, "David Brown", 36, "HR", 71000.0, "30.666326,104.115977"),

                // JSON格式 - lon/lat
                RowFactory.create(6, "Alice Cooper", 29, "Finance", 68000.0, "{\"lon\": 104.115977,\"lat\": 30.666326}"),

                // JSON格式 - longitude/latitude
                RowFactory.create(7, "Bob Smith", 35, "IT", 82000.0, "{\"longitude\": 104.115977,\"latitude\": 30.666326}"),

                // JSON格式 - x/y
                RowFactory.create(8, "Charlie Wilson", 42, "Operations", 75000.0, "{\"x\": 104.115977,\"y\": 30.666326}"),

                // 只有经度
                RowFactory.create(9, "Diana Lee", 27, "Marketing", 62000.0, "104.115977"),

                // 空值
                RowFactory.create(10, "Edward King", 38, "Management", 85000.0, "")
        );
        // Create DataFrame
        return spark.createDataFrame(rows, schema);
    }


    private static String getString(String field, String value, Boolean isTemplate) {
        String sqlValue = isTemplate ? "?" : String.format("'%s'", value);
        String where = String.format("%s IS NOT NULL AND %s != '' AND " +
                        "ST_Contains(ST_GeomFromText(%s), " +
                        "ST_Point(" +
                        "CAST(CASE " +
                        "    WHEN %s LIKE 'POINT%%' THEN " +
                        "        regexp_extract(%s, 'POINT\\\\s*\\\\(([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s RLIKE '^[-+]?[0-9]*\\\\.?[0-9]+[ ,][-+]?[0-9]*\\\\.?[0-9]+$' THEN " +
                        "        split(%s, '[ ,]')[0] " +
                        "    WHEN %s LIKE '{%%\"lon\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"lon\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s LIKE '{%%\"longitude\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"longitude\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s LIKE '{%%\"x\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"x\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    ELSE " +
                        "        NULL " +
                        "END AS DOUBLE), " +
                        "CAST(CASE " +
                        "    WHEN %s LIKE 'POINT%%' THEN " +
                        "        regexp_extract(%s, 'POINT\\\\s*\\\\([-+]?[0-9]*\\\\.?[0-9]+[ ,]([-+]?[0-9]*\\\\.?[0-9]+)\\\\)', 1) " +
                        "    WHEN %s RLIKE '^[-+]?[0-9]*\\\\.?[0-9]+[ ,][-+]?[0-9]*\\\\.?[0-9]+$' THEN " +
                        "        split(%s, '[ ,]')[size(split(%s, '[ ,]'))-1] " +
                        "    WHEN %s LIKE '{%%\"lon\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"lat\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s LIKE '{%%\"longitude\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"latitude\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s LIKE '{%%\"x\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"y\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    ELSE " +
                        "        NULL " +
                        "END AS DOUBLE)" +
                        "))",
                field, field, sqlValue,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field);
        return where;
    }





}
