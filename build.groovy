pipeline {
    agent any

    tools {
        maven 'maven-3.8.1'
        jdk 'openjdk-11'
    }

    stages {
        stage('Build GateWay') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-gateway/**/*.xml'
                    changeset '**/police-gateway/**/*.java'
                    changeset '**/police-gateway/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-gateway -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If <PERSON><PERSON> was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-gateway 116'
                    echo 'build police-gateway success'
                }
            }
        }

        stage('Build Authorize') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-authorize/**/*.xml'
                    changeset '**/police-authorize/**/*.java'
                    changeset '**/police-authorize/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-authorize -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-authorize 118'
                    echo 'build police-authorize success'
                }
            }
        }

        stage('Build OSS') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-oss/**/*.xml'
                    changeset '**/police-oss/**/*.java'
                    changeset '**/police-oss/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-oss -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-oss 119'
                    echo 'build police-sso success'
                }
            }
        }

        stage('Build MESSAGE') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-message/**/*.xml'
                    changeset '**/police-message/**/*.java'
                    changeset '**/police-message/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-message -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-message 120'
                    echo 'build police-message success'
                }
            }
        }
        stage('Build Fight') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-fight/**/*.xml'
                    changeset '**/police-fight/**/*.java'
                    changeset '**/police-fight/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-fight -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-fight 121'
                    echo 'build police-fight success'
                }
            }
        }
        stage('Build Global') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-global/**/*.xml'
                    changeset '**/police-global/**/*.java'
                    changeset '**/police-global/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-global -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-global 138'
                    echo 'build police-global success'
                }
            }
        }
        stage('Build Permission') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-permission/**/*.xml'
                    changeset '**/police-permission/**/*.java'
                    changeset '**/police-permission/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-permission -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-permission 161'
                    echo 'build police-permission success'
                }
            }
        }

        stage('Build Control') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-control/**/*.xml'
                    changeset '**/police-control/**/*.java'
                    changeset '**/police-control/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-control -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-control 160'
                    echo 'build police-control success'
                }
            }
        }
        stage('Build Approval') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-approval/**/*.xml'
                    changeset '**/police-approval/**/*.java'
                    changeset '**/police-approval/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-approval -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-approval 163'
                    echo 'build police-approval success'
                }
            }
        }
        stage('Build Log') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-log/**/*.xml'
                    changeset '**/police-log/**/*.java'
                    changeset '**/police-log/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-log -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-log 182'
                    echo 'build police-log success'
                }
            }
        }
        stage('Build Profile') {
            when {
                anyOf {
                    changeset 'police-common/**/*.xml'
                    changeset 'police-common/**/*.java'
                    changeset 'police-common/**/*.yaml'
                    changeset 'police-common/**/*.sql'
                    changeset '**/police-profile/**/*.xml'
                    changeset '**/police-profile/**/*.java'
                    changeset '**/police-profile/**/*.yaml'
                }
            }
            steps {
                // Run Maven on a Unix agent.
                sh 'mvn -pl police-apps/police-profile -am  clean package -Pbuild-dockerfile -Dmaven.test.skip=true'
            }
            post {
                // If Maven was able to run the tests, even if some of the build
                // failed, record the build results and archive the jar file.
                success {
                    sh 'sh restart.sh police-profile 178'
                    echo 'build police-profile success'
                }
            }
        }
    }

    post {
        success {
            dingtalk(robot: 'ys-build',
                    type: 'ACTION_CARD',
                    atAll: false,
                    title: "构建成功 ：${env.JOB_NAME}",
                    messageUrl: '${env.JOB_URL}',
                    text: ["### [${env.JOB_NAME}](${env.JOB_URL}) ",
                           '---',
                           "- 任务：[${currentBuild.displayName}](${env.BUILD_URL})",
                           '- 状态：<font color=#00EE00 >成功</font>',
                           "- 持续时间：${currentBuild.durationString}".split('and counting')[0],
                           "- 执行人：${currentBuild.buildCauses.shortDescription}",])
        }

        failure {
            dingtalk(robot: 'ys-build',
                    type: 'ACTION_CARD',
                    atAll: false,
                    title: "构建失败：${env.JOB_NAME}",
                    messageUrl: '${env.JOB_URL}',
                    text: ["### [${env.JOB_NAME}](${env.JOB_URL}) ",
                           '---',
                           "- 任务：[${currentBuild.displayName}](${env.BUILD_URL})",
                           '- 状态：<font color=#EE0000 >失败</font>',
                           "- 持续时间：${currentBuild.durationString}".split('and counting')[0],
                           "- 执行人：${currentBuild.buildCauses.shortDescription}",])
        }
    }
}
