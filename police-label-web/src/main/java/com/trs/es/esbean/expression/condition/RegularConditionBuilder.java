package com.trs.es.esbean.expression.condition;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.db.sdk.mysql.builder.condition.operator.RegularOperator;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

/**
 * start
 *
 * <AUTHOR>
 */
public class RegularConditionBuilder extends AbstractEsConditionBuilder {

    @Override
    public IOperator operator() {
        return RegularOperator.INSTANCE;
    }

    protected QueryBuilder conditionToQueryBuilderWithValue(String field, Object value) {
        String query = StringUtils.toStringValue(value);
        return QueryBuilders.regexpQuery(field, query);
    }

    protected QueryBuilder conditionToQueryBuilderWithValues(String field, Object value) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        Object[] rangeParam = (Object[]) value;
        if (rangeParam.length == 1) {
            return this.conditionToQueryBuilderWithValue(field, rangeParam[0]);
        } else {
            for(Object o : rangeParam) {
                queryBuilder.should(this.conditionToQueryBuilderWithValue(field, o));
            }
            return queryBuilder;
        }
    }
}
