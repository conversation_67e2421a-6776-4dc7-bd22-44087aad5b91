package com.trs.es.esbean.expression.condition;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.db.sdk.mysql.builder.condition.operator.EndWithOperator;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

/**
 * start
 *
 * <AUTHOR>
 */
public class EndConditionBuilder extends AbstractEsConditionBuilder {

    @Override
    public IOperator operator() {
        return EndWithOperator.INSTANCE;
    }

    protected QueryBuilder conditionToQueryBuilderWithValue(String field, Object value) {
        String query = StringUtils.toStringValue(value);
        query = query.contains("*") ? query : String.format("*%s", query);
        return QueryBuilders.wildcardQuery(field, query);
    }

    protected QueryBuilder conditionToQueryBuilderWithValues(String field, Object value) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        Object[] rangeParam = (Object[]) value;
        if (rangeParam.length == 1) {
            return this.conditionToQueryBuilderWithValue(field, rangeParam[0]);
        } else {
            for(Object o : rangeParam) {
                queryBuilder.should(this.conditionToQueryBuilderWithValue(field, o));
            }
            return queryBuilder;
        }
    }
}
