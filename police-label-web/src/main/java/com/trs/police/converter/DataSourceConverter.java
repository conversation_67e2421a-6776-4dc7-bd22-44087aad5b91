package com.trs.police.converter;

import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.police.utils.SourceInfoConverter;
import com.trs.police.vo.DataSourceVO;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 数据源转换器
 * 用于DTO、实体和VO之间的转换
 *
 * <AUTHOR>
 */
public class DataSourceConverter {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 将DTO转换为实体
     *
     * @param dto 数据源DTO
     * @return 数据源实体
     */
    public static DataSource toEntity(DataSourceDTO dto) {
        DataSource entity = new DataSource();
        BeanUtils.copyProperties(dto, entity);
        String uniqueId = dto.getSourceInfo().generateUniqueId();
        entity.setGenerateUniqueId(uniqueId);
        return entity;
    }

    /**
     * 将DTO转换为对应的SourceInfo对象
     *
     * @param dto 数据源DTO
     * @return 数据源信息
     */
    public static SourceInfo toSourceInfo(DataSourceDTO dto) {
        return dto.getSourceInfo();
    }

    /**
     * 将实体转换为VO
     *
     * @param entity 数据源实体
     * @return 数据源VO
     */
    public static DataSourceVO toVO(DataSource entity) {
        if (entity == null) {
            return null;
        }

        DataSourceVO vo = new DataSourceVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setCreateTime(formatDateTime(entity.getCreateTime()));
        vo.setUpdateTime(formatDateTime(entity.getUpdateTime()));
        vo.setLastCheckTime(formatDateTime(entity.getLastCheckTime()));

        // 转换详细信息
        SourceInfo sourceInfo = SourceInfoConverter.fromJson(entity.getSourceInfo(), entity.getType());
        if (sourceInfo != null) {
            vo.setSourceInfo(sourceInfo);
        }

        return vo;
    }

    private static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATE_TIME_FORMATTER);
    }
} 