package com.trs.police.converter.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.DataSourceType;
import com.trs.police.entity.datasource.EsSourceInfo;
import com.trs.police.entity.datasource.MysqlSourceInfo;
import com.trs.police.entity.datasource.SourceInfo;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


/**
 * SourceInfoDeserializer类用于从JSON数据中反序列化SourceInfo接口的实现类。
 * 该类通过数据源类型来决定使用哪个具体的SourceInfo实现类进行反序列化。
 * 目前支持MySQL和Elasticsearch两种数据源类型的反序列化。
 *
 * <AUTHOR>
 * @date 2023-06-19
 */
public class SourceInfoDeserializer extends JsonDeserializer<SourceInfo> {
    Map<String, Class<? extends SourceInfo>> typeToClassMap = new HashMap<>();

    public SourceInfoDeserializer() {
        typeToClassMap.put(DataSourceType.MYSQL.name(), MysqlSourceInfo.class);
        typeToClassMap.put(DataSourceType.ES.name(), EsSourceInfo.class);
    }


    @Override
    public SourceInfo deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        ObjectMapper mapper = (ObjectMapper) jp.getCodec();
        JsonNode node = mapper.readTree(jp);
        DataSourceType type = getCurrentDataSourceType(ctxt);
        Class<? extends SourceInfo> sourceInfoClass = typeToClassMap.get(type.name().toUpperCase());
        if (sourceInfoClass != null) {
            return mapper.treeToValue(node, sourceInfoClass);
        } else {
            throw new IllegalArgumentException("Unsupported database type: " + type);
        }
    }

    private DataSourceType getCurrentDataSourceType(DeserializationContext ctxt) {
        return ((DataSourceDTO) ctxt.getParser().getParsingContext().getCurrentValue()).getType();
    }

}
