package com.trs.police.utils;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 通用经纬度解析工具类
 * 支持多种输入格式并转换为标准WKT格式
 */
public class GeoPointParser {

    // WKT POINT 格式正则
    private static final Pattern WKT_POINT_PATTERN =
            Pattern.compile("(?i)POINT\\s*[\\[\\(]?\\s*([\\d.-]+)\\s*[, ]\\s*([\\d.-]+)\\s*[\\]\\)]?");

    // 坐标字符串分隔符正则
    private static final Pattern COORD_SPLIT_PATTERN =
            Pattern.compile("[,\\s]+");

    /**
     * 通用解析方法 - 将各种格式的经纬度转换为WKT格式
     *
     * @param geoInput 支持的输入格式包括:
     *                 - WKT格式: "POINT(104.06 30.67)", "POINT (104.06, 30.67)"
     *                 - 字符串坐标: "30.67 104.06", "30.67, 104.06"
     *                 - 数组: new double[]{104.06, 30.67} 或 new Object[]{104.06, 30.67}
     *                 - Map: {"lon":104.06, "lat":30.67}, {"x":104.06, "y":30.67}, {"lng":104.06, "latitude":30.67}
     *                 - Elasticsearch geo_point格式: {"lat":30.67, "lon":104.06}, [104.06,30.67], "30.67,104.06"
     * @return 标准WKT格式字符串 "POINT (104.06 30.67)"
     * @throws IllegalArgumentException 如果输入格式不支持或解析失败
     */
    public static String parseToWkt(Object geoInput) {
        if (geoInput == null) {
            throw new IllegalArgumentException("Geo input cannot be null");
        }

        // 1. 处理已经是WKT格式的情况
        if (geoInput instanceof String) {
            String str = ((String) geoInput).trim();

            // 检查是否是WKT格式
            Matcher m = WKT_POINT_PATTERN.matcher(str);
            if (m.matches()) {
                // 已经是WKT格式，直接规范化输出
                double lon = Double.parseDouble(m.group(1));
                double lat = Double.parseDouble(m.group(2));
                return formatWkt(lon, lat);
            }

            // 检查是否是JSON格式
            if (str.startsWith("{") && str.endsWith("}")) {
                try {
                    // 使用Jackson解析JSON字符串
                    ObjectMapper mapper = new ObjectMapper();
                    Map<?, ?> map = mapper.readValue(str.replaceAll("'", "\""), Map.class);
                    return parseMapToWkt(map);
                } catch (Exception e) {
                    throw new IllegalArgumentException("Invalid number in coordinate string: " + geoInput, e);
                }
            }
            // 处理普通坐标字符串
            return parseStringToWkt(str);
        }

        // 2. 处理数组格式
        if (geoInput.getClass().isArray()) {
            Object[] array;
            if (geoInput instanceof double[]) {
                double[] primitiveArray = (double[]) geoInput;
                array = new Double[]{primitiveArray[0], primitiveArray[1]};
            } else {
                array = (Object[]) geoInput;
            }
            return parseArrayToWkt(array);
        }

        // 3. 处理Map格式
        if (geoInput instanceof Map) {
            return parseMapToWkt((Map<?, ?>) geoInput);
        }

        // 4. 处理List格式
        if (geoInput instanceof List) {
            return parseListToWkt((List<?>) geoInput);
        }

        throw new IllegalArgumentException("Unsupported geo input format: " + geoInput.getClass());
    }

    private static String parseStringToWkt(String coordStr) {
        // 首先尝试匹配标准WKT格式
        Matcher m = WKT_POINT_PATTERN.matcher(coordStr);
        if (m.matches()) {
            double lat = Double.parseDouble(m.group(1));
            double lon = Double.parseDouble(m.group(2));
            return formatWkt(lon, lat);
        }

        // 如果不是标准WKT格式，尝试处理简单坐标字符串
        // 移除所有括号和POINT关键字，只保留坐标部分
        String cleaned = coordStr.replaceAll("(?i)point", "") // 移除POINT关键字
                .replaceAll("[\\(\\)\\[\\]]", "") // 移除各种括号
                .trim();

        // 分割坐标
        String[] parts = COORD_SPLIT_PATTERN.split(cleaned);
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid coordinate string. Expected 2 values but found " +
                    parts.length + " in: " + coordStr);
        }

        try {
            double first = Double.parseDouble(parts[0]);
            double second = Double.parseDouble(parts[1]);
            return formatWkt(second, first); // 第一个是纬度
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid number in coordinate string: " + coordStr, e);
        }
    }

    private static String parseArrayToWkt(Object[] array) {
        if (array.length != 2) {
            throw new IllegalArgumentException("Coordinate array must have exactly 2 elements");
        }

        try {
            double first = toDouble(array[0]);
            double second = toDouble(array[1]);

            // 数组通常假定为 [经度, 纬度] 顺序
            return formatWkt(first, second);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid number in coordinate array", e);
        }
    }

    private static String parseListToWkt(List<?> list) {
        if (list.size() != 2) {
            throw new IllegalArgumentException("Coordinate list must have exactly 2 elements");
        }

        try {
            double first = toDouble(list.get(0));
            double second = toDouble(list.get(1));

            // List通常假定为 [经度, 纬度] 顺序
            return formatWkt(first, second);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid number in coordinate list", e);
        }
    }

    private static String parseMapToWkt(Map<?, ?> map) {
        Double lat = null;
        Double lon = null;

        // 尝试多种可能的键名
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            String key = entry.getKey().toString().toLowerCase();

            if (key.equals("lat") || key.equals("latitude") || key.equals("y")) {
                lat = toDouble(entry.getValue());
            } else if (key.equals("lon") || key.equals("lng") || key.equals("longitude") || key.equals("x")) {
                lon = toDouble(entry.getValue());
            }
        }

        if (lat == null || lon == null) {
            throw new IllegalArgumentException("Map must contain both lat and lon values. Keys found: " + map.keySet());
        }

        return formatWkt(lon, lat);
    }

    private static String formatWkt(double longitude, double latitude) {
        return String.format("POINT (%.6f %.6f)", longitude, latitude);
    }

    private static double toDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid coordinate value: " + value, e);
        }
    }
}
