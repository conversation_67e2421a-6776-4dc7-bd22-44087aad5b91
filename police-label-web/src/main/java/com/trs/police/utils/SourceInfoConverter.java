package com.trs.police.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.entity.datasource.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据源信息转换工具类
 * 处理JSON与各类型数据源信息对象的转换
 *
 * <AUTHOR>
 */
@Slf4j
public class SourceInfoConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 将数据源信息对象转换为JSON字符串
     *
     * @param sourceInfo 数据源信息对象
     * @return JSON字符串
     */
    public static String toJson(SourceInfo sourceInfo) {
        try {
            return OBJECT_MAPPER.writeValueAsString(sourceInfo);
        } catch (JsonProcessingException e) {
            log.error("数据源信息转JSON失败", e);
            return "{}";
        }
    }

    /**
     * 将JSON字符串转换为数据源信息对象
     *
     * @param json JSON字符串
     * @param type 数据源类型
     * @return 数据源信息对象
     */
    public static SourceInfo fromJson(String json, DataSourceType type) {
        try {
            OBJECT_MAPPER.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            switch (type) {
                case ES:
                    return OBJECT_MAPPER.readValue(json, EsSourceInfo.class);
                case MYSQL:
                    return OBJECT_MAPPER.readValue(json, MysqlSourceInfo.class);
                case ORACLE:
                    return OBJECT_MAPPER.readValue(json, OracleSourceInfo.class);
                case HIVE:
                    return OBJECT_MAPPER.readValue(json, HiveSourceInfo.class);
                case HYBASE:
                    return OBJECT_MAPPER.readValue(json, HybaseSourceInfo.class);
                case POSTGRESQL:
                    return OBJECT_MAPPER.readValue(json, PostgresqlSourceInfo.class);
                case CLICKHOUSE:
                    return OBJECT_MAPPER.readValue(json, ClickHouseSourceInfo.class);
                case API:
                    return OBJECT_MAPPER.readValue(json, ApiSourceInfo.class);
                case KAFKA:
                    return OBJECT_MAPPER.readValue(json, KafkaSourceInfo.class);
                default:
                    throw new IllegalArgumentException("不支持的数据源类型: " + type);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转数据源信息失败", e);
            return null;
        }
    }
} 