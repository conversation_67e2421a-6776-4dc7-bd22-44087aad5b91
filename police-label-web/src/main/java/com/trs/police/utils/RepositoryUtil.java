package com.trs.police.utils;

import com.trs.db.sdk.config.DBInfoConfig;
import com.trs.db.sdk.constant.ConfigureConstant;
import com.trs.db.sdk.meta.MetaDataMaintainer;
import com.trs.db.sdk.pojo.RecordInfo;
import com.trs.db.sdk.repository.Repository;
import com.trs.db.sdk.repository.factory.RepositoryFactory;
import com.trs.police.entity.datasource.AbstractDbSourceInfo;

import java.util.HashMap;
import java.util.Map;

import static com.trs.db.sdk.constant.ConfigureConstant.PRINCIPAL;
import static com.trs.db.sdk.constant.ConfigureConstant.USER_KEYTAB_PATH;

/**
 * *@author:wen.wen
 * *@create 2021-04-18 11:48
 **/
public class RepositoryUtil {

    /**
     * 获取数据库DDL连接
     *
     * @param sourceInfo sourceInfo
     * @return MetaDataMaintainer
     */
    public static MetaDataMaintainer getMetaDataMaintainer(AbstractDbSourceInfo sourceInfo) {
        return getRepositoryByDataBaseInfo(sourceInfo).getMetaDataMaintainer();
    }

    /**
     * 获取数据库连接
     *
     * @param sourceInfo sourceInfo
     * @return Repository
     */
    public static Repository<RecordInfo> getRepositoryByDataBaseInfo(AbstractDbSourceInfo sourceInfo){
        DBInfoConfig dbInfoConfig = new DBInfoConfig();
        dbInfoConfig.setDbType(sourceInfo.getDbType());
        dbInfoConfig.setHost(sourceInfo.getHost());
        dbInfoConfig.setPort(sourceInfo.getPort());
        dbInfoConfig.setUserName(sourceInfo.getUserName());
        dbInfoConfig.setPassword(sourceInfo.getPassword());
        dbInfoConfig.setDbName(sourceInfo.getDbName());
        dbInfoConfig.setKerberos(sourceInfo.isKerberos());
        dbInfoConfig.setCloudType(ConfigureConstant.CLOUD_TYPE_HUAWEI);
        Map<String, String> customHeaders =new HashMap<>();
        customHeaders.put(USER_KEYTAB_PATH,sourceInfo.getUserKerTabPath());
        customHeaders.put(PRINCIPAL,sourceInfo.getPrincipal());
        customHeaders.put("KRB5CONF_PATH",sourceInfo.getKrb5ConfPath());
        dbInfoConfig.setCustomHeaders(customHeaders);
        return RepositoryFactory.getRepository(dbInfoConfig);
    }
}
