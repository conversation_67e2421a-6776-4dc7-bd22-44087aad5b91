package com.trs.police.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 周期时间工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class CycleTimeUtils {

    /**
     * 周期时间类型常量
     */
    public static final int CYCLE_TYPE_SECOND = 1; // 秒
    public static final int CYCLE_TYPE_MINUTE = 2; // 分
    public static final int CYCLE_TYPE_HOUR = 3;   // 时
    public static final int CYCLE_TYPE_DAY = 4;    // 天
    public static final int CYCLE_TYPE_MONTH = 5;  // 月

    /**
     * 计算下次执行时间
     *
     * @param lastExecuteTime 上次执行时间
     * @param cycleTimeType 周期时间类型
     * @param cycleTime 周期时间值
     * @return 下次执行时间
     */
    public static LocalDateTime calculateNextExecuteTime(LocalDateTime lastExecuteTime, 
                                                        Integer cycleTimeType, 
                                                        Integer cycleTime) {
        if (lastExecuteTime == null || cycleTimeType == null || cycleTime == null || cycleTime <= 0) {
            return LocalDateTime.now();
        }

        try {
            switch (cycleTimeType) {
                case CYCLE_TYPE_SECOND:
                    return lastExecuteTime.plusSeconds(cycleTime);
                case CYCLE_TYPE_MINUTE:
                    return lastExecuteTime.plusMinutes(cycleTime);
                case CYCLE_TYPE_HOUR:
                    return lastExecuteTime.plusHours(cycleTime);
                case CYCLE_TYPE_DAY:
                    return lastExecuteTime.plusDays(cycleTime);
                case CYCLE_TYPE_MONTH:
                    return lastExecuteTime.plusMonths(cycleTime);
                default:
                    log.warn("未知的周期时间类型: {}, 使用默认1小时", cycleTimeType);
                    return lastExecuteTime.plusHours(1);
            }
        } catch (Exception e) {
            log.error("计算下次执行时间失败: lastExecuteTime={}, cycleTimeType={}, cycleTime={}", 
                     lastExecuteTime, cycleTimeType, cycleTime, e);
            return LocalDateTime.now().plusHours(1);
        }
    }

    /**
     * 检查是否需要执行
     *
     * @param lastExecuteTime 上次执行时间
     * @param cycleTimeType 周期时间类型
     * @param cycleTime 周期时间值
     * @return 是否需要执行
     */
    public static boolean shouldExecute(LocalDateTime lastExecuteTime, 
                                       Integer cycleTimeType, 
                                       Integer cycleTime) {
        // 从未执行过，需要执行
        if (lastExecuteTime == null) {
            return true;
        }
        // 上一次还在运行中
        if(lastExecuteTime.isAfter(LocalDateTime.now())){
            return false;
        }

        LocalDateTime nextExecuteTime = calculateNextExecuteTime(lastExecuteTime, cycleTimeType, cycleTime);
        return LocalDateTime.now().isAfter(nextExecuteTime) || LocalDateTime.now().isEqual(nextExecuteTime);
    }

    /**
     * 将周期时间转换为毫秒
     *
     * @param cycleTimeType 周期时间类型
     * @param cycleTime 周期时间值
     * @return 毫秒数
     */
    public static long toMilliseconds(Integer cycleTimeType, Integer cycleTime) {
        if (cycleTimeType == null || cycleTime == null || cycleTime <= 0) {
            return 0;
        }

        try {
            switch (cycleTimeType) {
                case CYCLE_TYPE_SECOND:
                    return cycleTime * 1000L;
                case CYCLE_TYPE_MINUTE:
                    return cycleTime * 60 * 1000L;
                case CYCLE_TYPE_HOUR:
                    return cycleTime * 60 * 60 * 1000L;
                case CYCLE_TYPE_DAY:
                    return cycleTime * 24 * 60 * 60 * 1000L;
                case CYCLE_TYPE_MONTH:
                    // 按30天计算
                    return cycleTime * 30L * 24 * 60 * 60 * 1000L;
                default:
                    return 0;
            }
        } catch (Exception e) {
            log.error("转换周期时间为毫秒失败: cycleTimeType={}, cycleTime={}", cycleTimeType, cycleTime, e);
            return 0;
        }
    }

    /**
     * 获取周期时间类型描述
     *
     * @param cycleTimeType 周期时间类型
     * @return 描述
     */
    public static String getCycleTypeDescription(Integer cycleTimeType) {
        if (cycleTimeType == null) {
            return "未知";
        }

        switch (cycleTimeType) {
            case CYCLE_TYPE_SECOND:
                return "秒";
            case CYCLE_TYPE_MINUTE:
                return "分钟";
            case CYCLE_TYPE_HOUR:
                return "小时";
            case CYCLE_TYPE_DAY:
                return "天";
            case CYCLE_TYPE_MONTH:
                return "月";
            default:
                return "未知";
        }
    }

    /**
     * 格式化周期时间描述
     *
     * @param cycleTimeType 周期时间类型
     * @param cycleTime 周期时间值
     * @return 格式化描述
     */
    public static String formatCycleTime(Integer cycleTimeType, Integer cycleTime) {
        if (cycleTimeType == null || cycleTime == null) {
            return "未设置";
        }

        return cycleTime + getCycleTypeDescription(cycleTimeType);
    }

    /**
     * 计算两个时间之间的间隔描述
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 间隔描述
     */
    public static String formatDuration(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return "未知";
        }

        long seconds = ChronoUnit.SECONDS.between(startTime, endTime);
        
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        } else if (seconds < 86400) {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            return hours + "小时" + minutes + "分";
        } else {
            long days = seconds / 86400;
            long hours = (seconds % 86400) / 3600;
            return days + "天" + hours + "小时";
        }
    }
}
