package com.trs.police.utils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;

/**
 * AES加解密工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class AesUtils {

    private AesUtils() {
    }

    private static final String ALGORITHM = "AES";
    /**
     * 使用 ECB 模式
     */
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    public static final String DEFAULT_KEY = "trs-moye12345678";

    /**
     * aes加密
     *
     * @param rawText 明文
     * @param key     密钥
     * @return 加密后字符串
     */
    public static String encrypt(String rawText, String key) {
        try {
            byte[] keyBytes = key.length() == 16 ? key.getBytes(StandardCharsets.UTF_8) : HashUtils.sha256ToBytes(key);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            byte[] encryptedBytes = cipher.doFinal(rawText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("Failed to encrypt text", e);
            return rawText;
        }
    }

    /**
     * aes解密
     *
     * @param encryptedText 加密后的密码
     * @param key           密钥
     * @return 解密后字符串
     */
    public static String decrypt(String encryptedText, String key) {
        try {
            byte[] keyBytes = key.length() == 16 ? key.getBytes(StandardCharsets.UTF_8) : HashUtils.sha256ToBytes(key);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);
            byte[] originalBytes = cipher.doFinal(decodedBytes);

            return new String(originalBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Failed to decrypt text", e);
            return encryptedText;
        }
    }

    /**
     * aes默认密钥解密
     *
     * @param encryptedText 加密后的密码
     * @return 解密后字符串
     */
    public static String decrypt(String encryptedText) {
        return decrypt(encryptedText, DEFAULT_KEY);
    }
}
