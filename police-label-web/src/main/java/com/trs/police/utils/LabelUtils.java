package com.trs.police.utils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.trs.police.dto.label.LabelListDto;
import com.trs.police.service.shared.dict.DictFacade;
import com.trs.police.service.shared.dict.DictItemDTO;
import com.trs.police.service.shared.dict.DictTypeRegistry;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 表达式工具类
 */
public class LabelUtils {

    /**
     * 获取种类code到叶子节点的code
     *
     * @param dto dto
     * @param dictFacade dictFacade
     * @param dictTypeRegistry dictTypeRegistry
     */
    public static void initListDto(LabelListDto dto, DictFacade dictFacade, DictTypeRegistry dictTypeRegistry) {
        if (Objects.isNull(dto.getCategoryCode())){
            return;
        }
        List<DictItemDTO> dictList = dictFacade.getDicts(dictTypeRegistry.getLabelCategory());
        if (CollectionUtils.isEmpty(dictList)) {
            return;
        }
        List<Long> categoryCodeList = new ArrayList<>();
        Long categoryCode = dto.getCategoryCode();
        categoryCodeList.add(categoryCode);
        for (DictItemDTO dictItemDTO : dictList) {
            if (categoryCode.equals(dictItemDTO.getPCode())){
                categoryCode = dictItemDTO.getCode();
                categoryCodeList.add(categoryCode);
            }
        }
        dto.setCategoryCodeList(categoryCodeList);
        dto.setCategoryCode(null);
    }
}
