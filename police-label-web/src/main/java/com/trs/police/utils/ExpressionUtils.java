package com.trs.police.utils;

import com.trs.common.pojo.OrderDTO;
import com.trs.common.utils.expression.Expression;
import com.trs.police.constant.FieldConstant;
import com.trs.police.dto.DataTableOverviewDto;
import com.trs.web.entity.Order;
import com.trs.web.entity.PageInfo;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 表达式工具类
 */
public class ExpressionUtils {


    /**
     * 查询Expression
     *
     * @param dto dto
     * @return Expression
     */
    public static Expression generateExpression(DataTableOverviewDto dto) {
        if (CollectionUtils.isEmpty(dto.getOrderList())){
            return null;
        }
        return null;
    }

    /**
     * pageInfo
     *
     * @param dto dto
     * @return pageInfo
     */
    public static PageInfo generatePageInfo(DataTableOverviewDto dto) {
        PageInfo pageInfo = PageInfo.newPage(dto.getPageNum(), dto.getPageSize());
        if (CollectionUtils.isEmpty(dto.getOrderList())){
            return pageInfo;
        }
        List<Order> orders = new ArrayList<>();
        for (OrderDTO orderInfo : dto.getOrderList()) {
            Order order = FieldConstant.ASC.equals(orderInfo.getOrderType())
                    ? Order.asc(orderInfo.getFieldName()) : Order.desc(orderInfo.getFieldName());
            orders.add(order);
        }
        pageInfo.setOrders(orders);
        return pageInfo;
    }
}
