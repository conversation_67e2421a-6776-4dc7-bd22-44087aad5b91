package com.trs.police.utils;

import java.nio.charset.StandardCharsets;

/**
 * java类型工具
 *
 * <AUTHOR>
 * @since 2024-05-25 18:32
 */
public class HexUtils {

    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    private HexUtils() {
    }

    /**
     * 字节数组转16进制字符串
     *
     * @param bytes 字节数组
     * @return 16进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int i = 0; i < bytes.length; i++) {
            int v = bytes[i] & 0xFF;
            hexChars[i * 2] = HEX_ARRAY[v >>> 4];
            hexChars[i * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * 16进制加密
     *
     * @param plaintext 字节数组
     * @return 16进制字符串
     */
    public static String encrypt(String plaintext) {
        return bytesToHex(plaintext.getBytes());
    }

    /**
     * 16进制解密
     *
     * @param hexString 16进制字符串
     * @return 原始明文字符串
     * @throws IllegalArgumentException 输入不符合16进制格式时抛出
     */
    public static String decrypt(String hexString) {
        // 空值校验
        if (hexString == null || hexString.isEmpty()) {
            return "";
        }
        // 有效性校验：长度必须是偶数
        if (hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Invalid hex string length");
        }
        // 转换为字节数组
        byte[] bytes = new byte[hexString.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int high = Character.digit(hexString.charAt(i * 2), 16);
            int low = Character.digit(hexString.charAt(i * 2 + 1), 16);
            // 有效性校验：字符必须在0-9/A-F/a-f范围内
            if (high == -1 || low == -1) {
                throw new IllegalArgumentException("Contains invalid hex characters");
            }
            bytes[i] = (byte) ((high << 4) | low);
        }
        // 还原原始字符串（需与加密时使用的字符编码一致）
        return new String(bytes, StandardCharsets.UTF_8);
    }
}
