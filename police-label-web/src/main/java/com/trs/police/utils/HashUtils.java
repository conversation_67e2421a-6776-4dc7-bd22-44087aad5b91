package com.trs.police.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 哈希码工具类
 *
 * <AUTHOR>
 * @since 2025-05-09 16:21
 */
public class HashUtils {

    private HashUtils() {
    }

    /**
     * SHA256加密算法
     *
     * @param text 文本
     * @return SHA256加密后的字节数组
     */
    public static byte[] sha256ToBytes(String text) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            return digest.digest(text.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有发现SHA-256算法 ", e);
        }
    }

    /**
     * SHA256加密算法
     *
     * @param text 文本
     * @return SHA256加密后的字节数组
     */
    public static String sha256ToHexString(String text) {
        return HexUtils.bytesToHex(sha256ToBytes(text));
    }

}
