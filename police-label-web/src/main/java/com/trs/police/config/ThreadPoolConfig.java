package com.trs.police.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    /**
     * 数据源处理线程池
     *
     * @return Executor
     */
    @Bean("dataSourceTaskExecutor")
    public Executor dataSourceTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
//        executor.setCorePoolSize(BeanFactoryHolder.getEnv().getProperty("dataSourceTaskExecutor.corePoolSize", Integer.class, 100));
        executor.setCorePoolSize(100);
        // 最大线程数
//        executor.setMaxPoolSize(BeanFactoryHolder.getEnv().getProperty("dataSourceTaskExecutor.maxPoolSize", Integer.class, 100));
        executor.setMaxPoolSize(100);
        // 队列容量
//        executor.setQueueCapacity(BeanFactoryHolder.getEnv().getProperty("dataSourceTaskExecutor.queueCapacity", Integer.class, 1000));
        executor.setQueueCapacity(1000);
        // 线程名前缀
        executor.setThreadNamePrefix("data-source-async-");
        // 线程存活时间
//        executor.setKeepAliveSeconds(BeanFactoryHolder.getEnv().getProperty("dataSourceTaskExecutor.keepAliveSeconds", Integer.class, 600));
        executor.setKeepAliveSeconds(600);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 设置最大等待时间
//        executor.setAwaitTerminationSeconds(BeanFactoryHolder.getEnv().getProperty("dataSourceTaskExecutor.awaitTerminationSeconds", Integer.class, 1800));
        executor.setAwaitTerminationSeconds(1800);
        executor.initialize();
        return executor;
    }
} 