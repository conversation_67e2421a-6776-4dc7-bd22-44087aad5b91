package com.trs.police.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * security配置
 *
 * <AUTHOR>
 * @date 2022/02/18
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/websocket/**");
        //暂时不用登录，接口开发完成后需要登陆
//        web.ignoring().antMatchers("/api/**");
        web.ignoring().antMatchers("/public/**");
        web.ignoring().antMatchers("/druid/**");
    }
}
