package com.trs.police.config;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.PreConditionCheck;
import com.trs.db.sdk.config.DBInfoConfig;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.DataSourceType;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 数据源配置类
 */
@Component
public class DbSdkConfig {


    /**
     * 根据类型获取配置
     *
     * @param dbType es、MySQL等
     * @param dataSource 数据源
     * @return 配置
     */
    public DBInfoConfig getDbInfoConfig(DataSourceType dbType, DataSource dataSource) {
        PreConditionCheck.checkNotEmpty(dataSource.getSourceInfo(),"数据源信息不能为空");
        PreConditionCheck.checkNotNull(dbType, "dbType 不能为空");
        JSONObject sourceInfo = JSONObject.parseObject(dataSource.getSourceInfo());
        DBInfoConfig dbInfoConfig = new DBInfoConfig();
        switch (dbType) {
            case ES:
            case MYSQL:
                setBasicInfo(dbInfoConfig, sourceInfo);
                break;
            default:
                setBasicInfo(dbInfoConfig, sourceInfo);
                break;
        }
        return dbInfoConfig;
    }

    private static void setBasicInfo(DBInfoConfig dbInfoConfig, JSONObject sourceInfo) {
        dbInfoConfig.setDbType(sourceInfo.getString("dbType"));
        dbInfoConfig.setHost(sourceInfo.getString("host"));
        dbInfoConfig.setPort(sourceInfo.getString("port"));
        dbInfoConfig.setUserName(sourceInfo.getString("userName"));
        dbInfoConfig.setPassword(sourceInfo.getString("password"));
        dbInfoConfig.setDbName(sourceInfo.getString("dbName"));
        dbInfoConfig.setKerberos(sourceInfo.getBoolean("kerberos"));
        dbInfoConfig.setCloudType(sourceInfo.getString("cloudType"));
        dbInfoConfig.setCustomHeaders(sourceInfo.getObject("customHeaders", Map.class));
    }
}
