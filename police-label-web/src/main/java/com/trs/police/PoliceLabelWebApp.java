package com.trs.police;

import com.trs.police.common.core.annotation.EnablePoliceCloudResourceServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;

/**
 * 权限中心
 *
 * <AUTHOR>
 * @date 2022/06/04
 */

@SpringBootApplication
@EnablePoliceCloudResourceServer
@EnableCaching
@ComponentScan("com.trs.police")
@ComponentScan("com.trs.police.common")
@ComponentScan("com.trs.web.builder.util")
@ComponentScan("com.trs.tcache.conf")
@ComponentScan("com.trs.db.sdk")
@ComponentScan("com.trs.db.sdk.annotations.analysis.impl")
@ServletComponentScan
public class PoliceLabelWebApp {

    /**
     * 权限中心应用程序
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(PoliceLabelWebApp.class);
    }

}
