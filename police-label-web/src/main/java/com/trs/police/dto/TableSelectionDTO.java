package com.trs.police.dto;

import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TableSelectionDTO extends BaseDTO {
    /**
     * 数据源id
     */
    private Long dataSourceId;
    /**
     * 已选状态
     */
    private Integer selectedStatus;
    /**
     * 搜索值
     */
    private String searchValue;
    /**
     * 已选的id
     */
    private String selectedIds;
    /**
     * 未选的id
     */
    private String unselectedIds;
}
