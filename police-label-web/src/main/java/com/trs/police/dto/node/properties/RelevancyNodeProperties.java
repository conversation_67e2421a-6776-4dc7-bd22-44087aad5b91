package com.trs.police.dto.node.properties;

import com.trs.police.dto.node.ValueMateBase;
import com.trs.police.service.node.rowmatch.MatchConfig;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 关联配置类
 */
@Data
@NoArgsConstructor
public class RelevancyNodeProperties extends NodeProperties {

    /**
     * 来源表格A
     */
    private String tableA;

    /**
     * 来源表格B
     */
    private String tableB;

    /**
     * 关联类型：1 交集 2 并集（不去重） 3 并集（去重）4 差集
     */
    private Integer type;

    /**
     * 匹配字段配置
     */
    private List<MatchConfig> match;

    /**
     * 权重设置：1 选a 2 选b 3 执行比较取大 4 执行比大取小
     */
    private Integer weight;

    /**
     * 比较字段配xx置
     */
    private List<CompareFieldConfig> compareField;

    /**
     * Compare字段配置项
     */
    @Data
    public static class CompareFieldConfig {

        /**
         * 表A字段信息
         */
        private ValueMateBase a;

        /**
         * 表B字段信息
         */
        private ValueMateBase b;
    }
}
