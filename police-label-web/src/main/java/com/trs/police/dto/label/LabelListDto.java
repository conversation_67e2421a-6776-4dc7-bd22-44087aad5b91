package com.trs.police.dto.label;

import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标签列表DTO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelListDto extends BaseDTO {
    private Long categoryCode;     // 所属分类(码表code)
    private Integer policeKind;       // 所属警种(码表code)
    private Integer updateStatus;     // 更新状态：0-成功 1-等待计算 2-延迟计算
    private Integer updateMethod;     // 更新方法：0-手动 1-例行
    private Integer status;           // 状态：0-停用 1-启用
    private String createStartTime;  // 创建开始时间
    private String createEndTime;    // 创建结束时间
    private String searchKey;        // 标签名称(labelName)
    private String searchValue;     // 标签值
    private List<Long> categoryCodeList;
    private Long mainObjectCode; // 主对象编码
}
