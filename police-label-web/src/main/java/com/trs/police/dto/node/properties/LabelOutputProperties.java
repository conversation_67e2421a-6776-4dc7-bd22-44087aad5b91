package com.trs.police.dto.node.properties;

import com.trs.common.utils.TimeUtils;
import com.trs.police.constant.label.LabelConstant;
import com.trs.police.constant.label.TimeType;
import com.trs.police.dto.node.properties.bean.MainObject;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 标签输出节点属性
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelOutputProperties extends NodeProperties {

    /**
     * 标签主体
     */
    private List<MainObject> mainObject;

    /**
     * 标签客体
     */
    private List<MainObject> relatedObject;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 打标方式 0 手动{@link LabelConstant#SD} 1 自动 {@link LabelConstant#LX}
     */
    private Integer labelType;

    /**
     * 周期时间类型 秒 1 分 2 时 3 天 4 月 5 {@link TimeType}
     */
    private Integer cycleTimeType;

    /**
     * 周期时间
     */
    private Integer cycleTime;

    /**
     * 有效时间方式 0 永久 1 期限
     */
    private Integer effectiveTimeType;

    /**
     * 有效时间天数
     */
    private Integer effectiveTime;

    /**
     * 过期时间
     */
    private String expireTime;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签英文名
     */
    private String enName;

    /**
     * 警种
     */
    private Long policeKind;

    /**
     * 业务规则描述
     */
    private String businessRule;

    /**
     * 分类
     */
    private Long categoryCode;

    /**
     * 颜色
     */
    private String color;

    /**
     * 打标对象列名
     */
    private String objectNumberCol;

    /**
     * 数据源id列名
     */
    private String sourceDataIdCol;

    /**
     * 客体对象列名
     */
    private String relatedObjectNumberCol;

    /**
     * 打标类型
     */
    private Integer objectType;

    /**
     * 设置过期时间
     */
    public void setExpireTime(){
        if(effectiveTimeType!=null && effectiveTime!=null && effectiveTimeType == 1){
            this.expireTime = TimeUtils.dateBefOrAft(new Date(), effectiveTime, TimeUtils.YYYYMMDD_HHMMSS);
        }
    }
}
