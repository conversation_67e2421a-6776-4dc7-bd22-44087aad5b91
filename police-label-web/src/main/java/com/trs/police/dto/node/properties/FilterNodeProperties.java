package com.trs.police.dto.node.properties;

import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.ValueWrapper;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 过滤节点参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FilterNodeProperties extends NodeProperties {

    /**
     * token 如：["(",{"key":"g_ch_key","operator":"in","value":["1"]},")","或",{"key":"g_adp","operator":"in","value":["22"]}]
     */
    private String[] tokens;


    /**
     * 条件
     *
     * <AUTHOR>
     */
    @Data
    @NoArgsConstructor
    public static class Condition {

        /**
         * 来源节点（uid）
         */
        private String fromNode;

        /**
         * 字段英文名
         */
        private String key;

        /**
         * 操作类型
         */
        private String operator;

        /**
         * 值
         */
        private ValueWrapper value;

        /**
         * 获取id
         *
         * @return id
         */
        public String getId() {
            return new FieldInfoVO(null, key, null, fromNode).getId();
        }

    }
}
