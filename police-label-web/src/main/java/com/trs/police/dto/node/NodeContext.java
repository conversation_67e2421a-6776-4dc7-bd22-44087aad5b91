package com.trs.police.dto.node;

import com.trs.police.common.core.vo.node.ValueWrapper;
import lombok.Data;

import java.util.*;

/**
 * 节点预览上下文
 *
 * <AUTHOR>
 */
@Data
public class NodeContext {

    /**
     * 控制参数
     */
    private List<ControlDTO> control;

    /**
     * 控制参数填写的值
     */
    private Map<String, ValueWrapper> controlValueMap;

    /**
     * 所有节点列表
     */
    private List<NodeDTO> nodes;

    /**
     * 节点编排信息
     */
    private List<OrderDTO> nodeOrders;

    public NodeContext(List<ControlDTO> control) {
        this.control = control;
        this.controlValueMap = new HashMap<>();
    }

    /**
     * 获取控制参数的值
     *
     * @param name name
     * @return Value
     */
    public Optional<ValueWrapper> getValue(String name) {
        if (Objects.isNull(controlValueMap)) {
            return Optional.empty();
        }
        return Optional.ofNullable(controlValueMap.get(name));
    }

    /**
     * 添加控制参数的值
     *
     * @param name name
     * @param value value
     */
    public void addControlValue(String name, ValueWrapper value) {
        if (Objects.isNull(controlValueMap)) {
            controlValueMap = new HashMap<>();
        }
        controlValueMap.put(name, value);
    }
}
