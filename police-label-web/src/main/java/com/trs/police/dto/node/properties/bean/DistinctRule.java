package com.trs.police.dto.node.properties.bean;

import com.trs.police.dto.node.ValueMateBase;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 去重字段
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DistinctRule extends ValueMateBase{

    /**
     * 去重类型
     */
    private String order;

    /**
     * 是否是降序
     *
     * @return 布尔
     */
    public Boolean isDesc() {
        return "DESC".equalsIgnoreCase(order);
    }

    /**
     * 获取字段信息
     *
     * @return 字段信息
     */
    public ValueMateBase getFiled() {
        return this;
    }
}
