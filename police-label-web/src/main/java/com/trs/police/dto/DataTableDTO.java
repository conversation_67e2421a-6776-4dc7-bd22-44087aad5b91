package com.trs.police.dto;

import com.trs.police.entity.datasource.DataSourceType;
import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据表DTO基类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DataTableDTO extends BaseDTO {

    private Long id;

    private String tableNameCn;

    private String aliaseName;

    private DataSourceType type;

    private Integer selectedStatus;

    private List<OrderInfo> orderList;

    /**
     * 排序信息
     */
    @Data
    public static class OrderInfo{
        /**
         * 字段名称
         */
        private String fieldName;
        /**
         * 排序类型 asc/desc
         */
        private String orderType;
    }

}
