package com.trs.police.dto.label;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标签
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelOutputDTO{

    /**
     * 标签主体
     */
    private List<LabelMainObject> mainObject;

    /**
     * 标签客体
     */
    private List<LabelMainObject> relatedObject;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 打标方式 0 手动 1 自动
     */
    private Integer labelType;

    /**
     * 周期时间类型 秒 1 分 2 时 3 天 4 月 5
     */
    private Integer cycleTimeType;

    /**
     * 周期时间
     */
    private Integer cycleTime;

    /**
     * 有效时间方式 0 永久 1 期限
     */
    private Integer effectiveTimeType;

    /**
     * 有效时间天数
     */
    private Integer effectiveTime;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签英文名
     */
    private String enName;

    /**
     * 警种
     */
    private Long policeKind;

    /**
     * 业务规则描述
     */
    private String businessRule;

    /**
     * 分类
     */
    private Long categoryCode;

    /**
     * 颜色
     */
    private String color;
}
