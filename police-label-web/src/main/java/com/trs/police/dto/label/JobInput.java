package com.trs.police.dto.label;

import lombok.Data;

/**
 * 提交任务参数
 *
 *
 */
@Data
public class JobInput {

    /**
     * 任务名称 不能为空
     */
    private String jobName;

    /**
     * jar包的路径，可以为空
     */
    private String jarPath;

    /**
     * jar包的名字，可以为空
     */
    private String jarFileName;

    /**
     * Spark程序执行的目标类，不能为空
     */
    private String mainClass;

    /**
     * 输入的额外参数 json结构 可以为空
     */
    private String inputParams;
}
