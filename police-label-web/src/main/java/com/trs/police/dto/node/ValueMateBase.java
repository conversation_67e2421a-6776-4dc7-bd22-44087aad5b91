package com.trs.police.dto.node;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ValueMateBase {

    /**
     * 来源节点 uuid
     */
    private String fromNode;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 名称
     */
    private String enName;

    /**
     * 类型编码 {@link DataBaseFieldMappingType}
     */
    private String typeCode;

    public ValueMateBase(String fromNode, String cnName, String enName, String typeCode) {
        this.fromNode = fromNode;
        this.cnName = cnName;
        this.enName = enName;
        this.typeCode = typeCode;
    }

    /**
     * 唯一值
     *
     * @return 唯一值id
     */
    public String getId() {
        return String.format("%s.%s", fromNode, enName);
    }

    /**
     * 唯一列
     *
     * @return 唯一列
     */
    public String getCol() {
        if(StringUtils.isEmpty(fromNode)){
            return enName;
        }
        return getId().replaceAll("-","_");
    }
}
