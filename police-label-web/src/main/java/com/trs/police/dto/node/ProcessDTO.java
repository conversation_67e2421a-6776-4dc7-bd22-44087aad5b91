package com.trs.police.dto.node;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 流程参数
 */
@Data
@NoArgsConstructor
public class ProcessDTO {

    /**
     *  节点列表
     */
    private List<NodeDTO> nodes;

    /**
     * 节点编排信息
     */
    private List<OrderDTO> nodeOrders;

    /**
     * 控制参数
     */
    private List<ControlDTO> control;

    /**
     * 当前节点名称
     */
    private String currentNode;

    /**
     * 标签ID
     */
    private Long labelId;

    /**
     * 拷贝
     *
     * @return 浅拷贝对象
     */
    public ProcessDTO copy() {
        ProcessDTO clone = new ProcessDTO();
        clone.setNodes(this.nodes);
        clone.setNodeOrders(this.nodeOrders);
        clone.setControl(this.control);
        clone.setCurrentNode(this.currentNode);
        return clone;
    }
}
