package com.trs.police.dto.node;

import com.trs.police.common.core.dto.Position;
import com.trs.police.constant.NodeType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.UUID;

import static com.trs.police.constant.NodeType.FEATURE_IN;

/**
 * 节点
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class NodeMeta {

    /**
     * 节点名称 (单个流程中必须唯一)
     */
    private String name;

    /**
     * 节点唯一值
     */
    private String uuid;

    /**
     * 节点类型编码 {@link NodeType}
     */
    private Integer nodeTypeCode;

    /**
     * 位置信息
     */
    private Position position;

    /**
     * 输出行信息
     */
    private RowMeta outputRowMeta;


    /**
     * 初始化uuid
     */
    public void initUid() {
        if (null == uuid || uuid.isEmpty()) {
            this.uuid = UUID.randomUUID().toString().replaceAll("-", "_");
        }
    }

    /**
     * 由于前端在输入节点上没有给fromNode赋值，临时使用这个方法赋值
     *
     * @return 是否是输入节点
     */
    @Deprecated
    public Boolean isInputNode() {
        return Arrays.asList(NodeType.TABLE, FEATURE_IN).contains(nodeTypeCode);
    }
}
