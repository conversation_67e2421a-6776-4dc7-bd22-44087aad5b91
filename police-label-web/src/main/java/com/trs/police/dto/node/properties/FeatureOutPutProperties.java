package com.trs.police.dto.node.properties;

import com.trs.police.dto.node.FeatureBaseInfo;
import com.trs.police.dto.node.properties.bean.MainObject;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 特征输出节点
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FeatureOutPutProperties extends NodeProperties {

    /**
     * 特征基础信息
     */
    private FeatureBaseInfo baseInfo;

    /**
     * 特征主体
     */
    private List<MainObject> mainObject;
}
