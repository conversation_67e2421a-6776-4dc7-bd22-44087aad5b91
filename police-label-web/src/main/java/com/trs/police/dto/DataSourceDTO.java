package com.trs.police.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.police.converter.json.SourceInfoDeserializer;
import com.trs.police.entity.datasource.DataSourceType;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据源DTO基类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DataSourceDTO extends BaseDTO {

    /**
     * 获取数据源详细信息
     * 由子类实现，返回对应类型的SourceInfo
     */
    @NotNull(message = "数据源详细信息不能为空")
    @JsonDeserialize(using = SourceInfoDeserializer.class)
    public SourceInfo sourceInfo;
    /**
     * 数据源ID (更新时使用)
     */
    private Long id;
    /**
     * 数据源名称
     */
    @NotBlank(message = "数据源名称不能为空")
    private String name;

    /**
     * 数据源创建状态
     */
    private Integer creationStatus;

    /**
     * 模糊搜索
     */
    private String searchValue;

    /**
     * 数据源连接状态
     */
    private String connectionStatus;

    /**
     * 数据源类型
     */
    @NotNull(message = "数据源类型不能为空")
    private DataSourceType type;

    /**
     * 来源 0 默认 1 中台
     */
    private Integer sourceFrom;

    public DataSourceDTO(Long id, String name, DataSourceType type, SourceInfo sourceInfo) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.sourceInfo = sourceInfo;
    }

} 