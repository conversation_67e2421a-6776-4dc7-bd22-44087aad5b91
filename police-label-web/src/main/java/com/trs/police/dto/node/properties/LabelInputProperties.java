package com.trs.police.dto.node.properties;

import com.trs.police.dto.node.properties.bean.ControlValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标签输入节点
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelInputProperties extends NodeProperties {

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 控制参数的值
     */
    private List<ControlValue> controlValue;
}
