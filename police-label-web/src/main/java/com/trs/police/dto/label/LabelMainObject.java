package com.trs.police.dto.label;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标签主体
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelMainObject {

    private String enName;

    private String fromNode;

    private Long mainObjectTypeCode;

    public LabelMainObject(String enName, String fromNode, Long mainObjectTypeCode) {
        this.enName = enName;
        this.fromNode = fromNode;
        this.mainObjectTypeCode = mainObjectTypeCode;
    }
}
