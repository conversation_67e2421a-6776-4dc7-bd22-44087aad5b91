package com.trs.police.dto.node.properties;

import com.trs.police.dto.node.properties.bean.ControlValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 特征输入节点配置
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FeatureInputProperties extends NodeProperties {

    /**
     * 特征id
     */
    private Long featureId;

    /**
     * 控制参数的值
     */
    private List<ControlValue> controlValue;
}
