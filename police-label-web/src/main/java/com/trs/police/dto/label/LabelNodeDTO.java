package com.trs.police.dto.label;

import com.trs.police.vo.RowFieldVo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class LabelNodeDTO implements Serializable {
    /**
     * 节点类型
     */
    private Integer nodeType;
    /**
     * 节点nodeProperties转json
     */
    private String nodeProperties;
    /**
     * 输出的字段
     */
    private List<RowFieldVo> outputRow;
    /**
     * flow
     */
    private List<LabelNodeDTO> flow;

    /**
     * 上一个节点id
     */
    private String lastNodeId;

    public LabelNodeDTO(Integer nodeType, String nodeProperties, List<RowFieldVo> outputRow, String lastNodeId) {
        this.nodeType = nodeType;
        this.nodeProperties = nodeProperties;
        this.outputRow = outputRow;
        this.lastNodeId = lastNodeId;
    }
}
