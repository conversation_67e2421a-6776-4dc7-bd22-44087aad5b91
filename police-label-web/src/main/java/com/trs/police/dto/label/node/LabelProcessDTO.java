package com.trs.police.dto.label.node;

import com.trs.police.dto.node.ControlDTO;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.OrderDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *  标签流程DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelProcessDTO {

    /**
     *  基本信息
     */
    private LabelBaseInfo info;

    /**
     *  节点列表
     */
    private List<NodeDTO> nodes;

    /**
     * 节点编排信息
     */
    private List<OrderDTO> nodeOrders;

    /**
     * 控制参数
     */
    private List<ControlDTO> control;

    /**
     * 是否是手动
     */
    private Boolean manual;

}
