package com.trs.police.entity.datasource;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * MySQL数据源信息
 *
 * <AUTHOR>
 */
@Data
public class MysqlSourceInfo extends AbstractDbSourceInfo {

    @Override
    public DataSourceType getType() {
        return DataSourceType.MYSQL;
    }


    @Override
    public String generateUniqueId() {
        //数据库类型的数据源按驱动信息（如host、port、database）+url 排重创建
        StringBuilder builder = new StringBuilder();
        builder.append(super.getDbType()).append(":");
        if (StringUtils.hasText(super.getHost())) {
            builder.append(super.getHost()).append(":");
        }
        if (super.getPort() != null) {
            builder.append(super.getPort()).append("/");
        }
        if (StringUtils.hasText(super.getDbName())) {
            builder.append(super.getDbName());
        }
        return builder.toString();
    }
}