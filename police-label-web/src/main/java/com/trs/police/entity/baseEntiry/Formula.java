package com.trs.police.entity.baseEntiry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 字典表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "tb_formula")
public class Formula implements Serializable {

    private static final long serialVersionUID = 8164142332012636899L;
    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 公式名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 语法
     */
    @TableField(value = "syntax")
    private String syntax;

    /**
     * 示例
     */
    @TableField(value = "example")
    private String example;

    /**
     * 参数
     */
    @TableField(value = "parameters")
    private String parameters;

    /**
     * 返回类型
     */
    @TableField(value = "return_type")
    private String returnType;

    /**
     * 分组类型
     */
    @TableField(value = "category")
    private String category;

    /**
     * 排序值
     */
    @TableField(value = "order_value")
    private Integer orderValue;
}
