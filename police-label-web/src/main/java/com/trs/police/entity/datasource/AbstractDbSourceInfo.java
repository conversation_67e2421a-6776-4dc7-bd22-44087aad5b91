package com.trs.police.entity.datasource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 抽象数据库数据源信息类，提供了数据库连接所需的基本信息。
 * 该类实现了SourceInfo接口，定义了数据库连接的通用属性和方法。
 * 继承自该抽象类的具体实现类需要提供特定数据库类型相关的详细信息。
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class AbstractDbSourceInfo implements SourceInfo {

    private static final long serialVersionUID = 3923166373293566599L;

    /**
     * 主机地址，用于指定数据库服务器的位置。
     * 该字段存储了连接到数据库所需的主机名或IP地址，是建立数据库连接的关键信息之一。
     */
    private String host;

    /**
     * 端口号，用于指定数据库服务器的通信端口。
     * 该字段存储了连接到数据库所需的端口号，是建立数据库连接的关键信息之一。
     */
    private String port;


    /**
     * 用户名，用于数据库连接认证。
     * 该字段存储了访问数据库所需的用户名，是进行数据库身份验证的重要信息之一。
     */
    private String userName;


    /**
     * 密码，用于数据库连接认证。
     * 该字段存储了访问数据库所需的密码，是进行数据库身份验证的重要信息之一。
     * 请确保密码的安全性，避免明文存储或传输。
     */
    private String password;


    /**
     * 数据库类型，用于指定数据源的具体数据库类型。
     * 该字段存储了数据库的类型标识，例如MySQL、Oracle等，是建立特定类型数据库连接的关键信息之一。
     */
    private String dbType;

    /**
     * 服务名称，用于标识特定的数据源服务。
     * 该字段在不同的数据源类型中可能具有不同的含义和用途。
     */
    private String serviceName;


    /**
     * 用户Kerberos票据缓存路径，用于指定Kerberos认证过程中票据缓存文件的位置。
     * 该字段存储了Kerberos票据缓存文件的路径，是进行Kerberos认证时的重要信息之一。
     */
    private String userKerTabPath;
    /**
     * krb5配置文件路径，用于Kerberos认证时指定krb5.conf文件的位置。
     * 该配置文件包含了Kerberos客户端需要的领域信息以及KDC服务器的位置等重要信息。
     */
    private String krb5ConfPath;

    /**
     * Kerberos认证主体名称。
     * 该字段用于存储Kerberos认证时的主体名称，通常用于数据库连接的身份验证。
     */
    private String principal;

    /**
     * zookeeper用户名
     */
    private String zookeeperPrincipal;

    /**
     * 数据库名
     */
    private String dbName;

    /**
     * 是否启用Kerberos认证。
     */
    private boolean kerberos;

    /**
     * 云类型，用于标识数据源所在的云环境类型。
     */
    private String cloudType;

    /**
     * 协议类型，用于标识数据源的通信协议类型。
     */
    private String protocol;

    /**
     * 自定义请求头，用于在与数据源进行通信时传递额外的HTTP头部信息。
     * 该映射中的键表示头部字段名称，值表示对应的头部字段值。
     * 可以用于存储如认证信息、内容类型等自定义头部信息。
     */
    private Map<String, String> customHeaders;

    @Override
    public String generateUniqueId() {
        //数据库类型的数据源按驱动信息（如host、port、database）+url 排重创建
        StringBuilder builder = new StringBuilder();
        builder.append(dbType).append(":");
        if (StringUtils.hasText(host)) {
            builder.append(host).append(":");
        }
        if (port != null) {
            builder.append(port).append("/");
        }
        if (StringUtils.hasText(dbName)) {
            builder.append(dbName);
        }
        return builder.toString();
    }
}
