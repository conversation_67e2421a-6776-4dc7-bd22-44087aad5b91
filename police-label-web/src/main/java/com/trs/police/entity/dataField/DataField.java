package com.trs.police.entity.dataField;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据源实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_data_field")
@Slf4j
public class DataField extends AbstractBaseEntity {

    /**
     * 数据源ID
     */
    @TableField("table_id")
    private Long tableId;

    /**
     * 字段类型
     */
    @TableField("field_type")
    private String fieldType;

    /**
     * 字段原始类型
     */
    @TableField("field_original_type")
    private String fieldOriginalType;

    /**
     * 数据表中文名称
     */
    @TableField("field_name_cn")
    private String fieldNameCn;

    /**
     * 数据表英文名称
     */
    @TableField("field_name")
    private String fieldName;

    /**
     * 字段描述
     */
    @TableField("field_description")
    private String fieldDescription;

    /**
     * 可用状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 关联特征数量
     */
    @TableField("feature_count")
    private Integer featureCount = 0;

    /**
     * 关联标签数量
     */
    @TableField("label_count")
    private Integer labelCount = 0;

    /**
     * 关联模型数量
     */
    @TableField("model_count")
    private Integer modelCount = 0;


    /**
     * 检查数据源是否可以被修改
     * 数据源没有特征、标签、模型关联时可以被修改
     *
     * @return 是否可以修改
     */
    @JsonIgnore
    public boolean canBeModified() {
        return featureCount == 0 && labelCount == 0 && modelCount == 0;
    }

    /**
     * 检查数据源是否可以被删除
     * 仅当数据源无特征、标签、模型关联时可删除
     *
     * @return 是否可以删除
     */
    @JsonIgnore
    public boolean canBeDeleted() {
        return canBeModified();
    }

}
