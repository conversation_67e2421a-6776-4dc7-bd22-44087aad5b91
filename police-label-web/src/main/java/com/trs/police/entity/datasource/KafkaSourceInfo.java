package com.trs.police.entity.datasource;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * Kafka数据源信息
 *
 * <AUTHOR>
 */
@Data
public class KafkaSourceInfo implements SourceInfo {

    /**
     * Kafka服务地址
     */
    private String bootServices;

    /**
     * 主题
     */
    private String topic;

    /**
     * 消费组
     */
    private String group;

    @Override
    public DataSourceType getType() {
        return DataSourceType.KAFKA;
    }

    @Override
    public String generateUniqueId() {
        // kafka 数据源根据bootservice、账号、topic、group排重
        StringBuilder builder = new StringBuilder();
        if (StringUtils.hasText(bootServices)) {
            builder.append(bootServices).append("#");
        }
        if (StringUtils.hasText(topic)) {
            builder.append(topic).append("#");
        }
        if (StringUtils.hasText(group)) {
            builder.append(group);
        }
        return builder.toString();
    }
} 