package com.trs.police.entity.label;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.enums.LabelCalculationStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 标签计算任务实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_label_calculation_task")
public class LabelCalculationTaskDO extends AbstractBaseEntity {

    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 标签ID
     */
    @TableField("label_id")
    private Long labelId;

    /**
     * 标签名称
     */
    @TableField("label_name")
    private String labelName;

    /**
     * 触发类型 0=手动 1=定时
     */
    @TableField("trigger_type")
    private Integer triggerType;

    /**
     * jobId
     */
    @TableField("job_id")
    private String jobId;

    /**
     * 任务状态
     */
    @TableField("status")
    private String status;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    @TableField("duration")
    private Long duration;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 执行日志
     */
    @TableField("execution_log")
    private String executionLog;

    /**
     * 是否已删除 0=未删除 1=已删除
     */
    @TableField("deleted")
    private Integer deleted = 0;

    /**
     * 获取状态枚举
     *
     * @return 状态枚举
     */
    public LabelCalculationStatus getStatusEnum() {
        return status != null ? LabelCalculationStatus.fromCode(status) : null;
    }

    /**
     * 设置状态枚举
     *
     * @param statusEnum 状态枚举
     */
    public void setStatusEnum(LabelCalculationStatus statusEnum) {
        this.status = statusEnum != null ? statusEnum.getCode() : null;
    }

    /**
     * 计算执行耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 是否执行成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return LabelCalculationStatus.SUCCESS.getCode().equals(status);
    }

    /**
     * 是否执行失败
     *
     * @return 是否失败
     */
    public boolean isFailed() {
        return LabelCalculationStatus.FAILED.getCode().equals(status);
    }

    /**
     * 是否正在执行
     *
     * @return 是否正在执行
     */
    public boolean isRunning() {
        return LabelCalculationStatus.RUNNING.getCode().equals(status);
    }
}
