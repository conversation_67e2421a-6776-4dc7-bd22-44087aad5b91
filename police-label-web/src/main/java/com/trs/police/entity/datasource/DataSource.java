package com.trs.police.entity.datasource;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 数据源实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_data_source")
@Slf4j
public class DataSource extends AbstractBaseEntity {

    /**
     * 数据源名称
     */
    @TableField("name")
    private String name;

    /**
     * 数据源类型 (ES/Mysql/接口等)
     */
    @TableField("type")
    private DataSourceType type;

    /**
     * 连接状态
     */
    @TableField("connection_status")
    private ConnectionStatus connectionStatus = ConnectionStatus.UNKNOWN;

    /**
     * 数据源详细信息 (JSON格式，根据不同类型存储不同结构)
     */
    @TableField("source_info")
    private String sourceInfo;

    /**
     * 最后连接检查时间
     */
    @TableField("last_check_time")
    private LocalDateTime lastCheckTime;

    /**
     * 关联特征数量
     */
    @TableField("feature_count")
    private Integer featureCount = 0;

    /**
     * 关联标签数量
     */
    @TableField("label_count")
    private Integer labelCount = 0;

    /**
     * 关联模型数量
     */
    @TableField("model_count")
    private Integer modelCount = 0;

    /**
     * 是否被删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建状态
     * 0: 未完成创建流程
     * 1: 已完成创建流程
     */
    @TableField("create_status")
    private Integer createStatus;

    /**
     * 生成唯一标识(生成唯一标识用于判断数据源是否重复)
     */
    @TableField("generate_unique_id")
    private String generateUniqueId;

    /**
     * 数据源来源
     */
    @TableField("source_from")
    private Integer sourceFrom;

    /**
     * 检查数据源是否可以被修改
     * 数据源没有特征、标签、模型关联时可以被修改
     *
     * @return 是否可以修改
     */
    @JsonIgnore
    public boolean canBeModified() {
        return featureCount == 0 && labelCount == 0 && modelCount == 0;
    }

    /**
     * 检查数据源是否可以被删除
     * 仅当数据源无特征、标签、模型关联时可删除
     *
     * @return 是否可以删除
     */
    @JsonIgnore
    public boolean canBeDeleted() {
        return canBeModified();
    }

    /**
     * 数据源连接状态枚举
     */
    public enum ConnectionStatus {
        /**
         * 连接正常
         */
        CONNECTED,

        /**
         * 连接失败
         */
        DISCONNECTED,

        /**
         * 未知状态（尚未检查）
         */
        UNKNOWN
    }
} 