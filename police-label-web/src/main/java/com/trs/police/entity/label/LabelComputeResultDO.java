package com.trs.police.entity.label;

import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.annotations.TableName;
import com.trs.db.sdk.annotations.TableUUID;
import com.trs.police.vo.label.application.SourceDataVO;
import lombok.Data;

/**
 * 标签计算结果实体类
 */
@Data
@TableName("tb_label_compute_result")
public class LabelComputeResultDO {
    @TableUUID
    private String id;

    /**
     * 标签名称
     */
    @TableField("label_name")
    private String labelName;

    /**
     * 标签类型
     */
    @TableField("category")
    private Long category;

    /**
     * 对象类型（1:身份证）
     */
    @TableField("object_type")
    private Integer objectType;

    /**
     * 对象编号（可以是身份证号码、车牌号、手机号等）
     */
    @TableField("object_number")
    private String objectNumber;

    /**
     * 对象编号（可以是身份证号码、车牌号、手机号等）
     */
    @TableField("related_object_number")
    private String relatedObjectNumber;

    /**
     * 频次
     */
    @TableField("frequency")
    private Integer frequency;

    /**
     * 创建时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 更新时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    @TableField("update_time")
    private String updateTime;

    /**
     * 过期时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    @TableField("expire_time")
    private String expireTime;

    /**
     * 来源数据表
     */
    @TableField("source_table")
    private String sourceTable;

    /**
     * 关联的ID集合 {@link SourceDataVO}
     */
    @TableField("source_data_ids")
    private String sourceDataIds;

    /**
     * 关联警种元数据
     */
    @TableField("police_kind")
    private Long policeKind;

    /**
     * 扩展信息
     */
    @TableField("extra_info")
    private String extraInfo;
}