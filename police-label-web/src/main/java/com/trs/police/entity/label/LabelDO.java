package com.trs.police.entity.label;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.constant.label.EffectiveTimeEnum;
import com.trs.police.constant.label.LabelConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 标签实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_label")
@Slf4j
public class LabelDO extends AbstractBaseEntity {

    /**
     * 名称
     */
    @TableField("label_name")
    private String labelName;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 所属分类ID
     */
    @TableField("category_code")
    private Long categoryCode;

    /**
     * 码表接口：type = 'feature_police_kind'
     */
    @TableField("police_kind")
    private Long policeKind;

    /**
     * 业务规则描述
     */
    @TableField("business_rule")
    private String businessRule;

    /**
     * 颜色
     */
    @TableField("color")
    private String color;

    /**
     * 是否自定义颜色 0=否 1=是
     */
    @TableField("is_custom_color")
    private Integer isCustomColor;

    /**
     * 状态 0=停用 1=启用 {@link LabelConstant#START} {@link LabelConstant#STOP}
     */
    @TableField("status")
    private Integer status = LabelConstant.STOP;

    /**
     * 是否已删除 0=未删除 1=已删除
     */
    @TableField("deleted")
    private Integer deleted = 0;

    /**
     * 流程定义
     */
    @TableField("process_order")
    private String processOrder;

    /**
     * 流程定义
     */
    @TableField("label_base_info")
    private String labelBaseInfo;


    /**
     * 标签主体
     */
    @TableField("main_object")
    private String mainObject;

    /**
     * 标签主体
     */
    @TableField("related_object")
    private String relatedObject;

    /**
     * 打标方式 0 手动 1 自动
     */
    @TableField("label_type")
    private Integer labelType;

    /**
     * 周期时间类型 秒 1 分 2 时 3 天 4 月 5
     */
    @TableField("cycle_time_type")
    private Integer cycleTimeType;

    /**
     * 周期时间
     */
    @TableField("cycle_time")
    private Integer cycleTime;

    /**
     * 有效时间方式 0 永久 1 期限 {@link EffectiveTimeEnum}
     */
    @TableField("effective_time_type")
    private Integer effectiveTimeType;

    /**
     * 有效时间天数
     */
    @TableField("effective_time")
    private Integer effectiveTime;

    /**
     * 标签英文名
     */
    @TableField("en_name")
    private String enName;

    /**
     * 使用的特征列表（该字段用于方便特征统计被标签使用情况）json数组
     */
    @TableField("feature_id")
    private String featureId;

    /**
     * 最后一次完成时间
     */
    @TableField("last_finish_time")
    private LocalDateTime lastFinishTime;

    /**
     * 最后一次运行时间
     */
    @TableField("last_run_time")
    private LocalDateTime lastRunTime;

    /**
     * 命中数
     */
    @TableField("hits")
    private Long hits;

    /**
     * 流程定义
     */
    @TableField("process_order_snapshot")
    private String processOrderSnapshot;
}
