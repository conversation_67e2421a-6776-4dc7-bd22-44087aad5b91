package com.trs.police.entity.feature;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 特征实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_feature")
@Slf4j
public class FeatureDO extends AbstractBaseEntity {

    /**
     * 特征名称
     */
    @TableField("feature_name")
    private String featureName;

    /**
     * 特征描述
     */
    @TableField("description")
    private String description;

    /**
     * 所属分类ID
     */
    @TableField("category_code")
    private Long categoryCode;

    /**
     * 码表接口：type = 'feature_police_kind'
     */
    @TableField("police_kind")
    private Long policeKind;

    /**
     * 业务规则描述
     */
    @TableField("business_rule")
    private String businessRule;

    /**
     * 选用的表格ID
     */
    @TableField("table_id")
    private Long tableId;

    /**
     * 输出字段（JSON结构，包括特征主体、对象类型等）
     */
    @TableField("output_fields")
    private String outputFields;

    /**
     * 颜色
     */
    @TableField("color")
    private String color;

    /**
     * 状态 0=停用 1=启用
     */
    @TableField("status")
    private Integer status = 1;

    /**
     * 关联标签数量
     */
    @TableField("label_count")
    private Integer labelCount = 0;

    /**
     * 是否已删除 0=未删除 1=已删除
     */
    @TableField("deleted")
    private Integer deleted = 0;

    /**
     * 特征主体
     */
    @TableField("main_object_code")
    private Long mainObjectCode;

    /**
     * 流程定义
     */
    @TableField("process_order")
    private String processOrder;

    /**
     * 特征主体对象关联的字段
     */
    @TableField("main_object_field")
    private String mainObjectField;

    /**
     * 流程定义
     */
    @TableField("process_order_snapshot")
    private String processOrderSnapshot;
}
