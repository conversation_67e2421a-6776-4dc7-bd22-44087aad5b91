package com.trs.police.entity.baseEntiry;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * kerberos认证表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "auth_certificate_kerberos")
public class AuthCertificateKerberos extends AbstractBaseEntity {

    /**
     * principal用户名称
     */
    @TableField(value = "principal")
    private String principal;

    /**
     * krb5.conf配置文件路径
     */
    @TableField(value = "krb5_pth")
    private String krb5Pth;

    /**
     * keytab文件路径，密钥文件地址
     */
    @TableField(value = "keytab_pth")
    private String keytabPth;
}
