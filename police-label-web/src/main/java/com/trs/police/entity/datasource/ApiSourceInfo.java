package com.trs.police.entity.datasource;

import lombok.Data;

/**
 * API接口数据源信息
 *
 * <AUTHOR>
 */
@Data
public class ApiSourceInfo implements SourceInfo {

    /**
     * 厂商名称
     */
    private String vendorName;

    /**
     * 接口URL
     */
    private String url;

    /**
     * 接口鉴权方式
     */
    private String authType;

    /**
     * 接口鉴权密钥
     */
    private String authKey;

    /**
     * 请求头
     */
    private String headers;

    @Override
    public DataSourceType getType() {
        return DataSourceType.API;
    }

    @Override
    public String generateUniqueId() {
        // 接口数据源按照厂商的URL排重
        return url;
    }
} 