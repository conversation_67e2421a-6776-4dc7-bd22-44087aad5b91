package com.trs.police.entity.datatable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_data_table")
@Slf4j
public class DataTable extends AbstractBaseEntity {

    /**
     * 数据源ID
     */
    @TableField("data_source_id")
    private Long dataSourceId;

    /**
     * 数据表类型 (table/view)
     */
    @TableField("table_type")
    private String tableType;

    /**
     * 数据表中文名称
     */
    @TableField("table_name_cn")
    private String tableNameCn;

    /**
     * 数据表英文名称
     */
    @TableField("table_name")
    private String tableName;

    /**
     * 数据表别名
     */
    @TableField("aliase_name")
    private String aliaseName;

    /**
     * 业务描述
     */
    @TableField("business_description")
    private String businessDescription;

    /**
     * 可用状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否被选中
     */
    @TableField("selected_status")
    private Integer selectedStatus;

    /**
     * 关联特征标签状态
     */
    @TableField("feature_label_status")
    private Integer featureLabelStatus;

    /**
     * 关联特征数量
     */
    @TableField("feature_count")
    private Integer featureCount = 0;

    /**
     * 关联标签数量
     */
    @TableField("label_count")
    private Integer labelCount = 0;

    /**
     * 关联模型数量
     */
    @TableField("model_count")
    private Integer modelCount = 0;

    /**
     * 主键字段
     */
    @TableField("id_field")
    private String idField;


    /**
     * 检查数据源是否可以被修改
     * 数据源没有特征、标签、模型关联时可以被修改
     *
     * @return 是否可以修改
     */
    @JsonIgnore
    public boolean canBeModified() {
        return featureCount == 0 && labelCount == 0 && modelCount == 0;
    }

    /**
     * 检查数据源是否可以被删除
     * 仅当数据源无特征、标签、模型关联时可删除
     *
     * @return 是否可以删除
     */
    @JsonIgnore
    public boolean canBeDeleted() {
        return canBeModified();
    }

}
