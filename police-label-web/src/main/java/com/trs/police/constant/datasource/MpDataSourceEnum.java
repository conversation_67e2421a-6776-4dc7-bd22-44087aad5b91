package com.trs.police.constant.datasource;

import com.trs.police.entity.datasource.DataSourceType;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 中台数据源的映射关系
 *
 * <AUTHOR>
 */
@Getter
public enum MpDataSourceEnum {
    MYSQL(1, DataSourceType.MYSQL),
    ES(6, DataSourceType.ES),
    ORACLE(2, DataSourceType.ORACLE),
    POSTGRESQL(3, DataSourceType.POSTGRESQL),
    HIVE(4, DataSourceType.HIVE),
    CLICKHOUSE(5, DataSourceType.CLICKHOUSE);


    private Integer mpType;

    private DataSourceType type;

    MpDataSourceEnum(Integer mpType, DataSourceType type) {
        this.mpType = mpType;
        this.type = type;
    }

    /**
     * 根据中台数据源类型获取枚举
     *
     * @param mpType 中台数据源类型
     * @return 枚举
     */
    public static MpDataSourceEnum getByMpType(Integer mpType) {
        for (MpDataSourceEnum value : MpDataSourceEnum.values()) {
            if (value.mpType.equals(mpType)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据中台数据源类型获取枚举
     *
     * @param type 中台数据源类型
     * @return 枚举
     */
    public static Optional<MpDataSourceEnum> getByType(DataSourceType type) {
        return Arrays.stream(MpDataSourceEnum.values())
                .filter(mpDataSourceEnum -> mpDataSourceEnum.type.equals(type))
                .findFirst();
    }
}
