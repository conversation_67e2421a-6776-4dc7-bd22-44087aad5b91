package com.trs.police.constant;

/**
 * 节点类型
 *
 * <AUTHOR>
 */
public interface NodeType {

    /**
     * 表格类型
     */
    Integer TABLE = 1;

    /**
     * 过滤
     */
    Integer FILTER = 2;

    /**
     * 排序
     */
    Integer ORDER = 3;

    /**
     * 特征输出
     */
    Integer FEATURE_OUT = 4;

    /**
     * 特征输入
     */
    Integer FEATURE_IN = 5;

    /**
     * 统计
     */
    Integer STATISTIC = 6;

    /**
     * 去重
     */
    Integer DISTINCT = 8;

    /**
     * 转换
     */
    Integer TRANSFORM = 9;

    /**
     * 标签输入
     */
    Integer LABEL_INPUT = 10;

    /**
     * 标签输出
     */
    Integer LABEL_OUTPUT = 11;

    /**
     * 新字段
     */
    Integer NEW_FIELD = 12;

    // 连接

    /**
     * 关联
     */
    Integer RELEVANCY = 14;
}
