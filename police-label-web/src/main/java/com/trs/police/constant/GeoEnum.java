package com.trs.police.constant;

/**
 * GEO的类型
 *
 * <AUTHOR>
 */
public enum GeoEnum {

    CIRCLE("circle"),
    POLYGON("polygon");

    private String code;

    GeoEnum(String code) {
        this.code = code;
    }

    /**
     * 获取枚举代码
     *
     * @return 枚举代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 根据code获取对应的枚举值
     *
     * @param code 枚举代码
     * @return 对应的GeoEnum枚举值，如果未找到则返回null
     */
    public static GeoEnum fromCode(String code) {
        for (GeoEnum geoEnum : GeoEnum.values()) {
            if (geoEnum.getCode().equals(code)) {
                return geoEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取对应的枚举值，如果未找到则抛出异常
     *
     * @param code 枚举代码
     * @return 对应的GeoEnum枚举值
     * @throws IllegalArgumentException 如果未找到对应的枚举值
     */
    public static GeoEnum fromCodeOrThrow(String code) {
        GeoEnum result = fromCode(code);
        if (result == null) {
            throw new IllegalArgumentException("No GeoEnum with code: " + code);
        }
        return result;
    }
}

