package com.trs.police.constant.label;

import lombok.Getter;

/**
 * 数据库字段映射类型
 */
public enum LabelUpdateMethodEnum {

    HAND(0, "手动"),
    AUTO(1, "例行");

    @Getter
    private Integer labelUpdateType;

    @Getter
    private String typeName;

    LabelUpdateMethodEnum(Integer labelUpdateType, String typeName) {
        this.labelUpdateType = labelUpdateType;
        this.typeName = typeName;
    }

    /**
     * 获取类型名称
     *
     * @param fieldType type
     * @return 名称
     */
    public static String getTypeName(Integer fieldType) {
        if (fieldType == null) {
            return "";
        }
        for (LabelUpdateMethodEnum type : LabelUpdateMethodEnum.values()) {
            if (type.getLabelUpdateType().equals(fieldType)) {
                return type.getTypeName();
            }
        }
        return "";
    }
}
