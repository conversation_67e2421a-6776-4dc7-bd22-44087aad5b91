package com.trs.police.constant.label;

import lombok.Getter;

/**
 * 数据库字段映射类型
 */
public enum LabelUpdateStatusEnum {

    SUCCESS(0, "成功"),
    WAIT_COMPUTE(1, "等待计算"),
    DELAY_COMPUTE(2, "延迟计算");

    @Getter
    private Integer labelUpdateStatus;

    @Getter
    private String typeName;

    LabelUpdateStatusEnum(Integer labelUpdateStatus, String typeName) {
        this.labelUpdateStatus = labelUpdateStatus;
        this.typeName = typeName;
    }

    /**
     * 获取状态名称
     *
     * @param fieldType type
     * @return 名称
     */
    public static String getTypeName(Integer fieldType) {
        if (fieldType == null) {
            return "";
        }
        for (LabelUpdateStatusEnum type : LabelUpdateStatusEnum.values()) {
            if (type.getLabelUpdateStatus().equals(fieldType)) {
                return type.getTypeName();
            }
        }
        return "";
    }
}
