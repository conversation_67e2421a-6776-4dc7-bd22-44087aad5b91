package com.trs.police.service.node.impl;

import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.dto.node.properties.LabelInputProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;
import com.trs.police.service.node.NodeService;
import com.trs.police.service.shared.label.LabelFacade;
import com.trs.web.builder.util.BeanFactoryHolder;

import java.util.List;

import static com.trs.police.constant.NodeType.LABEL_INPUT;

/**
 * 标签输入节点
 */
public class LabelInputNode extends Node {

    private LabelFacade labelFacade;

    public LabelInputNode(NodeMeta nodeMeta, String nodeProperties, LabelFacade labelFacade) {
        super(nodeMeta, nodeProperties);
        this.labelFacade = labelFacade;
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        LabelInputProperties property = getPropertyAs(LabelInputProperties.class);
        ProcessDTO process = labelFacade.getProcess(property.getLabelId());
        NodeService service = BeanFactoryHolder.getBean(NodeService.class).get();
        NodeData nodeData = service.previewNode(process);
        return nodeData;
    }

    @Override
    public Integer nodeType() {
        return LABEL_INPUT;
    }
}
