package com.trs.police.service.node;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.dto.node.properties.bean.GroupField;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分组辅助
 *
 * <AUTHOR>
 */
public class GroupHelper {

    /**
     * 构造出单行分组的key
     *
     * @param groupField 配置
     * @return 分组构造函数
     */
    public static Function<List<FieldValue>, String> getGroupKey(List<GroupField> groupField) {
        if (null == groupField || groupField.isEmpty()) {
            throw new IllegalArgumentException("groupField is empty");
        }
        return data -> {
            return groupField
                    .stream()
                    .map(field -> {
                        for (FieldValue value : data) {
                            // 找到分组字段，构造值
                            if (field.getFiled().getId().equals(value.getId())) {
                                // 分组字段是时间类型
                                if (Objects.nonNull(value.getTypeCode()) && value.getTypeCode().equals(DataBaseFieldMappingType.DATETIME.getFieldType())) {
                                    if (StringUtils.isEmpty(value.getValue())) {
                                        return "null";
                                    }
                                    Date date = TimeUtils.stringToDate(value.getValue());
                                    LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
                                    LocalDateTime time;
                                    switch (field.getGroupType()) {
                                        case 1:
                                            // 时间精度保留到秒级别（后面的时间清零）
                                            time = localDateTime.
                                                    withNano(0)
                                                    .withSecond(localDateTime.getSecond());
                                            break;
                                        case 2:
                                            // 时间精度保留到分钟级别（秒和纳秒清零）
                                            time = localDateTime
                                                    .withNano(0)
                                                    .withSecond(0)
                                                    .withMinute(localDateTime.getMinute());
                                            break;
                                        case 3:
                                            // 时间精度保留到小时级别（分钟和秒和纳秒清零）
                                            time = localDateTime
                                                    .withNano(0)
                                                    .withSecond(0)
                                                    .withMinute(0)
                                                    .withHour(localDateTime.getHour());
                                            break;
                                        case 4:
                                            // 时间精度保留到天级别（小时、分钟
                                            time = localDateTime
                                                    .withNano(0)
                                                    .withSecond(0)
                                                    .withMinute(0)
                                                    .withHour(0);
                                            break;
                                        case 5:
                                            // 时间精度保留到周级别
                                            time = localDateTime
                                                    .withNano(0)
                                                    .withSecond(0)
                                                    .withMinute(0)
                                                    .withHour(0)
                                                    .with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
                                            break;
                                        case 6:
                                            // 时间精度保留到月级别（天、小时、分钟、秒和纳秒清零）
                                            time = localDateTime
                                                    .withNano(0)
                                                    .withSecond(0)
                                                    .withMinute(0)
                                                    .withHour(0)
                                                    .withDayOfMonth(1);
                                            break;
                                        case 7:
                                            // 时间精度保留到年级别（月、天、小时、分钟、秒和纳秒清零）
                                            time = localDateTime
                                                    .withNano(0)
                                                    .withSecond(0)
                                                    .withMinute(0)
                                                    .withHour(0)
                                                    .withMonth(1).
                                                    withDayOfMonth(1);
                                            break;
                                        default:
                                            throw new IllegalArgumentException("Unsupported groupType: " + field.getGroupType());
                                    }
                                    return String.valueOf(time.atZone(ZoneId.systemDefault()).toEpochSecond());

                                }
                                // 分组字段是普通字段
                                return value.getValue();
                            }
                        }
                        return "-";
                    })
                    .collect(Collectors.joining("|"));
        };
    }
}
