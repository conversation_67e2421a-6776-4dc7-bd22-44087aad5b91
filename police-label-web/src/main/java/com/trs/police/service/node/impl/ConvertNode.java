package com.trs.police.service.node.impl;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.ConvertNodeProperties;
import com.trs.police.dto.node.properties.bean.ConvertItem;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 转换节点
 *
 * <AUTHOR>
 */
public class ConvertNode extends Node {

    public ConvertNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        NodeData data = inputNode.get(0);
        ConvertNodeProperties property = getPropertyAs(ConvertNodeProperties.class);
        List<List<FieldValue>> result = data.getData()
                .stream()
                // 转换单行
                .map(d -> convertRow(d, property))
                .collect(Collectors.toList());
        // 返回结果
        NodeData r = new NodeData();
        r.setData(result);
        r.setTotalCount(data.getTotalCount());
        r.setNodeMeta(nodeMeta);
        for (FieldInfoVO fieldInfoVO : data.getHeader()) {
            Optional<ConvertItem> any = property.getConvertField().stream()
                    .filter(c -> c.getFrom().getEnName().equals(fieldInfoVO.getEnName()))
                    .findAny();
            if (any.isPresent()) {
                ConvertItem convertItem = any.get();
                fieldInfoVO.setCnName(convertItem.getTo().getCnName());
                fieldInfoVO.setEnName(convertItem.getTo().getEnName());
                fieldInfoVO.setTypeCode(convertItem.getTo().getTypeCode());
            }
        }
        r.setHeader(data.getHeader());
        return r;
    }

    @Override
    public Integer nodeType() {
        return NodeType.TRANSFORM;
    }

    private List<FieldValue> convertRow(List<FieldValue> input, ConvertNodeProperties property) {
        return input.stream()
                .map(fieldValue -> {
                    // 找到需要转换的字段
                    Optional<ConvertItem> any = property.getConvertField().stream()
                            .filter(c -> c.getFrom().getEnName().equals(fieldValue.getEnName()))
                            .findAny();
                    // 如果需要转换，执行转换再返回
                    if (any.isPresent()) {
                        return convertField(fieldValue, any.get());
                    } else {
                        return fieldValue;
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换字段
     *
     * @param fieldValue 字段值
     * @param item 转换目标
     * @return 转换后的值
     */
    private FieldValue convertField(FieldValue fieldValue, ConvertItem item) {
        if (fieldValue.getTypeCode().equalsIgnoreCase(item.getTo().getTypeCode())) {
            return fieldValue;
        }
        // 构造转换后的对象
        FieldInfoVO fieldInfoVO = fieldValue.getFieldInfo().copy();
        fieldInfoVO.setTypeCode(item.getTo().getTypeCode());
        FieldValue out = new FieldValue(null, fieldInfoVO);
        // 根据目标类型执行转换
        DataBaseFieldMappingType type = DataBaseFieldMappingType.getType(item.getTo().getTypeCode());
        switch (type) {
            case STRING:
                out.setValue(fieldValue.getValue());
                break;
            case NUMBER:
                switch (DataBaseFieldMappingType.getType(fieldValue.getTypeCode())) {
                    case NUMBER:
                        out.setValue(fieldValue.getValue());
                        break;
                    case STRING:
                        try {
                            out.setValue(Double.valueOf(fieldValue.getValue()).toString());
                        } catch (NumberFormatException e) {
                            out.setValue("");
                        }
                        break;
                    case DATETIME:
                        if (StringUtils.isNotEmpty(fieldValue.getValue())) {
                            long epochSecond = TimeUtils.stringToDate(fieldValue.getValue()).toInstant().atZone(ZoneId.systemDefault()).toEpochSecond();
                            out.setValue(epochSecond + "");
                        } else {
                            out.setValue("");
                        }
                        break;
                    default:
                        throw new RuntimeException("转换出错");
                }
                break;
            case DATETIME:
                switch (DataBaseFieldMappingType.getType(fieldValue.getTypeCode())) {
                    case NUMBER:
                        if (StringUtils.isEmpty(fieldValue.getValue())) {
                            out.setValue("");
                        } else {
                            out.setValue(TimeUtils.dateToString(new Date(Long.valueOf(fieldValue.getValue()))));
                        }
                        break;
                    case STRING:
                        if (StringUtils.isEmpty(fieldValue.getValue())) {
                            out.setValue("");
                        } else {
                            try {
                                Date date = TimeUtils.stringToDate(fieldValue.getValue());
                                if (date == null) {
                                    out.setValue("");
                                }
                                out.setValue(fieldValue.getValue());
                            } catch (NumberFormatException e) {
                                out.setValue("");
                            }
                        }
                        break;
                    case DATETIME:
                        out.setValue(fieldValue.getValue());
                        break;
                    default:
                        throw new RuntimeException("转换出错");
                }
                break;
            case BOOLEAN:
                switch (DataBaseFieldMappingType.getType(fieldValue.getTypeCode())) {
                    case BOOLEAN:
                        out.setValue(fieldValue.getValue());
                        break;
                    case STRING:
                        out.setValue(fieldValue.getValue().equalsIgnoreCase("true") ? "true" : "false");
                        break;
                    case NUMBER:
                        out.setValue(fieldValue.getValue().equals("1") ? "true" : "false");
                        break;
                    default:
                        throw new RuntimeException("转换出错");
                }
            default:
                throw new RuntimeException("转换出错");
        }
        return out;
    }
}
