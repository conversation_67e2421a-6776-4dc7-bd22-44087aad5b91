package com.trs.police.service.node.formula.function;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.NodeFunction;

import java.util.List;

import static com.trs.police.constant.node.NodeConstants.NUMBER;

/**
 * 时间差计算函数
 *
 * <AUTHOR>
 */
public class DateDiffNodeFunction implements NodeFunction {

    public static final DateDiffNodeFunction INSTANCE = new DateDiffNodeFunction();

    private DateDiffNodeFunction() {
    }

    @Override
    public FieldValue execute(List<FieldValue> parameters) {
        String time1 = parameters.get(0).getValue();
        String time2 = parameters.get(1).getValue();
        if (StringUtils.isEmpty(time1) || StringUtils.isEmpty(time2)) {
            return new FieldValue("", NUMBER);
        }
        long t1 = TimeUtils.stringToDate(time1).getTime();
        long t2 = TimeUtils.stringToDate(time2).getTime();
        Long diff = t1 - t2;
        return new FieldValue(diff.toString(), NUMBER);
    }

    @Override
    public String key() {
        return "date_diff";
    }
}
