package com.trs.police.service.node.rowmatch;

import com.googlecode.cqengine.resultset.ResultSet;
import com.trs.police.common.core.constant.FieldTypeMapping;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.common.core.vo.node.Row;
import com.trs.police.common.core.vo.node.ValueFactory;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.cache.DynamicRowIndexerFactory;
import com.trs.police.service.node.cache.QueryBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 行匹配器
 */
public class RowMatcher {

    /**
     * 执行行匹配
     *
     * @param a a
     * @param b b
     * @param configs 配置
     * @return 匹配结果
     */
    public static List<MatchResult> match(NodeData a, NodeData b, List<MatchConfig> configs) {
        // 构造b的索引
        DynamicRowIndexerFactory.Builder builder = DynamicRowIndexerFactory.builder(b.getRowList());
        configs.forEach(field -> {
            FieldTypeMapping tp = FieldTypeMapping.fromName(field.getB().getTypeCode());
            builder.addIndexField(field.getB().getId(), tp);
        });
        DynamicRowIndexerFactory.RowIndexer cache = builder.build();

        // 遍历a 匹配b
        Map<String, Integer> columnIndexById = a.findColumnIndexById();
        List<MatchResult> match = a
                .getRowList()
                .stream()
                .map(rowA -> {
                    MatchResult result = new MatchResult(rowA);
                    // 匹配到b中所有数据
                    QueryBuilder queryBuilder = new QueryBuilder(cache);
                    for (MatchConfig matchConfig : configs) {
                        String aid = matchConfig.getA().getId();
                        FieldValue value = rowA.getRowData().get(columnIndexById.get(aid));
                        Object typeValue = ValueFactory.getValue(value.getValue(), value.getTypeCode());
                        switch (matchConfig.getMatchType()) {
                            case "eq":
                                queryBuilder.eq(matchConfig.getB().getId(), typeValue);
                                break;
                            case "gt":
                                queryBuilder.gt(matchConfig.getB().getId(), (Comparable) typeValue);
                                break;
                            case "lt":
                                queryBuilder.lt(matchConfig.getB().getId(), (Comparable) typeValue);
                                break;
                            case "gte":
                                queryBuilder.gte(matchConfig.getB().getId(), (Comparable) typeValue);
                                break;
                            case "lte":
                                queryBuilder.lte(matchConfig.getB().getId(), (Comparable) typeValue);
                                break;
                            case "neq":
                                queryBuilder.neq(matchConfig.getB().getId(), typeValue);
                                break;
                            default:
                                throw new TRSException("不支持的匹配类型" + matchConfig.getMatchType());
                        }
                    }
                    ResultSet<Row> rows = queryBuilder.executeAnd();
                    List<Row> matched = new ArrayList<>();
                    for (Row row : rows) {
                        matched.add(row);;
                    }
                    result.setMatched(matched);
                    return result;
                })
                .collect(Collectors.toList());
        return match;
    }
}
