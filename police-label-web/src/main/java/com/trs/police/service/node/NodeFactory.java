package com.trs.police.service.node;

import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.datatable.DataTableService;
import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.service.node.impl.*;
import com.trs.police.service.shared.feature.FeatureFacade;
import com.trs.police.service.shared.label.LabelFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 节点工厂
 *
 * <AUTHOR>
 */
@Component
public class NodeFactory {


    @Autowired
    private DataTableService dataTableService;

    @Autowired
    private FieldsService fieldsService;

    @Autowired
    private FeatureFacade featureFacade;

    @Autowired
    private LabelFacade labelFacade;

    /**
     * 匹配节点
     *
     * @param type 类型
     * @param nodeMeta nm
     * @param nodeProperties np
     * @return 节点
     */
    public Optional<Node> nodeOf(Integer type, NodeMeta nodeMeta, String nodeProperties) {
        if (Objects.isNull(type)) {
            return Optional.empty();
        }
        if (NodeType.TABLE.equals(type)) {
            return Optional.of(new TableNode(nodeMeta, nodeProperties, dataTableService, fieldsService));
        }
        if (NodeType.FILTER.equals(type)) {
            return Optional.of(new FilterNode(nodeMeta, nodeProperties));
        }
        if (NodeType.ORDER.equals(type)) {
            return Optional.of(new OrderNode(nodeMeta, nodeProperties));
        }
        if (NodeType.FEATURE_OUT.equals(type)) {
            return Optional.of(new FeatureOutNode(nodeMeta, nodeProperties));
        }
        if (NodeType.FEATURE_IN.equals(type)) {
            return Optional.of(new FeatureInputNode(nodeMeta, nodeProperties, featureFacade));
        }
        if (NodeType.STATISTIC.equals(type)) {
            return Optional.of(new StatisticNode(nodeMeta, nodeProperties));
        }
        if (NodeType.DISTINCT.equals(type)) {
            return Optional.of(new DistinctNode(nodeMeta, nodeProperties));
        }
        if (NodeType.TRANSFORM.equals(type)) {
            return Optional.of(new ConvertNode(nodeMeta, nodeProperties));
        }
        if (NodeType.LABEL_INPUT.equals(type)) {
            return Optional.of(new LabelInputNode(nodeMeta, nodeProperties, labelFacade));
        }
        if (NodeType.LABEL_OUTPUT.equals(type)) {
            return Optional.of(new LabelOutputNode(nodeMeta, nodeProperties));
        }
        if (NodeType.NEW_FIELD.equals(type)) {
            return Optional.of(new NewFieldNode(nodeMeta, nodeProperties));
        }
        if (NodeType.RELEVANCY.equals(type)) {
            return Optional.of(new RelevancyNodeProxy(nodeMeta, nodeProperties));
        }
        return Optional.empty();
    }
}
