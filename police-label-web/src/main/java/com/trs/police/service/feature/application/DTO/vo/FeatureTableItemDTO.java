package com.trs.police.service.feature.application.DTO.vo;

import com.trs.police.service.shared.table.TableItemDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 特征使用表格的详情信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FeatureTableItemDTO extends TableItemDTO {

    /**
     * 表格中字段数
     */
    private Integer featureCount;

    public FeatureTableItemDTO(TableItemDTO dto) {
        if (Objects.nonNull(dto)) {
            setTableId(dto.getTableId());
            setTableName(dto.getTableName());
            setTableNameCn(dto.getTableNameCn());
        }
    }
}
