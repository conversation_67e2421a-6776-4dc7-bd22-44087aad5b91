package com.trs.police.service.shared.field;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.entity.dataField.DataField;
import com.trs.police.mapper.DataFieldMapper;
import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.service.shared.table.TableReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 字段
 *
 * <AUTHOR>
 */
@Component
public class FieldFacade {

    @Autowired
    private DataFieldMapper dataFieldMapper;

    @Autowired
    private FieldsService fieldsService;

    /**
     * 根据表格查找字段
     *
     * @param tableReference 表格
     * @return 字段
     */
    public List<DataTableFieldDTO> findByTable(TableReference tableReference) {
        List<DataField> dataFields = dataFieldMapper.selectList(
                Wrappers.lambdaQuery(DataField.class)
                        .eq(DataField::getTableId, tableReference.getTableId())
        );
        return dataFields.stream()
                .map(field -> {
                    DataTableFieldDTO dto = new DataTableFieldDTO();
                    dto.setFieldName(field.getFieldName());
                    dto.setFieldNameCn(field.getFieldNameCn());
                    dto.setFieldType(field.getFieldType());
                    return dto;
                })
                .collect(Collectors.toList());
    }
}
