package com.trs.police.service.node.impl.nodeDTO;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.EmtpyExpression;
import com.trs.common.utils.expression.Expression;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.properties.SparkLabelInputProperties;
import com.trs.police.dto.node.properties.TableNodeProperties;
import com.trs.police.entity.datasource.AbstractDbSourceInfo;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.police.entity.datatable.DataTable;
import com.trs.police.mapper.DataSourceMapper;
import com.trs.police.mapper.DataTableMapper;
import com.trs.police.service.node.FilterNodeConditionParser;
import com.trs.police.utils.SourceInfoConverter;
import com.trs.police.vo.RowFieldVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 表格输入节点
 */
@Component
public class TableNodeDTOBuilder extends LabelNodeDTOBuilder {
    @Autowired
    private DataTableMapper dataTableMapper;
    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Override
    public boolean supports(Integer nodeTypeCode) {
        return NodeType.TABLE.equals(nodeTypeCode);
    }

    @Override
    public List<LabelNodeDTO> build(NodeDTO nodeDTO, NodeContext nodeContext) {
        TableNodeProperties property = JSON.parseObject(nodeDTO.getNodeProperties(), TableNodeProperties.class);
        FilterNodeConditionParser parser = new FilterNodeConditionParser(nodeContext);
        Expression expression = StringUtils.isNotEmpty(property.getTokens())
                ? parser.parseCondition(property.getTokens())
                : new EmtpyExpression();
        PreConditionCheck.checkNotNull(property.getTableId(), "数据表ID不能为空");
        DataTable dataTable = dataTableMapper.selectById(property.getTableId());
        PreConditionCheck.checkNotNull(dataTable.getDataSourceId(), "数据源id为空");
        DataSource dataSource = dataSourceMapper.selectById(dataTable.getDataSourceId());
        SourceInfo sourceInfo = SourceInfoConverter.fromJson(dataSource.getSourceInfo(), dataSource.getType());
        AbstractDbSourceInfo abstractDbSourceInfo = (AbstractDbSourceInfo) sourceInfo;

        SparkLabelInputProperties properties = new SparkLabelInputProperties();
        properties.setWhere(where(expression)); // where条件由Manager统一处理
        String dataSourceType = "MySql".equalsIgnoreCase(abstractDbSourceInfo.getDbType()) ? "jdbc:mysql" : abstractDbSourceInfo.getDbType().toLowerCase();
        String dbName = "ES".equalsIgnoreCase(abstractDbSourceInfo.getDbType()) ? "" : ("/"+abstractDbSourceInfo.getDbName());
        properties.setUrl(dataSourceType+"://"+abstractDbSourceInfo.getHost()+":"+abstractDbSourceInfo.getPort()+dbName);
        properties.setUsername(abstractDbSourceInfo.getUserName());
        properties.setPassword(abstractDbSourceInfo.getPassword());
        properties.setTable(dataTable.getTableName());
        properties.setTableId(dataTable.getId());
        PreConditionCheck.checkNotEmpty(dataTable.getIdField(), "数据表ID字段不能为空");
        properties.setIdCol(dataTable.getIdField());
        List<RowFieldVo> outputRow = nodeDTO.getNodeMeta().getOutputRowMeta().getValueMetaList().stream().map(valueMateBase -> RowFieldVo.of(valueMateBase.getEnName(), valueMateBase.getCol())).collect(Collectors.toList());
        return Collections.singletonList(new LabelNodeDTO(NodeType.LABEL_INPUT, JSONObject.toJSONString(properties), outputRow, nodeDTO.getLastNodeId()));
    }
} 