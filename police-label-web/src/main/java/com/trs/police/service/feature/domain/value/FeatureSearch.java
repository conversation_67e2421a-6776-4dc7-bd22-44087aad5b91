package com.trs.police.service.feature.domain.value;

import com.trs.common.pojo.OrderDTO;
import lombok.Builder;
import lombok.Value;

import java.util.List;

/**
 * 特征搜索值对象
 * 用于封装特征搜索的领域逻辑
 */
@Value
@Builder
public class FeatureSearch {

    /**
     * 数据表名称
     */
    private String tableName;

    /**
     * 特征名称
     */
    private String featureName;

    /**
     * 类别 code
     */
    private String categoryCode;

    /**
     * 搜索的起始时间
     */
    private String startTime;

    /**
     * 搜索的结束时间
     */
    private String endTime;

    /**
     * 警种类型
     */
    private Long policeKind;

    /**
     * 表 ID
     */
    private Long tableId;


    private String featureSubject;

    /**
     * 特征主体编码
     */
    private Long mainObjectCode;

    /**
     * 表格输入字段 ID 列表
     */
    private List<Long> tableInputFieldIds;

    /**
     * 搜索的关键字或值
     */
    private String searchValue;

    /**
     * 排序规则列表
     */
    private List<OrderDTO> orderList;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    private Long dataSourceTableId;

    /**
     * 验证搜索条件是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        // 时间范围校验
        if (startTime != null && endTime != null && startTime.compareTo(endTime) > 0) {
            return false;
        }

        // 分页参数校验
        if (pageNum != null && pageNum < 1) {
            return false;
        }
        if (pageSize != null && pageSize < 1) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否包含时间范围条件
     *
     * @return 是否包含时间范围
     */
    public boolean hasTimeRange() {
        return startTime != null || endTime != null;
    }

    /**
     * 检查是否包含分类条件
     *
     * @return 是否包含分类条件
     */
    public boolean hasCategoryFilter() {
        return categoryCode != null && !categoryCode.isEmpty();
    }
}
