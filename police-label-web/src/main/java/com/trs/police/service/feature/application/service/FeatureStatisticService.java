package com.trs.police.service.feature.application.service;

import com.trs.police.common.core.vo.CountVO;
import com.trs.police.service.feature.application.DTO.query.FeatureCategoryStatisticDTO;
import com.trs.police.service.feature.application.DTO.vo.FeatureCategoryStatisticVO;
import com.trs.police.service.shared.table.TableReference;

import java.util.List;

/**
 * 特征统计服务
 *
 * <AUTHOR>
 */
public interface FeatureStatisticService {

    /**
     * 特征统计
     *
     * @param dto 参数
     * @return 特征统计结果
     */
    List<FeatureCategoryStatisticVO> featureCategoryStatistic(FeatureCategoryStatisticDTO dto);

    /**
     * 表格使用统计
     *
     * @param tbs 表格
     * @return 表格数量统计
     */
    List<CountVO> tableUseStatistic(List<TableReference> tbs);
}
