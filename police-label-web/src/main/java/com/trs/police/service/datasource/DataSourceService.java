package com.trs.police.service.datasource;

import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.police.vo.DataSourceVO;
import com.trs.police.vo.datasource.DataSourceGroupVO;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.builder.base.RestfulResultsV2;


/**
 * 数据源服务接口
 *
 * <AUTHOR>
 */
public interface DataSourceService {


    /**
     * 创建数据源
     *
     * @param dto 数据源信息
     * @return 创建的数据源
     */
    RestfulResults saveDataSource(DataSourceDTO dto);

    /**
     * 更新数据源
     *
     * @param dto 数据源信息
     * @return 创建的数据源
     */
    DataSourceVO updateDataSource(DataSourceDTO dto);

    /**
     * 根据ID查询数据源
     *
     * @param id 数据源ID
     * @return 数据源信息
     */
    DataSource getDataSourceById(Long id);

    /**
     * 分页查询数据源
     *
     * @param dto  dto
     * @return 数据源分页结果
     */
    RestfulResultsV2<DataSourceVO> findDataSources(DataSourceDTO dto);

    /**
     * 分页查询数据源
     *
     * @param dto  dto
     * @return 数据源分页结果
     */
    RestfulResultsV2<DataSourceGroupVO> listMpDataSources(DataSourceDTO dto);

    /**
     * 删除数据源
     * 仅当数据源未被关联时才能删除
     *
     * @param id 数据源ID
     * @throws IllegalArgumentException 如果数据源不能被删除
     */
    void deleteDataSource(Long id);

    /**
     * 检查数据源连接
     *
     * @param dto 数据源信息
     * @return 连接检查结果
     */
    boolean checkConnection(DataSourceDTO dto);

    /**
     * 更新数据源关联状态
     *
     * @param id           数据源ID
     * @param featureCount 特征数量
     * @param labelCount   标签数量
     * @param modelCount   模型数量
     * @return 更新后的数据源
     */
    DataSource updateRelationStatus(Long id, int featureCount, int labelCount, int modelCount);

    /**
     * 获取数据源详细信息
     *
     * @param id 数据源ID
     * @return 数据源详细信息
     */
    SourceInfo getSourceInfo(Long id);

    /**
     * 更改数据源创建状态
     *
     * @param id    数据源ID
     * @param status 数据源状态
     */
    void changeDataSourceStatus(Long id, Integer status);

    /**
     * 刷新数据源
     *
     * @param id id
     */
    void refreshDataSource(Long id);

}