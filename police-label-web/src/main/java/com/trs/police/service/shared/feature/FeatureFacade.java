package com.trs.police.service.shared.feature;

import com.trs.police.common.core.vo.CountVO;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessDTO;
import com.trs.police.service.feature.application.service.FeatureService;
import com.trs.police.service.feature.application.service.FeatureStatisticService;
import com.trs.police.service.shared.table.TableReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 特征装饰器
 *
 * <AUTHOR>
 */
@Component
public class FeatureFacade {

    @Autowired
    private FeatureService featureService;

    @Autowired
    private FeatureStatisticService featureStatisticService;

    /**
     * 获取特征的编排信息
     *
     * @param featureId 特征id
     * @return 特征编排信息
     */
    public ProcessDTO getFeatureProcess(Long featureId) {
        FeatureProcessDTO javaObject = featureService.detail(featureId)
                .getProcessOrder()
                .toJavaObject(FeatureProcessDTO.class);
        ProcessDTO dto = new ProcessDTO();
        dto.setNodes(javaObject.getNodes());
        dto.setControl(javaObject.getControl());
        dto.setNodeOrders(javaObject.getNodeOrders());
        String uuid = javaObject.getNodes().stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.FEATURE_OUT))
                .findAny()
                .map(NodeDTO::getNodeMeta)
                .map(NodeMeta::getUuid)
                .get();
        dto.setCurrentNode(uuid);
        return dto;
    }

    /***
     * 获取特征列表
     *
     * @param featureIds 特征id
     * @return 特征列表
     */
    public List<Feature> featureList(List<Long> featureIds) {
        return featureService.findFeaturesByIds(featureIds)
                .getDatas()
                .stream()
                .map(f -> {
                    Feature feature = new Feature();
                    feature.setFeatureId(f.getFeatureId());
                    feature.setFeatureName(f.getFeatureName());
                    return feature;
                })
                .collect(Collectors.toList());
    }

    /**
     * 表格使用统计
     *
     * @param tbs 表格
     * @return 表格数量统计
     */
    public List<CountVO> tableUseStatistic(List<TableReference> tbs) {
        return featureStatisticService.tableUseStatistic(tbs);
    }
}