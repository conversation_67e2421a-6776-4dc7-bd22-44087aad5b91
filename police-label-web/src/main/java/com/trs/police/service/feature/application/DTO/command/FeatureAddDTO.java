package com.trs.police.service.feature.application.DTO.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 特征新增请求DTO
 */
@Data
public class FeatureAddDTO {
    
    /**
     * 特征ID
     */
    private Long featureId;
    
    /**
     * 特征名称
     */
    @NotBlank(message = "特征名称不能为空")
    private String featureName;
    
    /**
     * 特征描述
     */
    private String description;
    
    /**
     * 所属分类(码表code)
     */
    @NotNull(message = "所属分类不能为空")
    private Long categoryCode;
    
    /**
     * 所属警种(码表code)
     */
    @NotNull(message = "所属警种不能为空")
    private Long policeKind;
    
    /**
     * 业务规则描述
     */
    private String businessRule;
    
    /**
     * 选用的表格ID
     */
    private Long tableId;

    /**
     * 特征主体编码
     */
    private Long mainObjectCode;

    /**
     * 所选表主键字段
     */
    private String idField;
}