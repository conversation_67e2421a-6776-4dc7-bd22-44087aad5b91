package com.trs.police.service.node.cache;

import com.googlecode.cqengine.attribute.Attribute;
import com.googlecode.cqengine.query.Query;
import com.googlecode.cqengine.resultset.ResultSet;
import com.trs.police.common.core.vo.node.Row;

import java.util.ArrayList;
import java.util.List;

import static com.googlecode.cqengine.query.QueryFactory.*;
import static com.googlecode.cqengine.query.QueryFactory.contains;

/**
 * 条件构造期
 *
 * <AUTHOR>
 */
public class QueryBuilder {
    private final DynamicRowIndexerFactory.RowIndexer indexer;
    private final List<Query<Row>> queries = new ArrayList<>();

    public QueryBuilder(DynamicRowIndexerFactory.RowIndexer indexer) {
        this.indexer = indexer;
    }

    /**
     * 添加大于条件
     *
     * @param id 表头id
     * @param value 值
     * @return QueryBuilder
     */
    public QueryBuilder gt(String id, Comparable value) {
        Attribute<Row, Comparable> attribute = (Attribute<Row, Comparable>) indexer.getAttribute(id);
        queries.add(greaterThan(attribute, value));
        return this;
    }

    /**
     * 添加大于条件
     *
     * @param id 表头id
     * @param value 值
     * @return QueryBuilder
     */
    public QueryBuilder lt(String id, Comparable value) {
        Attribute<Row, Comparable> attribute = (Attribute<Row, Comparable>) indexer.getAttribute(id);
        queries.add(lessThan(attribute, value));
        return this;
    }

    /**
     * 添加大于条件
     *
     * @param id 表头id
     * @param value 值
     * @return QueryBuilder
     */
    public QueryBuilder gte(String id, Comparable value) {
        Attribute<Row, Comparable> attribute = (Attribute<Row, Comparable>) indexer.getAttribute(id);
        queries.add(greaterThanOrEqualTo(attribute, value));
        return this;
    }

    /**
     * 添加大于条件
     *
     * @param id 表头id
     * @param value 值
     * @return QueryBuilder
     */
    public QueryBuilder lte(String id, Comparable value) {
        Attribute<Row, Comparable> attribute = (Attribute<Row, Comparable>) indexer.getAttribute(id);
        queries.add(lessThanOrEqualTo(attribute, value));
        return this;
    }

    /**
     * 添加小于条件
     *
     * @param id 表头id
     * @param value 值
     * @return QueryBuilder
     */
    public QueryBuilder eq(String id, Object value) {
        Attribute<Row, Object> attribute = (Attribute<Row, Object>) indexer.getAttribute(id);
        queries.add(equal(attribute, value));
        return this;
    }

    /**
     * 添加小于条件
     *
     * @param id 表头id
     * @param value 值
     * @return QueryBuilder
     */
    public QueryBuilder neq(String id, Object value) {
        Attribute<Row, Object> attribute = (Attribute<Row, Object>) indexer.getAttribute(id);
        queries.add(not(equal(attribute, value)));
        return this;
    }

    /**
     * 添加前缀匹配条件
     *
     * @param id 表头id
     * @param prefix 前缀
     * @return QueryBuilder
     */
    public QueryBuilder startsWithQuery(String id, String prefix) {
        Attribute<Row, String> attribute = (Attribute<Row, String>) indexer.getAttribute(id);
        queries.add(startsWith(attribute, prefix));
        return this;
    }

    /**
     * 添加子串匹配条件
     *
     * @param id 表头id
     * @param substring 子串
     * @return QueryBuilder
     */
    public QueryBuilder containsQuery(String id, String substring) {
        Attribute<Row, String> attribute = (Attribute<Row, String>) indexer.getAttribute(id);
        queries.add(contains(attribute, substring));
        return this;
    }


    /**
     * 执行并查询
     *
     * @return r
     */
    public ResultSet<Row> executeAnd() {
        return indexer.andQuery(queries);
    }

    /**
     * 执行或查询
     *
     * @return r
     */
    public ResultSet<Row> executeOr() {
        return indexer.orQuery(queries);
    }
}
