package com.trs.police.service.node.impl.nodeDTO;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.dto.node.properties.FeatureInputProperties;
import com.trs.police.dto.node.properties.SparkFilterProperties;
import com.trs.police.service.shared.feature.FeatureFacade;
import com.trs.police.vo.RowFieldVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 特征输入节点
 */
@Component
public class FeatureInputNodeDTOBuilder extends LabelNodeDTOBuilder {
    @Autowired
    private FeatureFacade featureFacade;

    @Override
    public boolean supports(Integer nodeTypeCode) {
        return NodeType.FEATURE_IN.equals(nodeTypeCode);
    }

    @Override
    public List<LabelNodeDTO> build(NodeDTO nodeDTO, NodeContext nodeContext) {
        FeatureInputProperties property = JSON.parseObject(nodeDTO.getNodeProperties(), FeatureInputProperties.class);
        ProcessDTO process = featureFacade.getFeatureProcess(property.getFeatureId());
        final LabelNodeDTOBuilderFactory labelNodeDtoBuilderFactory = BeanFactoryHolder.getBean(LabelNodeDTOBuilderFactory.class).get();
        NodeContext featureNodeContext = new NodeContext(process.getControl());
        addControlValue(process.getControl(), property.getControlValue(), featureNodeContext);
        featureNodeContext.setNodes(process.getNodes());
        featureNodeContext.setNodeOrders(process.getNodeOrders());
        List<NodeDTO> sortedNodes = labelNodeDtoBuilderFactory.sortNodes(process.getNodes(), process.getNodeOrders());
        List<LabelNodeDTO> res = sortedNodes.stream().map(dto -> labelNodeDtoBuilderFactory.buildByStrategy(dto, featureNodeContext)).flatMap(List::stream).collect(Collectors.toList());
        // 添加一个空条件的过滤节点，来过滤输出字段
        SparkFilterProperties sparkFilterProperties = new SparkFilterProperties("");
        List<RowFieldVo> outputRow = nodeDTO.getNodeMeta().getOutputRowMeta().getValueMetaList().stream().map(valueMateBase -> RowFieldVo.of(valueMateBase.getEnName(), valueMateBase.getCol())).collect(Collectors.toList());
        res.add(new LabelNodeDTO(NodeType.FILTER, JSONObject.toJSONString(sparkFilterProperties), outputRow, nodeDTO.getLastNodeId()));

        return res;
    }
} 