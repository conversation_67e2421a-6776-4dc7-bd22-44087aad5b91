package com.trs.police.service.feature.domain.repository;

import com.trs.police.service.feature.domain.entity.Feature;

/**
 * 特征持久化
 *
 * <AUTHOR>
 */
public interface FeatureRepository {

    /**
     * 保存特征
     *
     * @param feature 特征
     * @return 特征
     */
    Feature add(Feature feature);

    /**
     * 更新特征
     *
     * @param feature 特征
     * @return 特征
     */
    Feature update(Feature feature);

    /**
     * 删除特征
     *
     * @param feature 特征
     * @return 删除结果
     */
    Boolean delete(Feature feature);
}
