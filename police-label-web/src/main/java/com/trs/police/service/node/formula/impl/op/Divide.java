package com.trs.police.service.node.formula.impl.op;

import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.formula.Operator;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.trs.police.constant.node.NodeConstants.NUMBER;

/**
 * 除法
 *
 * <AUTHOR>
 */
public class Divide implements Operator {

    public static final Divide INSTANCE = new Divide();

    private Divide() {
    }

    @Override
    public FieldValue calculate(FieldValue left, FieldValue right) {
        if (!DataBaseFieldMappingType.NUMBER.getFieldType().equals(left.getTypeCode())
                || !DataBaseFieldMappingType.NUMBER.getFieldType().equals(right.getTypeCode())) {
            throw new RuntimeException("除法运算要求两个参数都是数字");
        }
        Double v = BigDecimal.valueOf(Double.valueOf(left.getValue()))
                .divide(BigDecimal.valueOf(Double.valueOf(right.getValue())), 10, RoundingMode.HALF_UP)
                .doubleValue();
        return new FieldValue(v.toString(), NUMBER);
    }
}
