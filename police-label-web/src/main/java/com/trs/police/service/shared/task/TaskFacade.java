package com.trs.police.service.shared.task;

import com.trs.police.entity.label.LabelCalculationTaskDO;
import com.trs.police.service.shared.task.mapper.LabelCalculationTaskFacadeMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务外观 对内是中级 对外是外观
 */
@Component
public class TaskFacade {

    @Autowired
    private LabelCalculationTaskFacadeMapper labelCalculationTaskFacadeMapper;

    /**
     * 获取所有标签定时任务执行
     *
     * @return 标签定时任务执行情况
     */
    public List<LabelCalculationTaskDTO> allLabelCalculationTasks() {
        List<LabelCalculationTaskDO> dos = labelCalculationTaskFacadeMapper.allLabelCalculationTasks();
        return dos.stream()
                .map(d -> {
                    LabelCalculationTaskDTO dto = new LabelCalculationTaskDTO();
                    BeanUtils.copyProperties(d, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }
}
