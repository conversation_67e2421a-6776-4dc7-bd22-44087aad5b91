package com.trs.police.service.node.formula.function;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.node.NodeConstants;
import com.trs.police.service.node.NodeFunction;

import java.util.List;

import static com.trs.common.utils.TimeUtils.YYYYMMDD;

/**
 * 当前时间
 *
 * <AUTHOR>
 */
public class CurdateNodeFunction implements NodeFunction {

    public static final CurdateNodeFunction INSTANCE = new CurdateNodeFunction();

    private CurdateNodeFunction() {
    }

    @Override
    public FieldValue execute(List<FieldValue> parameters) {
        return new FieldValue(TimeUtils.getCurrentDate(YYYYMMDD + " 00:00:00"), NodeConstants.DATETIME);
    }

    @Override
    public String key() {
        return "curdate";
    }
}
