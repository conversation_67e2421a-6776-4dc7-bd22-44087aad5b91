package com.trs.police.service.node.impl;

import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.common.core.vo.node.Row;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.NewFieldProperties;
import com.trs.police.dto.node.properties.bean.NewField;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;
import com.trs.police.service.node.formula.FormulaContext;
import com.trs.police.service.node.formula.FormulaParser;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 新增字段节点
 *
 * <AUTHOR>
 */
@Slf4j
public class NewFieldNode extends Node {

    public NewFieldNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        NewFieldProperties properties =  getPropertyAs(NewFieldProperties.class);
        NodeData inNode = inputNode.get(0);

        NodeData nodeData = new NodeData();

        // 构造表头
        List<NewFieldWrapper> newFieldWrapperList = properties.getOutValue().stream()
                .map(outField -> {
                    FieldInfoVO fieldInfoVO = new FieldInfoVO(outField.getCnName(), outField.getEnName(), outField.getTypeCode(), nodeMeta.getUuid());
                    return new NewFieldWrapper(outField, fieldInfoVO);
                })
                .collect(Collectors.toList());
        List<FieldInfoVO> outHd = new ArrayList<>();
        outHd.addAll(inNode.getHeader());
        outHd.addAll(newFieldWrapperList.stream().map(NewFieldWrapper::getFieldInfoVO).collect(Collectors.toList()));
        nodeData.setHeader(outHd);

        // 添加字段
        List<Row> data = inputNode.get(0).getRowList();
        List<Row> out = data.stream()
                .map(in -> add(inNode, in, newFieldWrapperList))
                .collect(Collectors.toList());
        // 构造返回结构
        nodeData.setRowList(out);
        nodeData.setNodeMeta(nodeMeta);
        nodeData.setTotalCount(BigDecimal.valueOf(out.size()).longValue());

        return nodeData;
    }

    @Override
    public Integer nodeType() {
        return NodeType.NEW_FIELD;
    }

    private Row add(NodeData inputNode, Row input, List<NewFieldWrapper> newFieldWrapperList) {
        List<FieldValue> out = new ArrayList<>();
        out.addAll(input.getRowData());
        for (NewFieldWrapper wp : newFieldWrapperList) {
            // 构造新字段
            FieldValue fieldValue = new FieldValue("", wp.getFieldInfoVO());
            // 计算值
            try {
                NewField newField = wp.getNewField();
                String string = newField.getValue();
                FieldValue evaluate = FormulaParser.parse(string)
                        .evaluate(new FormulaContext(inputNode, input));
                fieldValue.setValue(evaluate.getValue());
            } catch (Exception e) {
                log.error("公式计算错误, 执行公式：{}", wp.getNewField().getValue(), e);
            }
            out.add(fieldValue);
        }
        return new Row(out);
    }

    @Data
    private static class NewFieldWrapper {
        private NewField newField;
        private FieldInfoVO fieldInfoVO;

        public NewFieldWrapper(NewField newField, FieldInfoVO fieldInfoVO) {
            this.newField = newField;
            this.fieldInfoVO = fieldInfoVO;
        }
    }
}
