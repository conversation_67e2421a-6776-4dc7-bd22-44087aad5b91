package com.trs.police.service.label;

import com.trs.police.entity.label.LabelCalculationTaskDO;
import com.trs.police.entity.label.LabelDO;
import com.trs.police.enums.LabelCalculationStatus;
import com.trs.police.mapper.LabelCalculationTaskMapper;
import com.trs.police.mapper.LabelMapper;
import com.trs.police.service.label.impl.LabelCalculationManager;
import com.trs.police.vo.label.SubmitInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * 标签计算执行器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LabelCalculationExecutor {

    @Autowired
    private LabelCalculationTaskMapper taskMapper;

    @Autowired
    private LabelMapper labelMapper;

    @Autowired
    private LabelCalculationManager labelCalculationManager;

    /**
     * 异步执行标签计算
     *
     * @param taskId 任务ID
     * @param labelId 标签ID
     * @param triggerType 触发类型 0=手动 1=定时
     * @return 异步结果
     */
    @Async("dataSourceTaskExecutor")
    public CompletableFuture<Boolean> executeCalculation(String taskId, Long labelId, Integer triggerType) {
        log.info("开始执行标签计算任务: taskId={}, labelId={}, triggerType={}", taskId, labelId, triggerType);

        LabelCalculationTaskDO task = null;
        try {
            // 查询标签信息
            LabelDO label = labelMapper.selectById(labelId);
            if (label == null) {
                log.error("标签不存在: labelId={}", labelId);
                return CompletableFuture.completedFuture(false);
            }

            // 创建任务记录
            task = createTask(taskId, label, triggerType);
            taskMapper.insert(task);

            // 更新任务状态为运行中
            updateTaskStatus(taskId, LabelCalculationStatus.RUNNING, null, null);

            // 执行实际的标签计算逻辑
            SubmitInfoVo result = performCalculation(label, taskId);

            //更新jobid
            task.setStatusEnum(LabelCalculationStatus.RUNNING);
            task.setJobId(result.getSubmitId());
            taskMapper.updateById(task);

            return CompletableFuture.completedFuture(true);
        } catch (Exception e) {
            log.error("标签计算任务执行失败: taskId={}, labelId={}", taskId, labelId, e);
            
            // 更新任务状态为失败
            updateTaskStatus(taskId, LabelCalculationStatus.FAILED, LocalDateTime.now(), e.getMessage());
            
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * 创建任务记录
     *
     * @param taskId 任务ID
     * @param label 标签信息
     * @param triggerType 触发类型
     * @return 任务记录
     */
    private LabelCalculationTaskDO createTask(String taskId, LabelDO label, Integer triggerType) {
        LabelCalculationTaskDO task = new LabelCalculationTaskDO();
        task.setTaskId(taskId);
        task.setLabelId(label.getId());
        task.setLabelName(label.getLabelName());
        task.setTriggerType(triggerType);
        task.setStatusEnum(LabelCalculationStatus.PENDING);
        task.setStartTime(LocalDateTime.now());
        return task;
    }

    /**
     * 执行实际的标签计算逻辑
     *
     * @param label 标签信息
     * @param taskId 任务ID
     * @return 计算结果
     */
    private SubmitInfoVo performCalculation(LabelDO label, String taskId) {
        return labelCalculationManager.submitLabelCalculation(label.getId(), taskId);
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param endTime 结束时间
     * @param errorMessage 错误信息
     */
    private void updateTaskStatus(String taskId, LabelCalculationStatus status, 
                                 LocalDateTime endTime, String errorMessage) {
        try {
            taskMapper.updateTaskStatus(taskId, status.getCode(), endTime, errorMessage, LocalDateTime.now());
            log.debug("更新任务状态: taskId={}, status={}", taskId, status.getCode());
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, status={}", taskId, status.getCode(), e);
        }
    }

    /**
     * 更新任务结果
     *
     * @param taskId 任务ID
     * @param result 计算结果
     */
    private void updateTaskResult(String taskId, CalculationResult result) {
        try {
            taskMapper.updateTaskResult(taskId,
                    result.getProcessedCount(),
                    result.getSuccessCount(),
                    result.getFailedCount(),
                    result.getExecutionLog(),
                    LocalDateTime.now());
            log.debug("更新任务结果: taskId={}, result={}", taskId, result);
        } catch (Exception e) {
            log.error("更新任务结果失败: taskId={}", taskId, e);
        }
    }

    /**
     * 计算结果内部类
     */
    public static class CalculationResult {
        private Long processedCount = 0L;
        private Long successCount = 0L;
        private Long failedCount = 0L;
        private String executionLog;

        // Getters and Setters
        public Long getProcessedCount() { return processedCount; }
        public void setProcessedCount(Long processedCount) { this.processedCount = processedCount; }
        
        public Long getSuccessCount() { return successCount; }
        public void setSuccessCount(Long successCount) { this.successCount = successCount; }
        
        public Long getFailedCount() { return failedCount; }
        public void setFailedCount(Long failedCount) { this.failedCount = failedCount; }
        
        public String getExecutionLog() { return executionLog; }
        public void setExecutionLog(String executionLog) { this.executionLog = executionLog; }

        @Override
        public String toString() {
            return String.format("CalculationResult{processed=%d, success=%d, failed=%d}", 
                               processedCount, successCount, failedCount);
        }
    }
}
