package com.trs.police.service.node.rowmatch;

import com.trs.police.common.core.vo.node.Row;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 匹配结果
 */
@NoArgsConstructor
@Data
public class MatchResult {

    private Row from;

    private List<Row> matched = new ArrayList<>();

    public MatchResult(Row from) {
        this.from = from;
    }

    public MatchResult(Row from, List<Row> matched) {
        this.from = from;
        this.matched = matched;
    }
}
