package com.trs.police.service.feature.application.assembler;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.dto.Position;
import com.trs.police.constant.FeatureConstant;
import com.trs.police.dto.node.*;
import com.trs.police.dto.node.properties.TableNodeProperties;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.properties.FeatureOutPutProperties;
import com.trs.police.service.shared.field.FieldFacade;
import com.trs.police.service.shared.table.TableFacade;
import com.trs.police.service.feature.application.DTO.command.FeatureAddDTO;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessDTO;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessInfoDTO;
import com.trs.police.service.feature.domain.entity.Feature;
import com.trs.police.service.shared.dict.DictReference;
import com.trs.police.service.shared.dict.DictTypeRegistry;
import com.trs.police.service.shared.table.TableItemDTO;
import com.trs.police.service.shared.table.TableReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 特征搜索参数转换器
 * 负责在应用层DTO和领域层值对象之间进行转换
 */
@Component
public class FeatureAddAssembler {

    @Autowired
    private DictTypeRegistry dictTypeRegistry;

    @Autowired
    private FeatureNodeAssembler featureNodeAssembler;

    @Autowired
    private TableFacade tableFacade;

    @Autowired
    private FieldFacade fieldFacade;

    /**
     * 将新增DTO转换为领域实体
     *
     * @param dto 新增DTO
     * @return 特征实体
     */
    public Feature toFeature(FeatureAddDTO dto) {
        if (dto == null) {
            return null;
        }

        Feature feature = new Feature();
        // 特征默认停用
        feature.setStatus(FeatureConstant.STOP);
        feature.setFeatureId(dto.getFeatureId());
        feature.setFeatureName(dto.getFeatureName());
        feature.setDescription(dto.getDescription());
        feature.setBusinessRule(dto.getBusinessRule());
        feature.setTable(new TableReference(dto.getTableId()));

        // 设置分类引用
        DictReference category = new DictReference();
        category.setCode(dto.getCategoryCode());
        category.setType(dictTypeRegistry.getFeatureCategory());
        feature.setCategory(category);

        // 设置警种引用
        DictReference policeKind = new DictReference();
        policeKind.setCode(dto.getPoliceKind());
        policeKind.setType(dictTypeRegistry.getPoliceKind());
        feature.setPoliceKind(policeKind);
        feature.setMainObject(new DictReference(dto.getMainObjectCode(), dictTypeRegistry.getFeatureMainObject()));

        // 初始化特征节点（输入节点和输出节点）
        FeatureProcessDTO processDTO = initFeatureProcess(feature);
        String string = featureNodeAssembler.toString(processDTO);
        feature.setProcessOrder(string);
        return feature;
    }

    /**
     * 复制特征 （流程信息此时未修改）
     *
     * @param feature 被复制的特征
     * @return 新特征
     */
    public Feature copy(Feature feature) {
        Feature add = new Feature();
        BeanUtils.copyProperties(feature, add, "featureId");
        // 调整创建时间
        add.setCreateTime(LocalDateTime.now());
        // 修改名称
        add.setFeatureName(feature.getFeatureName() + "_copy");
        return add;
    }

    private FeatureProcessDTO initFeatureProcess(Feature feature) {
        // 基本信息
        FeatureProcessInfoDTO info = new FeatureProcessInfoDTO();
        info.setFeatureId(null);
        info.setName(feature.getFeatureName());
        info.setDescription(feature.getDescription());

        // 节点信息（输入节点和输出节点）
        NodeMeta inputNodeMeta = new NodeMeta();
        inputNodeMeta.initUid();
        TableItemDTO table = tableFacade.findByIds(Arrays.asList(feature.getTable().getTableId())).get(0);
        inputNodeMeta.setName(table.getTableNameCn());
        inputNodeMeta.setNodeTypeCode(NodeType.TABLE);
        inputNodeMeta.setPosition(new Position("0", "0"));
        List<ValueMateBase> fs = fieldFacade.findByTable(feature.getTable())
                .stream()
                .map(field -> {
                    ValueMateBase valueMateBase = new ValueMateBase(inputNodeMeta.getUuid(), field.getFieldNameCn(), field.getFieldName(), field.getFieldType());
                    return valueMateBase;
                })
                .collect(Collectors.toList());
        inputNodeMeta.setOutputRowMeta(new RowMeta(fs));

        NodeDTO input = new NodeDTO();

        input.setNodeMeta(inputNodeMeta);
        TableNodeProperties tableNodeProperties = new TableNodeProperties();
        tableNodeProperties.setTableId(feature.getTable().getTableId());
        tableNodeProperties.setTokens("");
        input.setNodeProperties(JSON.toJSONString(tableNodeProperties));

        List<NodeDTO> nodes = new ArrayList<>();
        nodes.add(input);

        NodeMeta outputNodeMeta = new NodeMeta();
        outputNodeMeta.initUid();
        outputNodeMeta.setName(feature.getFeatureName());
        outputNodeMeta.setNodeTypeCode(NodeType.FEATURE_OUT);
        outputNodeMeta.setPosition(new Position("400", "0"));
        outputNodeMeta.setOutputRowMeta(new RowMeta(new ArrayList<>()));

        NodeDTO output = new NodeDTO();

        output.setNodeMeta(outputNodeMeta);
        FeatureBaseInfo baseInfo = new FeatureBaseInfo();
        baseInfo.setFeatureName(feature.getFeatureName());
        baseInfo.setDescription(feature.getDescription());
        baseInfo.setPoliceKind(feature.getPoliceKind().getCode());
        baseInfo.setCategoryCode(feature.getCategory().getCode());
        FeatureOutPutProperties outPutProperties = new FeatureOutPutProperties();
        outPutProperties.setBaseInfo(baseInfo);
        outPutProperties.setMainObject(new ArrayList<>());
        output.setNodeProperties(JSON.toJSONString(outPutProperties));

        nodes.add(output);


        // 节点编排信息
        FeatureProcessDTO process = new FeatureProcessDTO();
        process.setNodes(nodes);
        process.setNodeOrders(new ArrayList<>());

        // 控制参数
        process.setControl(new ArrayList<>());
        process.setInfo(info);
        return process;
    }
}
