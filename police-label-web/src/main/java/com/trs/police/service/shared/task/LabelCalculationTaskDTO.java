package com.trs.police.service.shared.task;

import com.trs.police.enums.LabelCalculationStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 标签计算任务实体
 *
 * <AUTHOR>
 */
@Data
public class LabelCalculationTaskDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户主键
     */
    private Long createUserId;

    /**
     * 创建单位主键
     */
    private Long createDeptId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户主键
     */
    private Long updateUserId;

    /**
     * 更新单位主键
     */
    private Long updateDeptId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 标签ID
     */
    private Long labelId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 触发类型 0=手动 1=定时
     */
    private Integer triggerType;

    /**
     * jobId
     */
    private String jobId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long duration;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行日志
     */
    private String executionLog;

    /**
     * 是否已删除 0=未删除 1=已删除
     */
    private Integer deleted = 0;

    /**
     * 获取状态枚举
     *
     * @return 状态枚举
     */
    public LabelCalculationStatus getStatusEnum() {
        return status != null ? LabelCalculationStatus.fromCode(status) : null;
    }
}
