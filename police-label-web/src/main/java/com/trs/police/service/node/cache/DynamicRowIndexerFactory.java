package com.trs.police.service.node.cache;

import com.googlecode.cqengine.ConcurrentIndexedCollection;
import com.googlecode.cqengine.IndexedCollection;
import com.googlecode.cqengine.attribute.Attribute;
import com.googlecode.cqengine.attribute.SimpleNullableAttribute;
import com.googlecode.cqengine.index.Index;
import com.googlecode.cqengine.index.hash.HashIndex;
import com.googlecode.cqengine.index.navigable.NavigableIndex;
import com.googlecode.cqengine.query.Query;
import com.googlecode.cqengine.query.option.QueryOptions;
import com.googlecode.cqengine.resultset.ResultSet;
import com.trs.police.common.core.constant.FieldTypeMapping;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.common.core.vo.node.Row;
import com.trs.police.common.core.vo.node.ValueFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.googlecode.cqengine.query.QueryFactory.*;

/**
 * 动态行索引工厂类
 *
 * <AUTHOR>
 */
public class DynamicRowIndexerFactory {

    // 缓存已创建的索引器
    private static final Map<String, RowIndexer> INDEXER_CACHE = new ConcurrentHashMap<>();

    // 私有构造方法，使用构建器模式
    private DynamicRowIndexerFactory() {}

    /**
     * 创建索引器构建器
     *
     * @param rows r
     * @return b
     */
    public static Builder builder(List<Row> rows) {
        return new Builder(rows);
    }

    /**
     * 构建器类
     */
    public static class Builder {
        private final List<Row> rows;
        private final Set<String> fieldsToIndex = new HashSet<>();
        private final Map<String, FieldTypeMapping> fieldTypes = new HashMap<>();

        public Builder(List<Row> rows) {
            this.rows = rows;
        }

        /**
         * 添加需要索引的字段
         *
         * @param id 字段英文名称
         * @param type 字段类型
         * @return b
         */
        public Builder addIndexField(String id, FieldTypeMapping type) {
            this.fieldsToIndex.add(id);
            this.fieldTypes.put(id, type);
            return this;
        }

        /**
         * 构建RowIndexer
         *
         * @return r
         */
        public RowIndexer build() {
            // 生成缓存键
            String cacheKey = generateCacheKey();

            // 检查缓存
            if (INDEXER_CACHE.containsKey(cacheKey)) {
                return INDEXER_CACHE.get(cacheKey);
            }

            // 创建新的索引器
            RowIndexer indexer = new RowIndexer(rows, fieldsToIndex, fieldTypes);
            INDEXER_CACHE.put(cacheKey, indexer);
            return indexer;
        }

        private String generateCacheKey() {
            List<String> sortedFields = new ArrayList<>(fieldsToIndex);
            Collections.sort(sortedFields);

            StringBuilder sb = new StringBuilder();
            for (String field : sortedFields) {
                sb.append(field).append(":").append(fieldTypes.get(field)).append(";");
            }
            return sb.toString();
        }
    }

    /**
     * 动态Row索引器
     */
    public static class RowIndexer {
        private final IndexedCollection<Row> indexedCollection;
        private final Map<String, Attribute<Row, ?>> attributeCache = new ConcurrentHashMap<>();

        public RowIndexer(List<Row> rows, Set<String> fieldsToIndex, Map<String, FieldTypeMapping> fieldTypes) {
            this.indexedCollection = new ConcurrentIndexedCollection<>();
            this.indexedCollection.addAll(rows);

            // 为每个需要索引的字段创建索引
            for (String field : fieldsToIndex) {
                FieldTypeMapping type = fieldTypes.get(field);
                Attribute<Row, ?> attribute = createAttribute(field, type);
                attributeCache.put(field, attribute);

                // 根据类型选择合适的索引
                Index<Row> index = createIndexForType(attribute, type);
                this.indexedCollection.addIndex(index);
            }
        }

        private Attribute<Row, ?> createAttribute(String id, FieldTypeMapping type) {
            switch (type) {
                case STRING:
                    return new SimpleNullableAttribute<Row, String>(id + "Attr") {
                        @Override
                        public String getValue(Row row, QueryOptions queryOptions) {
                            return (String) ValueFactory.getValue(getFieldValue(row, id), type);
                        }
                    };
                case NUMBER:
                    return new SimpleNullableAttribute<Row, Double>(id + "Attr") {
                        @Override
                        public Double getValue(Row row, QueryOptions queryOptions) {
                            return (Double) ValueFactory.getValue(getFieldValue(row, id), type);
                        }
                    };
                case DATETIME:
                    return new SimpleNullableAttribute<Row, Date>(id + "Attr") {
                        @Override
                        public Date getValue(Row row, QueryOptions queryOptions) {
                            return (Date) ValueFactory.getValue(getFieldValue(row, id), type);
                        }
                    };
                default:
                    return new SimpleNullableAttribute<Row, String>(id + "Attr") {
                        @Override
                        public String getValue(Row row, QueryOptions queryOptions) {
                            return (String) ValueFactory.getValue(getFieldValue(row, id), type);
                        }
                    };
            }
        }

        private Index<Row> createIndexForType(Attribute<Row, ?> attribute, FieldTypeMapping type) {
            switch (type) {
                case STRING:
                    // 对于字符串，默认使用HashIndex，也可以根据需要改为RadixTreeIndex或SuffixTreeIndex
                    return HashIndex.onAttribute((Attribute<Row, String>) attribute);
                case NUMBER:
                case DATETIME:
                    // 对于数值和日期，使用NavigableIndex支持范围查询
                    return NavigableIndex.onAttribute((Attribute<Row, Comparable>) attribute);
                default:
                    return HashIndex.onAttribute((Attribute<Row, String>) attribute);
            }
        }

        private String getFieldValue(Row row, String id) {
            return row.getRowData().stream()
                    .filter(fv -> id.equals(fv.getFieldInfo().getId()))
                    .map(FieldValue::getValue)
                    .findFirst()
                    .orElse(null);
        }

        // ========== 查询方法 ==========

        /**
         * 多条件AND查询
         *
         * @param queries q
         * @return r
         */
        public ResultSet<Row> andQuery(List<Query<Row>> queries) {
            if (queries == null || queries.isEmpty()) {
                throw new IllegalArgumentException("Queries list cannot be null or empty");
            }

            if (queries.size() == 1) {
                return indexedCollection.retrieve(queries.get(0));
            }

            // 使用第一个查询作为基准，逐步构建AND查询
            Query<Row> combinedQuery = queries.get(0);
            for (int i = 1; i < queries.size(); i++) {
                combinedQuery = and(combinedQuery, queries.get(i));
            }
            return indexedCollection.retrieve(combinedQuery);
        }

        /**
         * 多条件OR查询
         *
         * @param queries q
         * @return r
         */
        public ResultSet<Row> orQuery(List<Query<Row>> queries) {
            if (queries == null || queries.isEmpty()) {
                throw new IllegalArgumentException("Queries list cannot be null or empty");
            }

            if (queries.size() == 1) {
                return indexedCollection.retrieve(queries.get(0));
            }

            // 使用第一个查询作为基准，逐步构建OR查询
            Query<Row> combinedQuery = queries.get(0);
            for (int i = 1; i < queries.size(); i++) {
                combinedQuery = or(combinedQuery, queries.get(i));
            }
            return indexedCollection.retrieve(combinedQuery);
        }


        /**
         * 获取字段索引
         *
         * @param fieldName f
         * @return r
         */
        public Attribute<Row, ?> getAttribute(String fieldName) {
            Attribute<Row, ?> attribute = attributeCache.get(fieldName);
            if (attribute == null) {
                throw new IllegalArgumentException("No index created for field: " + fieldName);
            }
            return attribute;
        }
    }
}
