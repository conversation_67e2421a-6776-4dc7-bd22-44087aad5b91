package com.trs.police.service.feature.infrastructure.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.service.feature.domain.entity.Feature;
import com.trs.police.service.feature.domain.repository.FeatureRepository;
import com.trs.police.entity.feature.FeatureDO;
import com.trs.police.mapper.FeatureMapper;
import com.trs.police.service.feature.infrastructure.repository.converter.FeatureConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 特征command mp实现
 */
@Component
public class FeatureRepositoryMyBatisImpl implements FeatureRepository {

    @Resource
    private FeatureMapper featureMapper;

    @Autowired
    private FeatureConverter featureConverter;

    @Override
    public Feature add(Feature feature) {
        FeatureDO featureDO = featureConverter.toFeatureDO(feature);
        featureMapper.insert(featureDO);
        feature.setFeatureId(featureDO.getId());
        return feature;
    }

    @Override
    public Feature update(Feature feature) {
        FeatureDO featureDO = featureConverter.toFeatureDO(feature);
        featureMapper.updateById(featureDO);
        if (StringUtils.isEmpty(featureDO.getProcessOrderSnapshot())) {
            featureMapper.update(
                    null,
                    Wrappers.lambdaUpdate(FeatureDO.class)
                            .eq(FeatureDO::getId, featureDO.getId())
                            .set(FeatureDO::getProcessOrderSnapshot, null)
            );
        }
        return feature;
    }

    @Override
    public Boolean delete(Feature feature) {
        featureMapper.deleteById(feature.getFeatureId());
        return Boolean.TRUE;
    }
}
