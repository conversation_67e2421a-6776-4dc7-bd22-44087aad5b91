package com.trs.police.service.baseService.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.db.sdk.exception.TrsDDLException;
import com.trs.db.sdk.meta.MetaDataMaintainer;
import com.trs.db.sdk.meta.data.MetaDataTable;
import com.trs.mq.utils.CollectionUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.TableVO;
import com.trs.police.entity.baseEntiry.AuthCertificateKerberos;
import com.trs.police.entity.baseEntiry.Formula;
import com.trs.police.entity.dataField.DataField;
import com.trs.police.entity.datasource.AbstractDbSourceInfo;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.police.entity.datatable.DataTable;
import com.trs.police.mapper.*;
import com.trs.police.service.baseService.CommonService;
import com.trs.police.common.core.constant.FieldTypeMapping;
import com.trs.police.utils.RepositoryUtil;
import com.trs.police.utils.SourceInfoConverter;
import com.trs.police.vo.common.FormulaVO;
import io.vavr.control.Either;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DDL实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    private static final String BASE_DIRECTORY = "/TRS/DATA/kerberos/";

    @Resource
    private DataSourceMapper dataSourceMapper;

    @Resource
    private DataTableMapper dataTableMapper;

    @Resource
    private DataFieldMapper dataFieldMapper;

    @Resource
    private AuthCertificateKerberosMapper authCertificateKerberosMapper;

    @Resource
    private FormulaMapper formulaMapper;

    @Override
    public void saveTableAndFieldData(DataSource entity) throws Exception {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        SourceInfo sourceData = SourceInfoConverter.fromJson(entity.getSourceInfo(), entity.getType());
        //获取该数据源下所有数据表信息
        List<TableVO> tableInfo = getTableInfo(sourceData);
        // 批量插入数据表数据
        List<Table> dataTables = insertDataTable(entity, tableInfo, currentUser);
        // 批量插入字段数据
        insertDataField(dataTables, currentUser);
    }

    @Override
    public void refreshTableAndFieldData(DataSource entity) throws Exception {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        SourceInfo sourceData = SourceInfoConverter.fromJson(entity.getSourceInfo(), entity.getType());
        List<TableVO> tableInfo = getTableInfo(sourceData);

        // 新增表
        List<DataTable> saved = dataTableMapper.selectList(
                Wrappers.lambdaQuery(DataTable.class)
                        .eq(DataTable::getDataSourceId, entity.getId())
        );
        List<TableVO> tables = tableInfo.stream()
                .filter(tableVO -> {
                    return  !saved.stream().anyMatch(savedTable ->
                            savedTable.getTableName().equals(tableVO.getTableName()));
                })
                .collect(Collectors.toList());
        if (!tables.isEmpty()) {
            // 批量插入数据表数据
            List<Table> dataTables = insertDataTable(entity, tables, currentUser);
            // 批量插入字段数据
            insertDataField(dataTables, currentUser);
        }

        // 更新已经存在的表
        List<Table> savedTable = tableInfo.stream()
                .map(tableVO -> {
                    Optional<DataTable> match = saved.stream()
                            .filter(stb -> stb.getTableName().equals(tableVO.getTableName()))
                            .findAny();
                    if (match.isPresent()) {
                        return new Table(tableVO, match.get().getId());
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        for (Table table : savedTable) {
            refreshTable(table);
        }
    }

    @Override
    public void refreshTableById(Long tableId) throws Exception {
        TableVO tableVO = getTableInfoById(tableId);
        if (Objects.isNull(tableVO)) {
            return;
        }
        refreshTable(new Table(tableVO, tableId));
    }

    private TableVO getTableInfoById(Long tableId) throws Exception {
        DataTable table = dataTableMapper.selectById(tableId);
        Long dataSourceId = table.getDataSourceId();
        DataSource dataSource = dataSourceMapper.selectById(dataSourceId);
        SourceInfo sourceInfo = SourceInfoConverter.fromJson(dataSource.getSourceInfo(), dataSource.getType());
        List<TableVO> tableInfo = getTableInfo(sourceInfo);
        Optional<TableVO> any = tableInfo.stream()
                .filter(tableVO -> tableVO.getTableName().equals(table.getTableName()))
                .findAny();
        return any.orElse(null);
    }

    private void refreshTable(Table table) throws Exception {
        if (Objects.isNull(table) || Objects.isNull(table.getSavedId())) {
            return;
        }
        // 构造全新字段
        List<DataField> dataFields = buildDataFiled(table, AuthHelper.getCurrentUser());

        // 插入新增字段
        List<DataField> savedFields = dataFieldMapper.selectList(
                Wrappers.lambdaQuery(DataField.class)
                        .eq(DataField::getTableId, table.getSavedId())
        );
        List<DataField> insertDataFields = dataFields.stream()
                .filter(field -> savedFields.stream().noneMatch(savedField -> savedField.getFieldName().equals(field.getFieldName())))
                .collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(insertDataFields)) {
            dataFieldMapper.insertBatch(insertDataFields);
        }

        // 修改删除的字段状态
        List<DataField> deleteDataFields = savedFields.stream()
                .filter(savedField -> dataFields.stream().noneMatch(field -> field.getFieldName().equals(savedField.getFieldName())))
                .collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(deleteDataFields)) {
            List<Long> did = deleteDataFields.stream()
                    .map(DataField::getId)
                    .collect(Collectors.toList());
            dataFieldMapper.update(
                    null,
                    Wrappers.lambdaUpdate(DataField.class)
                            .in(DataField::getId, did)
                            .set(DataField::getStatus, 1)
            );
        }
    }



    /**
     * 批量插入数据表数据
     *
     * @param entity     数据源
     * @param tableInfo  表信息
     * @param currentUser 当前用户
     * @return 数据表集合
     */
    private List<Table> insertDataTable(DataSource entity, List<TableVO> tableInfo, CurrentUser currentUser) {
        // 批量插入数据表
        List<Table> dataTables = new ArrayList<>();
        for (TableVO tableVO : tableInfo) {
            DataTable dataTable = new DataTable();
            if (Objects.nonNull(currentUser)){
                dataTable.fillAuditFields(currentUser);
            }
            dataTable.setDataSourceId(entity.getId());
            dataTable.setTableType("VIEW".equals(tableVO.getTableComment()) ? "view" : "table");
            dataTable.setTableName(tableVO.getTableName());
            dataTable.setTableNameCn(StringUtils.isEmpty(tableVO.getTableComment())
                    || "VIEW".equals(tableVO.getTableComment())
                    ? tableVO.getTableName() : tableVO.getTableComment());
            dataTable.setAliaseName(tableVO.getAliaseName());
            int insert = dataTableMapper.insert(dataTable);
            dataTables.add(new Table(tableVO, dataTable.getId()));
        }
        return dataTables;
    }

    /**
     * 批量插入字段数据
     *
     * @param tableInfo  表信息
     * @param currentUser 当前用户
     */
    private void insertDataField(List<Table> tableInfo, CurrentUser currentUser) {
        // 批量插入字段数据
        List<DataField> dataFields = new ArrayList<>();
        for (int i = 0; i < tableInfo.size(); i++) {
            Table tb = tableInfo.get(i);
            // 构造字段
            dataFields.addAll(buildDataFiled(tb, currentUser));
        }
        // 分批执行字段批量插入，每批500条
        if (!dataFields.isEmpty()) {
            int batchSize = 500;
            for (int i = 0; i < dataFields.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, dataFields.size());
                List<DataField> batch = dataFields.subList(i, endIndex);
                dataFieldMapper.insertBatch(batch);
            }
            log.info("批量插入{}个字段", dataFields.size());
        }
    }

    private List<DataField> buildDataFiled(Table table, CurrentUser currentUser) {
        TableVO tableVO = table.getTableInfo();
        Long tableId = table.getSavedId();
        List<DataField> dataFields = new ArrayList<>();
        if (tableVO.getFieldList() != null && !tableVO.getFieldList().isEmpty()) {
            tableVO.getFieldList().forEach(fieldVO -> {
                DataField dataField = new DataField();
                if (Objects.nonNull(currentUser)){
                    dataField.fillAuditFields(currentUser);
                }
                dataField.setTableId(tableId);
                dataField.setFieldName(fieldVO.getName());
                dataField.setFieldOriginalType(fieldVO.getOriginalType());
                // 处理字段中文名称和描述
                String description = fieldVO.getDescription();
                if (StringUtils.isNotEmpty(description)) {
                    // 尝试从描述中提取中文名称和详细描述
                    String[] parts = extractNameAndDescription(description);
                    dataField.setFieldNameCn(parts[0]);
                    dataField.setFieldDescription(parts[1]);
                } else {
                    // 如果没有描述，则使用字段名作为中文名称
                    dataField.setFieldNameCn(fieldVO.getName());
                }
                // 如果原始类型涉及空间类型的，特俗处理
                if ("geo_point".equals(fieldVO.getOriginalType()) || "point".equals(fieldVO.getOriginalType())) {
                    dataField.setFieldType(FieldTypeMapping.GEO.getName());
                } else {
                    dataField.setFieldType(FieldTypeMapping.fromType(fieldVO.getType()).getName());
                }
                dataFields.add(dataField);
            });
        }
        return dataFields;
    }


    /**
     * 获取数据源中的表信息
     *
     * @param sourceInfo 数据源信息
     * @return 表信息
     * @throws Exception Exception
     */
    @Override
    public List<TableVO> getTableInfo(SourceInfo sourceInfo) throws Exception {
        // 获取连接
        MetaDataMaintainer metaDataMaintainer = getMetaDataMaintainer(sourceInfo);

        List<TableVO> tableVOList = new ArrayList<>();
        Either<TrsDDLException, List<MetaDataTable>> either = metaDataMaintainer.getAllTableInfo();
        List<MetaDataTable> metaDataTables = either.getOrElseThrow(ex -> ex);
        if (metaDataTables != null) {
            metaDataTables.forEach(m -> {
                tableVOList.add(new TableVO(m));
            });
        }

        return tableVOList;
    }

    @Override
    @Transactional
    public void uploadKerberosFile(String userName,String type,MultipartFile file) {
        try {
            // 上传文件
            String fileName = uploadKerberos(userName, file);
            List<AuthCertificateKerberos> principalList = authCertificateKerberosMapper
                    .selectList(new QueryWrapper<AuthCertificateKerberos>().eq("principal", userName));
            if (principalList.isEmpty()){
                // 存入到数据库中
                AuthCertificateKerberos authCertificateKerberos = new AuthCertificateKerberos();
                authCertificateKerberos.setPrincipal(userName);
                setKerberosValue(type, authCertificateKerberos, fileName);
                authCertificateKerberosMapper.insert(authCertificateKerberos);
            }else {
                AuthCertificateKerberos authCertificateKerberos = principalList.get(0);
                setKerberosValue(type, authCertificateKerberos, fileName);
                authCertificateKerberosMapper.updateById(authCertificateKerberos);
            }

        } catch (Exception e) {
            throw new TRSException("文件上传失败: " + e.getMessage());
        }
    }


    @Override
    public List<String> getKerberosFileList(String userName, String type) {
        LambdaQueryWrapper<AuthCertificateKerberos> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuthCertificateKerberos::getPrincipal, userName);
        List<AuthCertificateKerberos> principalList = authCertificateKerberosMapper
                .selectList(queryWrapper);
        if (CollectionUtils.isEmpty(principalList)){
            return List.of();
        }
        if ("keytab".equals(type)){
            // 如果是keytab类型，返回keytab文件路径
            return principalList.stream()
                    .map(AuthCertificateKerberos::getKeytabPth)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
        } else {
            // 否则返回krb5.conf文件路径
            return principalList.stream()
                    .map(AuthCertificateKerberos::getKrb5Pth)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
        }
    }

    @Override
    public List<FormulaVO> getFormulaList() {
        List<Formula> formulas = formulaMapper.selectList(new QueryWrapper<>());
        return formulas.stream()
                .sorted(Comparator.comparingInt(Formula::getOrderValue))
                .map(f -> {
                    FormulaVO vo = new FormulaVO();
                    BeanUtils.copyProperties(f, vo, "parameters");
                    if (StringUtils.isNotEmpty(f.getParameters())) {
                        vo.setParameters(JSON.parseArray(f.getParameters(), FormulaVO.Parameters.class));
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 设置kerberos保存文件路径
     *
     * @param type              类型
     * @param authCertificateKerberos kerberos对象
     * @param fileName          文件名
     */
    private static void setKerberosValue(String type, AuthCertificateKerberos authCertificateKerberos, String fileName) {
        if ("keytab".equals(type)){
            authCertificateKerberos.setKeytabPth(fileName);
        }else {
            authCertificateKerberos.setKrb5Pth(fileName);
        }
    }

    /**
     * 上传Kerberos文件
     *
     * @param userName 用户名
     * @param file     文件
     * @return 文件路径
     * @throws Exception Exception
     */
    private static String uploadKerberos(String userName, MultipartFile file) throws Exception {
        if (userName == null || file.isEmpty()) {
            throw new TRSException("用户名或文件为空！");
        }
        // 确保基础目录存在，如果不存在则创建
        File baseDirectory = new File(BASE_DIRECTORY);
        if (!baseDirectory.exists()) {
            baseDirectory.mkdirs();
        }
        // 定义用户目录
        String userDirectory = BASE_DIRECTORY + userName + "/";
        // 确保用户目录存在，如果不存在则创建
        File directory = new File(userDirectory);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        //文件保存路径
        String fileName = userDirectory + file.getOriginalFilename();
        // 负责将文件保存到指定路径
        FileOutputStream outputStream = new FileOutputStream(fileName);
        outputStream.write(file.getBytes());
        outputStream.close();
        return fileName;
    }


    /**
     * 获取数据库DDL连接
     *
     * @param sourceInfo 数据源信息
     * @return 元数据维护器
     */
    private MetaDataMaintainer getMetaDataMaintainer(SourceInfo sourceInfo) {
        // 检查sourceInfo是否为AbstractDbSourceInfo的子类
        if (sourceInfo instanceof AbstractDbSourceInfo) {
            return RepositoryUtil.getMetaDataMaintainer((AbstractDbSourceInfo) sourceInfo);
        } else {
            throw new IllegalArgumentException("不支持的数据源类型: " + sourceInfo.getClass().getName());
        }
    }


    /**
     * 从字段描述中提取中文名称和详细描述
     * 支持多种分隔符：逗号、空格、分号、冒号、横杠、下划线等
     *
     * @param description 字段描述
     * @return 包含中文名称和详细描述的数组，索引0为中文名称，索引1为详细描述
     */
    private String[] extractNameAndDescription(String description) {
        String[] result = new String[2];

        // 使用正则表达式匹配第一个分隔符（逗号、分号、冒号、空格等）
        String[] parts = description.split("[,;:\\s]", 2);

        if (parts.length >= 2) {
            result[0] = parts[0].trim();
            result[1] = parts[1].trim();
        } else {
            // 如果无法分割，则将整个描述作为中文名称
            result[0] = description.trim();
            result[1] = "";
        }

        return result;
    }

    /**
     * 数据表
     *
     * <AUTHOR>
     */
    @Data
    private static class Table {

        private TableVO tableInfo;

        private Long savedId;

        public Table(TableVO tableInfo, Long savedId) {
            this.tableInfo = tableInfo;
            this.savedId = savedId;
        }
    }
}
