package com.trs.police.service.node.impl;

import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;

import java.util.List;

/**
 * 标签输出节点
 */
public class LabelOutputNode extends Node {

    public LabelOutputNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        NodeData input = inputNode.get(0);
        NodeData outPut = new NodeData();
        outPut.setNodeMeta(nodeMeta);
        outPut.setTotalCount(input.getTotalCount());
        outPut.setData(input.getData());
        outPut.setHeader(input.getHeader());
        // 标签输出节点的输出字段取决于输入字段
        nodeMeta.setOutputRowMeta(input.getNodeMeta().getOutputRowMeta());
        return outPut;
    }

    @Override
    public Integer nodeType() {
        return NodeType.LABEL_OUTPUT;
    }
}
