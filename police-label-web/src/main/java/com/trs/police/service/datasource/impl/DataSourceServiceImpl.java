package com.trs.police.service.datasource.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.connection.ConnectionTesterFactory;
import com.trs.police.constant.datasource.MpDataSourceEnum;
import com.trs.police.converter.DataSourceConverter;
import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.*;
import com.trs.police.entity.datasource.DataSource.ConnectionStatus;
import com.trs.police.factory.DataSourceDTOFactory;
import com.trs.police.mapper.DataSourceMapper;
import com.trs.police.service.baseService.CommonService;
import com.trs.police.service.datasource.DataSourceService;
import com.trs.police.utils.AesUtils;
import com.trs.police.utils.SourceInfoConverter;
import com.trs.police.vo.DataSourceVO;
import com.trs.police.vo.datasource.DataSourceGroupVO;
import com.trs.police.vo.datasource.MpDataSource;
import com.trs.police.vo.datasource.MpDataSourceResult;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据源服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataSourceServiceImpl extends ServiceImpl<DataSourceMapper, DataSource> implements DataSourceService {

    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ConnectionTesterFactory connectionTesterFactory;

    @Resource(name = "dataSourceTaskExecutor")
    private Executor dataSourceTaskExecutor;

    @Override
    @Transactional
    public RestfulResults saveDataSource(DataSourceDTO dto) {
        try {
            CurrentUser currentUser = AuthHelper.getCurrentUser();
            log.info("当前登录人信息：{}", currentUser);
            // 类型校验
            if (!DataSourceDTOFactory.matchType(dto, dto.getType())) {
                throw new IllegalArgumentException("数据源类型与DTO类型不匹配");
            }
            if (Objects.nonNull(dto.getId())){
                DataSource dataSource = this.getById(dto.getId());
                return RestfulResults.ok(DataSourceConverter.toVO(dataSource));
            }
            // 转换DTO为实体和数据源信息
            DataSource entity = DataSourceConverter.toEntity(dto);
            if (Objects.nonNull(currentUser)){
                entity.fillAuditFields(currentUser);
            }
            SourceInfo sourceInfo = DataSourceConverter.toSourceInfo(dto);
            validateParams(entity, sourceInfo);
            validateTypeConsistency(entity, sourceInfo);
            handleCreate(entity, sourceInfo);
            // 如果该数据源连接失败，直接保存数据源相关数据，但是不进行插入数据表和数据字段等操作
            // 保存数据源
            this.saveOrUpdate(entity);
            // 如果连接成功，启动新线程处理数据表和数据字段的插入
            if (entity.getConnectionStatus().equals(ConnectionStatus.CONNECTED)) {
                try {
                    commonService.saveTableAndFieldData(entity);
                } catch (Exception e) {
                    log.error("插入数据表和数据字段失败", e);
                }
                log.info("插入数据表和数据字段完成");
            }
            return RestfulResults.ok(DataSourceConverter.toVO(entity));
        }catch (Exception e){
            log.error("新建数据源失败：",e);
            return RestfulResults.error(e.getMessage());
        }
    }


    @Override
    @Transactional
    public DataSourceVO updateDataSource(DataSourceDTO dto) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        // 类型校验
        if (!DataSourceDTOFactory.matchType(dto, dto.getType())) {
            throw new IllegalArgumentException("数据源类型与DTO类型不匹配");
        }
        // 转换DTO为实体和数据源信息
        DataSource entity = DataSourceConverter.toEntity(dto);
        if (Objects.nonNull(currentUser)){
            entity.setUpdateUserId(currentUser.getId());
            entity.setUpdateDeptId(currentUser.getDeptId());
            entity.fillAuditFields();
        }
        SourceInfo sourceInfo = DataSourceConverter.toSourceInfo(dto);
        validateParams(entity, sourceInfo);
        validateTypeConsistency(entity, sourceInfo);
        handleUpdate(entity, sourceInfo);
        // 保存数据源
        this.saveOrUpdate(entity);
        return DataSourceConverter.toVO(entity);
    }

    /**
     * 验证参数是否为空。
     *
     * @param dataSource 数据源对象，不能为空
     * @param sourceInfo 源信息对象，不能为空
     */
    private void validateParams(DataSource dataSource, SourceInfo sourceInfo) {
        if (dataSource == null || sourceInfo == null) {
            throw new IllegalArgumentException("数据源信息不能为空");
        }
    }

    /**
     * 验证数据源类型的一致性。
     *
     * @param dataSource 数据源对象，包含需要验证的数据源类型信息。
     * @param sourceInfo 源信息对象，包含预期的数据源类型信息。
     * @throws IllegalArgumentException 如果数据源类型与源信息中的类型不一致，则抛出此异常。
     */
    private void validateTypeConsistency(DataSource dataSource, SourceInfo sourceInfo) {
        if (dataSource.getType() != sourceInfo.getType()) {
            throw new IllegalArgumentException("数据源类型不一致");
        }
    }

    /**
     * 处理数据源更新操作。
     *
     * @param dataSource 新的数据源信息
     * @param sourceInfo 数据源的附加信息
     */
    private void handleUpdate(DataSource dataSource, SourceInfo sourceInfo) {
        DataSource existingDataSource = getDataSourceById(dataSource.getId());
        if (existingDataSource == null) {
            throw new IllegalArgumentException("数据源不存在");
        }

        validateTypeModification(existingDataSource, dataSource);
        validateNameUpdate(existingDataSource, dataSource);

        if (existingDataSource.canBeModified() || Objects.equals(existingDataSource.getType(), dataSource.getType())) {
            updateDataSourceInfo(dataSource, sourceInfo, existingDataSource);
        }
    }


    /**
     * 验证数据源类型的修改是否合法。
     *
     * @param existingDataSource 当前已存在的数据源
     * @param dataSource         待更新的数据源
     */
    private void validateTypeModification(DataSource existingDataSource, DataSource dataSource) {
        if (!Objects.equals(existingDataSource.getType(), dataSource.getType()) && !existingDataSource.canBeModified()) {
            throw new IllegalArgumentException("数据源已关联特征/标签/模型，不能修改类型");
        }
    }

    /**
     * 验证数据源名称更新的合法性
     * 1. 检查数据源名称是否发生变更
     * 2. 如果名称变更，确保新名称未被其他数据源占用
     *
     * @param existingDataSource 原有的数据源对象
     * @param dataSource         待更新的数据源对象
     * @throws IllegalArgumentException 当新名称已被其他数据源使用时抛出
     */
    private void validateNameUpdate(DataSource existingDataSource, DataSource dataSource) {
        if (!Objects.equals(existingDataSource.getName(), dataSource.getName())) {
            DataSource nameCheck = dataSourceMapper.findByName(dataSource.getName());
            if (nameCheck != null && !Objects.equals(nameCheck.getId(), dataSource.getId())) {
                throw new IllegalArgumentException("数据源名称已存在");
            }
        }
    }

    /**
     * 更新数据源信息并保留原有关联计数
     * 1. 将源信息转换为JSON并设置
     * 2. 更新数据源连接状态
     * 3. 保留原数据源的特征、标签和模型计数
     *
     * @param dataSource         待更新的数据源对象
     * @param sourceInfo         新的源信息
     * @param existingDataSource 原有的数据源对象
     */
    private void updateDataSourceInfo(DataSource dataSource, SourceInfo sourceInfo, DataSource existingDataSource) {
        String infoJson = SourceInfoConverter.toJson(sourceInfo);
        dataSource.setSourceInfo(infoJson);

        updateConnectionStatus(dataSource, sourceInfo);

        // 保留原有的关联计数
        dataSource.setFeatureCount(existingDataSource.getFeatureCount());
        dataSource.setLabelCount(existingDataSource.getLabelCount());
        dataSource.setModelCount(existingDataSource.getModelCount());
    }


    /**
     * 处理新数据源的创建流程
     * 1. 验证数据源的合法性
     * 2. 转换数据源信息为JSON
     * 3. 初始化新数据源的基本属性
     * 4. 测试并更新数据源连接状态
     *
     * @param dataSource 待创建的数据源对象
     * @param sourceInfo 数据源详细信息
     */
    private void handleCreate(DataSource dataSource, SourceInfo sourceInfo) {
        validateNewDataSource(dataSource, sourceInfo);

        String infoJson = SourceInfoConverter.toJson(sourceInfo);
        initializeNewDataSource(dataSource, infoJson);
        updateConnectionStatus(dataSource, sourceInfo);
    }

    /**
     * 验证新数据源的合法性
     * 1. 检查数据源名称是否已存在
     * 2. 检查数据源是否重复创建
     *
     * @param dataSource 数据源对象
     * @param sourceInfo 数据源信息
     * @throws IllegalArgumentException 当数据源名称已存在或数据源重复创建时抛出
     */
    private void validateNewDataSource(DataSource dataSource, SourceInfo sourceInfo) {
        String json = SourceInfoConverter.toJson(sourceInfo);
        SourceInfo sourceData = SourceInfoConverter.fromJson(json, dataSource.getType());
        AbstractDbSourceInfo dbSourceInfo = null;
        if (sourceData instanceof AbstractDbSourceInfo) {
            dbSourceInfo = (AbstractDbSourceInfo) sourceInfo;
        }
        if (dataSourceMapper.findByName(dataSource.getName()) != null) {
            throw new IllegalArgumentException("数据源名称已存在");
        }
        if (dataSourceMapper.findByDbName(dbSourceInfo.getDbName()) != null) {
            throw new IllegalArgumentException("数据库名称已被其他数据源占用，请更换名称或检查现有数据源");
        }
        String uniqueId = sourceInfo.generateUniqueId();
        List<DataSource> existingSources =
                dataSourceMapper.findByTypeAndSourceInfoContaining(sourceInfo.getType(), uniqueId);

        if (!existingSources.isEmpty()) {
            throw new IllegalArgumentException("数据源已存在，无需重复创建");
        }
    }

    /**
     * 初始化新的数据源。
     *
     * @param dataSource 要初始化的数据源对象。
     * @param infoJson   包含数据源信息的JSON字符串。
     */
    private void initializeNewDataSource(DataSource dataSource, String infoJson) {
        dataSource.setSourceInfo(infoJson);
        dataSource.setConnectionStatus(ConnectionStatus.UNKNOWN);
        dataSource.setFeatureCount(0);
        dataSource.setLabelCount(0);
        dataSource.setModelCount(0);
    }

    /**
     * 更新数据源的连接状态。
     *
     * @param dataSource 数据源对象，用于存储连接状态和最后检查时间
     * @param sourceInfo 源信息对象，包含连接测试所需的信息
     */
    private void updateConnectionStatus(DataSource dataSource, SourceInfo sourceInfo) {
        boolean connectionOk = connectionTesterFactory.testConnection(sourceInfo);
        dataSource.setConnectionStatus(connectionOk ? ConnectionStatus.CONNECTED : ConnectionStatus.DISCONNECTED);
        dataSource.setLastCheckTime(LocalDateTime.now());
    }

    @Override
    public DataSource getDataSourceById(Long id) {
        if (id == null) {
            return null;
        }

        return this.getById(id);
    }

    @Override
    public RestfulResultsV2<DataSourceVO> findDataSources(DataSourceDTO dto) {
        // 创建查询条件
        QueryWrapper<DataSource> queryWrapper = new QueryWrapper<DataSource>()
                .eq("deleted", 0)
                .eq("create_status", 1)
                .eq(Objects.nonNull(dto.getType()), "type", dto.getType())
                .eq(StringUtils.isNotEmpty(dto.getConnectionStatus()), "connection_status", dto.getConnectionStatus())
                .like(StringUtils.isNotEmpty(dto.getSearchValue()), "name", dto.getSearchValue());
        // 创建分页对象
        Page<DataSource> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        // 转换为VO
        Page<DataSource> dataSourcePage = dataSourceMapper.selectPage(page, queryWrapper);
        Page<DataSourceVO> resultPage = new Page<>(dataSourcePage.getCurrent(), dataSourcePage.getSize(), dataSourcePage.getTotal());
        List<DataSourceVO> records = dataSourcePage.getRecords().stream()
                .map(DataSourceConverter::toVO)
                .collect(Collectors.toList());
        resultPage.setRecords(records);
        return RestfulResultsV2.ok(resultPage.getRecords())
                .addTotalCount(resultPage.getTotal())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize());
    }

    @Override
    public RestfulResultsV2<DataSourceGroupVO> listMpDataSources(DataSourceDTO dto) {
        // 获取到过滤的类型
        Optional<MpDataSourceEnum> byType = MpDataSourceEnum.getByType(dto.getType());
        // 根据dto获取数据
        List<DataSourceGroupVO> list = getMpSource(byType.map(MpDataSourceEnum::getMpType).orElse(null))
                .stream()
                .map(mp -> {
                    // 构造分组
                    MpDataSourceEnum byMpType = MpDataSourceEnum.getByMpType(mp.getDbType());
                    if (Objects.isNull(byMpType)) {
                        return null;
                    }
                    DataSourceGroupVO vo = new DataSourceGroupVO();
                    vo.setType(byMpType.getType());
                    // 构造分组下的数据
                    List<DataSourceVO> data = mp.getDbInfos()
                            .stream()
                            .filter(s -> {
                                if (StringUtils.isNotEmpty(dto.getSearchValue())) {
                                    return s.getName().contains(dto.getSearchValue());
                                }
                                return true;
                            })
                            .map(this::convertToVO)
                            .collect(Collectors.toList());
                    vo.setData(data);
                    return vo;
                })
                .filter(Objects::nonNull)
                .filter(s -> {
                    if (Objects.isNull(dto.getType())) {
                        return true;
                    }
                    return Objects.equals(s.getType(), dto.getType());
                })
                .collect(Collectors.toList());
        // 逻辑分页
        RestfulResultsV2<DataSourceGroupVO> ok = RestfulResultsV2.ok(list);
        return ok;
    }

    @Override
    @Transactional
    public void deleteDataSource(Long id) {
        DataSource dataSource = getDataSourceById(id);
        if (dataSource == null) {
            throw new IllegalArgumentException("数据源不存在");
        }

        // 检查是否可以删除
        if (!dataSource.canBeDeleted()) {
            throw new IllegalArgumentException("数据源已关联特征/标签/模型，无法删除");
        }
        dataSource.setDeleted(1);
        // 执行逻辑删除
        this.updateById(dataSource);
    }

    @Override
    public boolean checkConnection(DataSourceDTO dto) {
        // 类型校验
        if (!DataSourceDTOFactory.matchType(dto, dto.getType())) {
            throw new IllegalArgumentException("数据源类型与DTO类型不匹配");
        }
        // 使用连接测试工厂执行连接测试
        return connectionTesterFactory.testConnection(dto.getSourceInfo());
    }

    @Override
    @Transactional
    public DataSource updateRelationStatus(Long id, int featureCount, int labelCount, int modelCount) {
        DataSource dataSource = getDataSourceById(id);
        if (dataSource == null) {
            throw new IllegalArgumentException("数据源不存在");
        }

        dataSource.setFeatureCount(featureCount);
        dataSource.setLabelCount(labelCount);
        dataSource.setModelCount(modelCount);

        this.updateById(dataSource);
        return dataSource;
    }

    @Override
    public SourceInfo getSourceInfo(Long id) {
        DataSource dataSource = getDataSourceById(id);
        if (dataSource == null) {
            return null;
        }

        return SourceInfoConverter.fromJson(dataSource.getSourceInfo(), dataSource.getType());
    }

    @Override
    public void changeDataSourceStatus(Long id, Integer status) {
        DataSource dataSource = dataSourceMapper.selectById(id);
        dataSource.setCreateStatus(status);
        dataSourceMapper.updateById(dataSource);
    }

    @Override
    public void refreshDataSource(Long id) {
        DataSource dataSource = dataSourceMapper.selectById(id);
        try {
            commonService.refreshTableAndFieldData(dataSource);
        } catch (Exception e) {
            log.error("数据源刷新失败", e);
            throw new RuntimeException(e);
        }
    }

    private List<MpDataSourceResult.MpItem> getMpSource(Integer mpType) {
        String host = BeanFactoryHolder.getEnv().getProperty("label.zt.host");
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 构建请求体（假设我们传递的是一个包含 mpType 的整数列表）
        List<Integer> requestBody = Objects.isNull(mpType)
                ? Stream.of(MpDataSourceEnum.values()).map(MpDataSourceEnum::getMpType).collect(Collectors.toList())
                : List.of(mpType);

        // 创建请求实体
        HttpEntity<List<Integer>> requestEntity = new HttpEntity<>(requestBody, headers);

        // 发送 POST 请求并获取响应
        String url = String.format("%s%s", host, "/moye/out/service/dbInfo");
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 检查响应是否成功
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            String json = responseEntity.getBody();  // 获取响应内容
            List<MpDataSourceResult.MpItem> data = JSON.parseObject(json, MpDataSourceResult.class)
                    .getData();
            return data;
        } else {
            throw new RuntimeException("HTTP 请求失败，状态码：" + responseEntity.getStatusCode());
        }
    }

    private DataSourceVO convertToVO(MpDataSource mpDataSource) {
        DataSourceVO vo = new DataSourceVO();
        vo.setId(null);
        vo.setName(mpDataSource.getName());
        // 目前中台没有返回数据库名
        vo.setName(mpDataSource.getName());
        vo.setType(Optional.ofNullable(MpDataSourceEnum.getByMpType(mpDataSource.getDbType())).map(MpDataSourceEnum::getType).orElse(null));
        vo.setSourceInfo(buildByZg(mpDataSource));
        return vo;
    }

    private AbstractDbSourceInfo buildByZg(MpDataSource mp) {
        MpDataSourceEnum byMpType = MpDataSourceEnum.getByMpType(mp.getDbType());
        if (Objects.isNull(byMpType)) {
            return null;
        }
        String protocol = StringUtils.isEmpty(mp.getProtocol()) ? "http" : mp.getProtocol();
        switch (byMpType) {
            case MYSQL:
                MysqlSourceInfo mysql = new MysqlSourceInfo();
                mysql.setHost(mp.getHost());
                mysql.setPort(mp.getPort());
                mysql.setDbType("Mysql");
                if (null != mp.getPassword() && !"".equals(mp.getPassword())) {
                    mysql.setPassword(AesUtils.decrypt(mp.getPassword()));
                }
                mysql.setUserName(mp.getUserName());
                mysql.setDbName(mp.getDbName());
                mysql.setProtocol(protocol);
                return mysql;
            case ES:
                EsSourceInfo es = new EsSourceInfo();
                es.setDbType("ES");
                es.setHost(mp.getHost());
                es.setPort(mp.getPort());
                es.setUserName(mp.getUserName());
                if (null != mp.getPassword() && !"".equals(mp.getPassword())) {
                    es.setPassword(AesUtils.decrypt(mp.getPassword()));
                }
                es.setDbName(mp.getName());
                es.setProtocol(protocol);
                return es;
            case ORACLE:
                OracleSourceInfo oracle = new OracleSourceInfo();
                oracle.setHost(mp.getHost());
                oracle.setPort(mp.getPort());
                oracle.setDbType("Oracle");
                if (null != mp.getPassword() && !"".equals(mp.getPassword())) {
                    oracle.setPassword(AesUtils.decrypt(mp.getPassword()));
                }
                oracle.setUserName(mp.getUserName());
                oracle.setDbName(mp.getDbName());
                oracle.setProtocol(protocol);
                return oracle;
            case POSTGRESQL:
                PostgresqlSourceInfo pg = new PostgresqlSourceInfo();
                pg.setHost(mp.getHost());
                pg.setPort(mp.getPort());
                pg.setDbType("Postgresql");
                if (null != mp.getPassword() && !"".equals(mp.getPassword())) {
                    pg.setPassword(AesUtils.decrypt(mp.getPassword()));
                }
                pg.setUserName(mp.getUserName());
                pg.setDbName(mp.getDbName());
                pg.setProtocol(protocol);
            case CLICKHOUSE:
                ClickHouseSourceInfo ch = new ClickHouseSourceInfo();
                ch.setHost(mp.getHost());
                ch.setPort(mp.getPort());
                ch.setDbType("ClickHouse");
                if (null != mp.getPassword() && !"".equals(mp.getPassword())) {
                    ch.setPassword(AesUtils.decrypt(mp.getPassword()));
                }
                ch.setUserName(mp.getUserName());
                ch.setDbName(mp.getDbName());
                ch.setProtocol(protocol);
            case HIVE:
                HiveSourceInfo hs = new HiveSourceInfo();
                hs.setHost(mp.getHost());
                hs.setPort(mp.getPort());
                hs.setDbType("Hive");
                if (null != mp.getPassword() && !"".equals(mp.getPassword())) {
                    hs.setPassword(AesUtils.decrypt(mp.getPassword()));
                }
                hs.setUserName(mp.getUserName());
                hs.setDbName(mp.getDbName());
                hs.setProtocol(protocol);
            default:
                return null;
        }
    }

} 