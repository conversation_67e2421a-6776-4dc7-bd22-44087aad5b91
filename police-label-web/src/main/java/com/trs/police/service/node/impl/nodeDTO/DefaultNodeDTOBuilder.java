package com.trs.police.service.node.impl.nodeDTO;

import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 默认的节点DTO构建器
 */
@Component
public class DefaultNodeDTOBuilder extends LabelNodeDTOBuilder {
    @Override
    public boolean supports(Integer nodeTypeCode) {
        // 只处理未被其他Builder处理的类型
        return NodeType.ORDER.equals(nodeTypeCode)
                || NodeType.STATISTIC.equals(nodeTypeCode)
                || NodeType.DISTINCT.equals(nodeTypeCode)
                || NodeType.TRANSFORM.equals(nodeTypeCode)
                || NodeType.NEW_FIELD.equals(nodeTypeCode);
    }

    @Override
    public List<LabelNodeDTO> build(NodeDTO nodeDTO, NodeContext nodeContext) {
        return Collections.singletonList(new LabelNodeDTO(nodeDTO.getNodeMeta().getNodeTypeCode(), nodeDTO.getNodeProperties(), getOuputRow(nodeDTO), nodeDTO.getLastNodeId()));
    }
} 