package com.trs.police.service.feature.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.utils.ResultHelper;
import com.trs.police.common.core.vo.CountVO;
import com.trs.police.mapper.FeatureStatisticMapper;
import com.trs.police.service.feature.domain.entity.Feature;
import com.trs.police.service.feature.domain.repository.FeatureQueryRepository;
import com.trs.police.service.feature.domain.value.FeatureCategoryCount;
import com.trs.police.service.feature.domain.value.FeatureCategoryStatisticDTO;
import com.trs.police.service.feature.domain.value.FeatureSearch;
import com.trs.police.entity.feature.FeatureDO;
import com.trs.police.mapper.FeatureMapper;
import com.trs.police.service.feature.infrastructure.repository.converter.FeatureConverter;
import com.trs.police.service.shared.dict.DictTypeRegistry;
import com.trs.police.service.shared.table.TableReference;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询实现
 */
@Component
@Slf4j
public class FeatureQueryRepositoryMyBatisImpl implements FeatureQueryRepository {

    @Resource
    private FeatureMapper featureMapper;

    @Autowired
    private DictTypeRegistry dictTypeRegistry;

    @Autowired
    private FeatureConverter featureConverter;

    @Autowired
    private FeatureStatisticMapper featureStatisticMapper;

    @Override
    public RestfulResultsV2<Feature> findFeatures(FeatureSearch search) {
        try {
            IPage<FeatureDO> featurePage = featureMapper.findFeaturePage(Page.of(search.getPageNum(), search.getPageSize()),
                    search);
            IPage<Feature> convert = featurePage.convert(this::buildFeature);
            return ResultHelper.pageConvert2RestfulResults(convert);
        } catch (Exception e) {
            log.error("获取特征列表失败", e);
            return RestfulResultsV2.error("获取特征列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Feature> findByIds(List<Long> ids) {
        if (null == ids || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return featureMapper.selectBatchIds(ids)
                .stream()
                .map(this::buildFeature)
                .collect(Collectors.toList());
    }

    @Override
    public Feature selectById(Long featureId) {
        FeatureDO featureDO = featureMapper.selectById(featureId);
        return buildFeature(featureDO);
    }

    @Override
    public List<FeatureCategoryCount> categoryCount(FeatureCategoryStatisticDTO dto) {
        return featureStatisticMapper.categoryCount(dto);
    }

    @Override
    public List<CountVO> tableUseStatistic(List<TableReference> tbs) {
        if (null == tbs || tbs.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> tableIds = tbs.stream()
                .map(TableReference::getTableId)
                .collect(Collectors.toList());
        return featureStatisticMapper.tableUseStatistic(tableIds);
    }

    private Feature buildFeature(FeatureDO featureDO) {
        return featureConverter.toFeature(featureDO);
    }
}
