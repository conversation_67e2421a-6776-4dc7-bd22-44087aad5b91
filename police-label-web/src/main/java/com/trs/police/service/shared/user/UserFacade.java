package com.trs.police.service.shared.user;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.service.shared.user.ysimpl.Dept;
import com.trs.police.service.shared.user.ysimpl.mapper.YsDeptMapper;
import com.trs.police.service.shared.user.ysimpl.User;
import com.trs.police.service.shared.user.ysimpl.mapper.YsUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户外观服务
 *
 * <AUTHOR>
 */
@Component
public class UserFacade {

    @Autowired
    private YsUserMapper userMapper;

    @Autowired
    private YsDeptMapper deptMapper;


    /**
     * 查找当前用户
     *
     * @param userDeptReference 当前用户引用
     * @return 当前用户
     */
    public Optional<CurrentUserDTO> findCurrentUser(UserDeptReference userDeptReference) {
        List<CurrentUserDTO> currentUsers = findCurrentUsers(Arrays.asList(userDeptReference));
        return currentUsers.isEmpty() ? Optional.empty() : Optional.of(currentUsers.get(0));
    }

    /**
     * 批量查找当前用户
     *
     * @param userDeptReferences 当前用户引用
     * @return 当前用户
     */
    public List<CurrentUserDTO> findCurrentUsers(List<UserDeptReference> userDeptReferences) {
    	if (Objects.isNull(userDeptReferences) || userDeptReferences.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> uid = userDeptReferences.stream()
                .map(UserDeptReference::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<User> users = uid.isEmpty() ? new ArrayList<>() : userMapper.selectList(
                Wrappers.lambdaQuery(User.class)
                        .in(User::getId, uid)
        );
        List<Long> did = userDeptReferences.stream()
                .map(UserDeptReference::getDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<Dept> depts = did.isEmpty() ? new ArrayList<>() : deptMapper.selectList(
                Wrappers.lambdaQuery(Dept.class)
                        .in(Dept::getId, did)
        );
        List<CurrentUserDTO> result = userDeptReferences.stream()
                .map(ur -> {
                    CurrentUserDTO currentUserDTO = new CurrentUserDTO();
                    UserDTO userDTO = users.stream()
                            .filter(u -> u.getId().equals(ur.getUserId()))
                            .findAny()
                            .map(this::toUserInfoDTO)
                            .orElse(null);
                    currentUserDTO.setUser(userDTO);
                    DeptDTO deptDTO = depts.stream()
                            .filter(d -> d.getId().equals(ur.getDeptId()))
                            .findAny()
                            .map(this::toDeptDTO)
                            .orElse(null);
                    currentUserDTO.setDept(deptDTO);
                    if (Objects.isNull(userDTO) || Objects.isNull(deptDTO)) {
                        return null;
                    }
                    return currentUserDTO;
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        return result;
    }

    private UserDTO toUserInfoDTO(User user) {
        UserDTO userInfoDTO = new UserDTO();
        userInfoDTO.setUserId(user.getId());
        userInfoDTO.setUserRealName(user.getRealName());
    	return userInfoDTO;
    }

    private DeptDTO toDeptDTO(Dept dept) {
    	DeptDTO deptDTO = new DeptDTO();
    	deptDTO.setDeptId(dept.getId());
    	deptDTO.setDeptName(dept.getName());
        deptDTO.setDeptCode(dept.getCode());
        deptDTO.setDeptShorName(dept.getShortName());
    	return deptDTO;
    }
}
