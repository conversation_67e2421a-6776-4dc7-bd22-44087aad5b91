package com.trs.police.service.node.formula.impl;

import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.formula.Expression;
import com.trs.police.service.node.formula.FormulaContext;

import java.util.Map;
import java.util.Objects;

/**
 * 输入字段
 *
 * <AUTHOR>
 */
public class PropertyExpression implements Expression {

    private final FieldInfoVO fieldInfo;

    /**
     * 缓存hash值
     */
    private String cacheUuid;

    /**
     * 缓存字段索引
     */
    private Map<String, Integer> cache;

    public PropertyExpression(FieldInfoVO fieldInfo) {
        this.fieldInfo = fieldInfo;
    }

    @Override
    public FieldValue evaluate(FormulaContext context) {
        if (Objects.isNull(cacheUuid) || cacheUuid.equals(context.getNodeData().getNodeMeta().getUuid())) {
            cache = context.getNodeData().findColumnIndexById();
            cacheUuid = context.getNodeData().getNodeMeta().getUuid();
        }
        Integer i = cache.get(fieldInfo.getId());
        Objects.requireNonNull(i, String.format("字段%s不存在", fieldInfo.getId()));
        return context.getCurrentRow().getRowData().get(cache.get(fieldInfo.getId()));
    }
}
