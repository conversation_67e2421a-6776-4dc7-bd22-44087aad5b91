package com.trs.police.service.feature.application.DTO.query;

import com.trs.common.pojo.OrderDTO;
import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 特征数据传输对象 (FeatureSearchDTO)
 * <p>
 * 该类用于封装特征搜索的相关参数，作为数据传输对象在前后端之间传递。
 * 继承自 BaseDTO，并实现 Serializable 接口以支持序列化。
 * <p>
 * 作者: wenwen
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FeatureSearchDTO extends BaseDTO implements Serializable {

    /**
     * 数据表名称
     */
    private String tableName;

    /**
     * 特征名称
     */
    private String featureName;

    /**
     * 类别 ID
     */
    private String categoryCode;

    /**
     * 搜索的起始时间
     */
    private String startTime;

    /**
     * 搜索的结束时间
     */
    private String endTime;

    /**
     * 所属警种code，t_dict，type = 'feature_police_kind'
     */
    private Long policeKind;

    /**
     * 表格输入字段 ID 列表
     */
    private List<Long> tableInputFieldIds;

    /**
     * 表 ID
     */
    private Long tableId;

    private String featureSubject;

    /**
     * 特征主体编码
     */
    private Long mainObjectCode;

    /**
     * 搜索的关键字或值
     */
    private String searchValue;

    /**
     * 排序规则列表
     */
    private List<OrderDTO> orderList;

    private Long dataSourceTableId;
}