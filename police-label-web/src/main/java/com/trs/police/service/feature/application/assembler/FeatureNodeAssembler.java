package com.trs.police.service.feature.application.assembler;

import com.alibaba.fastjson.JSON;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.OrderDTO;
import com.trs.police.dto.node.RowMeta;
import com.trs.police.dto.node.properties.FeatureOutPutProperties;
import com.trs.police.service.feature.domain.value.FeatureBaseInfo;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessDTO;
import com.trs.police.service.feature.domain.value.Filed;
import com.trs.police.service.shared.dict.DictFacade;
import com.trs.police.service.shared.dict.DictReference;
import com.trs.police.service.shared.dict.DictTypeRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 特征节点装配器
 *
 * <AUTHOR>
 */
@Component
public class FeatureNodeAssembler {

    @Autowired
    private DictFacade dictFacade;

    @Autowired
    private DictTypeRegistry dictTypeRegistry;

    /**
     * 转换成字符串
     *
     * @param dto dto
     * @return 字符串
     */
    public String toString(FeatureProcessDTO dto) {
        // 查找uuid没有赋值的节点，给uuid赋值
        dto.getNodes().forEach(node -> node.getNodeMeta().initUid());
        // 特征输出节点和它输入节点的输出字段一致
        Optional<NodeDTO> any = dto.getNodes()
                .stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.FEATURE_OUT))
                .findAny();
        if (any.isPresent()) {
            Optional<String> fromId = dto.getNodeOrders()
                    .stream()
                    .filter(order -> order.getTo().equals(any.get().getNodeMeta().getUuid()))
                    .findAny()
                    .map(OrderDTO::getFrom);
            if (fromId.isPresent()) {
                String from = fromId.get();
                Optional<NodeDTO> fromNode = dto.getNodes()
                        .stream()
                        .filter(node -> node.getNodeMeta().getUuid().equals(from))
                        .findAny();
                if (fromNode.isPresent()) {
                    NodeDTO nodeDTO = fromNode.get();
                    any.get().getNodeMeta().setOutputRowMeta(JSON.parseObject(JSON.toJSONString(nodeDTO.getNodeMeta().getOutputRowMeta()), RowMeta.class));
                }
            }
        }
        // 转换成字符串
        return JSON.toJSONString(dto);
    }

    /**
     * 转换成dto
     *
     * @param json json
     * @return dto
     */
    public FeatureProcessDTO toDTO(String json) {
        return JSON.parseObject(json, FeatureProcessDTO.class);
    }

    /**
     * 装换成特征的基础信息
     *
     * @param dto 流程信息
     * @return 特征基础信息
     */
    public FeatureBaseInfo toFeatureBaseInfo(FeatureProcessDTO dto) {
        Optional<NodeDTO> any = dto.getNodes()
                .stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.FEATURE_OUT))
                .findAny();
        if (any.isEmpty()) {
            return null;
        }
        FeatureBaseInfo baseInfo = new FeatureBaseInfo();
        FeatureOutPutProperties outPutProperties = JSON.parseObject(any.get().getNodeProperties(), FeatureOutPutProperties.class);
        baseInfo.setFeatureName(outPutProperties.getBaseInfo().getFeatureName());
        baseInfo.setDescription(outPutProperties.getBaseInfo().getDescription());
        baseInfo.setPoliceKind(new DictReference(outPutProperties.getBaseInfo().getPoliceKind(), dictTypeRegistry.getPoliceKind()));
        baseInfo.setCategoryCode(new DictReference(outPutProperties.getBaseInfo().getCategoryCode(), dictTypeRegistry.getFeatureCategory()));
        // 根据输出节点特征主体字段修改特征主体信息
        if (null != outPutProperties.getMainObject() && !outPutProperties.getMainObject().isEmpty()) {
            baseInfo.setMainObject(new DictReference(outPutProperties.getMainObject().get(0).getMainObjectTypeCode(), dictTypeRegistry.getFeatureMainObject()));
            any.get()
                    .getNodeMeta()
                    .getOutputRowMeta()
                    .getValueMetaList()
                    .stream()
                    .filter(v -> v.getEnName().equals(outPutProperties.getMainObject().get(0).getEnName()))
                    .findAny()
                    .ifPresent(v -> {
                        Filed filed = Filed.builder()
                                .enName(v.getEnName())
                                .cnName(v.getCnName())
                                .fromNode(v.getFromNode())
                                .typeCode(v.getTypeCode())
                                .build();
                        baseInfo.setMainObjectField(filed);
                    });
        }
        return baseInfo;
    }

    /**
     * 更新id和名称
     *
     * @param dto 流程
     * @param featureId 特征id
     * @param featureName 特征名称
     */
    public void updateIdAndName(FeatureProcessDTO dto, Long featureId, String featureName) {
        dto.getNodes().stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.FEATURE_OUT))
                .findAny()
                .ifPresent(node -> {
                    String nodeProperties = node.getNodeProperties();
                    FeatureOutPutProperties outPutProperties = JSON.parseObject(nodeProperties, FeatureOutPutProperties.class);
                    outPutProperties.getBaseInfo().setFeatureName(featureName);
                    node.setNodeProperties(JSON.toJSONString(outPutProperties));
                    node.getNodeMeta().setName(featureName);
                });
        dto.getInfo().setFeatureId(featureId);
        dto.getInfo().setName(featureName);
    }
}
