package com.trs.police.service.shared.user;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;
import java.util.Optional;

/**
 * 当前用户（具体到部门）
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CurrentUserDTO {

    private UserDTO user;

    private DeptDTO dept;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        CurrentUserDTO that = (CurrentUserDTO) o;
        Long uid = Optional.of(user).map(UserDTO::getUserId).orElse(null);
        Long did = Optional.of(dept).map(DeptDTO::getDeptId).orElse(null);
        Long uid2 = Optional.of(that.user).map(UserDTO::getUserId).orElse(null);
        Long did2 = Optional.of(that.dept).map(DeptDTO::getDeptId).orElse(null);
        return Objects.equals(uid, uid2) && Objects.equals(did, did2);
    }

    public CurrentUserDTO(Long user, Long dept) {
        this.user = new UserDTO();
        this.dept = new DeptDTO();
        this.user.setUserId(user);
        this.dept.setDeptId(dept);
    }
}
