package com.trs.police.service.feature.application.service.impl;

import com.trs.police.common.core.vo.CountVO;
import com.trs.police.service.feature.application.DTO.query.FeatureCategoryStatisticDTO;
import com.trs.police.service.feature.application.DTO.vo.FeatureCategoryStatisticVO;
import com.trs.police.service.feature.application.assembler.FeatureSearchAssembler;
import com.trs.police.service.feature.application.service.FeatureStatisticService;
import com.trs.police.service.feature.domain.repository.FeatureQueryRepository;
import com.trs.police.service.feature.domain.value.FeatureCategoryCount;
import com.trs.police.service.shared.table.TableReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 特征统计服务
 *
 * <AUTHOR>
 */
@Service
public class FeatureStatisticServiceImpl implements FeatureStatisticService {

    @Autowired
    private FeatureQueryRepository featureQueryRepository;

    @Autowired
    private FeatureSearchAssembler featureSearchAssembler;

    @Override
    public List<FeatureCategoryStatisticVO> featureCategoryStatistic(FeatureCategoryStatisticDTO dto) {
        List<FeatureCategoryCount> counts = featureQueryRepository.categoryCount(featureSearchAssembler.toCategoryStatisticDTO(dto));
        return featureSearchAssembler.toCategoryStatisticVO(counts);
    }

    @Override
    public List<CountVO> tableUseStatistic(List<TableReference> tbs) {
        return featureQueryRepository.tableUseStatistic(tbs);
    }
}
