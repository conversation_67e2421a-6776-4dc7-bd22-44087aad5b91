package com.trs.police.service.node.formula;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.node.Operator;
import com.trs.police.service.node.formula.impl.op.Divide;
import com.trs.police.service.node.formula.impl.op.Multiply;
import com.trs.police.service.node.formula.impl.op.Plus;
import com.trs.police.service.node.formula.impl.op.Subtract;

/**
 * 操作符号工厂
 *
 * <AUTHOR>
 */
public class OperationFactory {

    /**
     * 计算
     *
     * @param left   左侧
     * @param operator 操作符号
     * @param right   右侧
     * @return 结果
     */
    public static FieldValue calculate(FieldValue left, Operator operator, FieldValue right) {
        switch (operator) {
            case ADD:
                return Plus.INSTANCE.calculate(left, right);
            case SUBTRACT:
                return Subtract.INSTANCE.calculate(left, right);
            case MULTIPLY:
                return Multiply.INSTANCE.calculate(left, right);
            case DIVIDE:
                return Divide.INSTANCE.calculate(left, right);
            default:
                throw new IllegalArgumentException("Unsupported operator: " + operator);

        }
    }
}
