package com.trs.police.service.node.impl;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.*;
import com.trs.police.dto.node.properties.bean.GroupField;
import com.trs.police.dto.node.properties.bean.OrderItem;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.GroupHelper;
import com.trs.police.service.node.Node;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 去重节点
 *
 * <AUTHOR>
 */
public class DistinctNode extends Node {

    public DistinctNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        DistinctNodeProperties property = getPropertyAs(DistinctNodeProperties.class);

        // 执行分组
        List<GroupField> groupFieldList = property.getDistinctField()
                .stream()
                .map(field -> {
                    return new GroupField(field.getFiled(), field.getDistinctType());
                })
                .collect(Collectors.toList());
        NodeData input = inputNode.get(0);
        Map<String, List<List<FieldValue>>> group = input.getData()
                .stream()
                .collect(Collectors.groupingBy(GroupHelper.getGroupKey(groupFieldList)));

        // 组内排序
        if (CollectionUtils.isNotEmpty(property.getDistinctRule())) {
            List<OrderItem> ods = property.getDistinctRule()
                    .stream()
                    .map(rule -> {
                        OrderItem orderItem = new OrderItem();
                        orderItem.setFromNode(rule.getFiled().getFromNode());
                        orderItem.setEnName(rule.getFiled().getEnName());
                        orderItem.setOrder(rule.getOrder());
                        return orderItem;
                    })
                    .collect(Collectors.toList());
            OrderNodeProperties orderProperties = new OrderNodeProperties();
            orderProperties.setOrderItems(ods);
            Comparator<List<FieldValue>> comparable = OrderNode.comparable(orderProperties);
            for (List<List<FieldValue>> value : group.values()) {
                value.sort(comparable);
            }
        }

        // 每组保留一条记录
        NodeData nodeData = new NodeData();
        nodeData.setNodeMeta(nodeMeta);
        nodeData.setHeader(input.getHeader());
        nodeData.setData(group.values()
                .stream()
                .map(list -> {
                    return list.get(0);
                })
                .collect(Collectors.toList()));
        long total = nodeData.getData().size();
        nodeData.setTotalCount(total);
        return nodeData;
    }

    @Override
    public Integer nodeType() {
        return NodeType.DISTINCT;
    }
}
