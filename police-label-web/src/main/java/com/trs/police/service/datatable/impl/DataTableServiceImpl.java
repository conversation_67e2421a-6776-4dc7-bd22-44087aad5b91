package com.trs.police.service.datatable.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.Operator;
import com.trs.db.sdk.exception.TrsCrudException;
import com.trs.db.sdk.pojo.RecordInfo;
import com.trs.db.sdk.repository.Repository;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.VoParameterConstructor;
import com.trs.police.common.core.vo.CountVO;
import com.trs.police.config.DbSdkConfig;
import com.trs.police.dto.DataTableDTO;
import com.trs.police.dto.DataTableOverviewDto;
import com.trs.police.dto.TableSelectionDTO;
import com.trs.police.entity.datasource.AbstractDbSourceInfo;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.DataSourceType;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.police.entity.datatable.DataTable;
import com.trs.police.mapper.DataFieldMapper;
import com.trs.police.mapper.DataSourceMapper;
import com.trs.police.mapper.DataTableMapper;
import com.trs.police.service.baseService.CommonService;
import com.trs.police.service.datatable.DataTableService;
import com.trs.police.service.shared.feature.FeatureFacade;
import com.trs.police.service.shared.table.TableReference;
import com.trs.police.utils.ExpressionUtils;
import com.trs.police.utils.RepositoryUtil;
import com.trs.police.utils.SourceInfoConverter;
import com.trs.police.vo.DataTableVO;
import com.trs.police.vo.data.TableDataSourceVO;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.entity.PageInfo;
import com.trs.web.entity.PageList;
import io.vavr.control.Either;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * 数据表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataTableServiceImpl implements DataTableService {


    @Autowired
    private DataTableMapper dataTableMapper;

    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Autowired
    private DataFieldMapper dataFieldMapper;

    @Autowired
    private DbSdkConfig dbSdkConfig;

    @Autowired
    private FeatureFacade featureFacade;

    @Autowired
    private CommonService commonService;

    @Override
    public RestfulResultsV2<DataTableVO> getAllDataTable(TableSelectionDTO dto) {
        Page<DataTableVO> resultPage = null;
        try {
            // 创建查询条件
            QueryWrapper<DataTable> queryWrapper = new QueryWrapper<DataTable>()
                    .eq(Objects.nonNull(dto.getDataSourceId()), "data_source_id", dto.getDataSourceId())
                    .eq("status", 0)
                    .eq(Objects.nonNull(dto.getSelectedStatus()), "selected_status", dto.getSelectedStatus());
            // 如果有搜索条件，添加模糊查询
            if (StringUtils.isNotEmpty(dto.getSearchValue())) {
                queryWrapper.and(wrapper -> wrapper
                        .like("table_name", dto.getSearchValue())
                        .or()
                        .like("table_name_cn", dto.getSearchValue()));
            }
            Page<DataTable> page = new Page<>(dto.getPageNum(), dto.getPageSize());
            Page<DataTable> dataTablePage = dataTableMapper.selectPage(page, queryWrapper);
            // 将DataTable分页结果转换为DataTableVO分页结果
            resultPage = new Page<>(dataTablePage.getCurrent(), dataTablePage.getSize(), dataTablePage.getTotal());
            // 将DataTable转换为DataTableVO
            List<DataTableVO> records = new ArrayList<>();
            records = dataTablePage.getRecords().stream()
                    .map(dataTable -> {
                        DataTableVO dataTableVO = new DataTableVO();
                        dataTableVO.setId(dataTable.getId());
                        dataTableVO.setAliaseName(dataTable.getAliaseName());
                        dataTableVO.setTableNameEn(dataTable.getTableName());
                        dataTableVO.setTableNameCn(dataTable.getTableNameCn());
                        dataTableVO.setSelectedStatus(dataTable.getStatus());
                        dataTableVO.setSelectedStatus(dataTable.getSelectedStatus());
                        dataTableVO.setRelationLabelCount(dataTable.getFeatureLabelStatus() != null
                                ? dataTable.getFeatureLabelStatus().longValue()
                                : 0L);
                        dataTableVO.setDataSource(new TableDataSourceVO(dataTable.getDataSourceId()));
                        dataTableVO.setIdField(dataTable.getIdField());
                        return dataTableVO;
                    })
                    .collect(Collectors.toList());
            // 反差数据源
            List<Long> sourceId = records.stream()
                    .map(DataTableVO::getDataSource)
                    .map(TableDataSourceVO::getId)
                    .distinct()
                    .collect(Collectors.toList());
            final List<DataSource> dataSources = dataSourceMapper.selectBatchIds(sourceId);
            // 构造vo
            VoParameterConstructor.of(records)
                    .singleValueMatcher(this::getTableUseCountByTable, (v, c) -> Objects.equals(v.getId(), Long.valueOf(c.getId())))
                    .consumer((v, c) -> {
                        v.setRelationLabelCount(c.getCount().longValue());
                        v.setFeatureCount(c.getCount());
                    })
                    .singleValueMatcher(v -> dataSources, (v, d) -> Objects.equals(v.getDataSource().getId(), d.getId()))
                    .consumer((v, d) -> v.getDataSource().setName(d.getName()))
                    .build();
            // 设置分页结果
            resultPage.setRecords(records);
        } catch (Exception e) {
            log.error("获取所有数据表信息失败", e);
        }
        return RestfulResultsV2.ok(resultPage.getRecords())
                .addTotalCount(resultPage.getTotal())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize());
    }

    @Override
    public void updateDataSource(Long id, DataTableDTO dto) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        DataTable dataTable = dataTableMapper.selectById(id);
        if (Objects.nonNull(currentUser)){
            dataTable.setUpdateUserId(currentUser.getId());
            dataTable.setUpdateDeptId(currentUser.getDeptId());
            dataTable.fillAuditFields();
        }
        if (Objects.nonNull(dto.getTableNameCn())){
            dataTable.setTableNameCn(dto.getTableNameCn());
        }
        if (Objects.nonNull(dto.getAliaseName())){
            dataTable.setAliaseName(dto.getAliaseName());
        }
        dataTableMapper.updateById(dataTable);
    }

    @Override
    public void selectTables(TableSelectionDTO dto) {
        if (StringUtils.isNotEmpty(dto.getSelectedIds())){
            List<Long> selectedIdsList = Arrays.stream(dto.getSelectedIds().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            // 更新被选中的数据表状态为1
            if (!selectedIdsList.isEmpty()) {
                dataTableMapper.update(new DataTable(), new UpdateWrapper<DataTable>()
                        .in("id", selectedIdsList)
                        .set("selected_status", 1));
            }
        }
        if (StringUtils.isNotEmpty(dto.getUnselectedIds())){
            List<Long> unselectedIdsList = Arrays.stream(dto.getUnselectedIds().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            // 更新未被选中的数据表状态为0
            if (!unselectedIdsList.isEmpty()) {
                dataTableMapper.update(new DataTable(), new UpdateWrapper<DataTable>()
                        .in("id", unselectedIdsList)
                        .set("selected_status", 0));
            }
        }

    }

    @Override
    public void delete(Long id) {
        DataTable dataTable = dataTableMapper.selectById(id);
        if (dataTable == null) {
            throw new IllegalArgumentException("数据表不存在");
        }
        // 检查是否可以删除
        if (!dataTable.canBeDeleted()) {
            throw new IllegalArgumentException("数据源已关联特征/标签/模型，无法删除");
        }
        dataTable.setStatus(1);
        dataTableMapper.updateById(dataTable);
    }

    @Override
    public RestfulResultsV2<DataTableVO> datatableDetail(Integer id) {
        if (Objects.isNull(id)) {
            return RestfulResultsV2.ok(new DataTableVO());
        }
        DataTable dataTable = dataTableMapper.selectById(id);
        if (Objects.isNull(dataTable)) {
            return RestfulResultsV2.error("数据表不存在");
        }
        DataTableVO dataTableVO = new DataTableVO();
        dataTableVO.setId(dataTable.getId());
        dataTableVO.setTableNameEn(dataTable.getTableName());
        dataTableVO.setTableNameCn(dataTable.getTableNameCn());
        dataTableVO.setAliaseName(dataTable.getAliaseName());

        // 特征使用数量
        List<CountVO> countVs = featureFacade.tableUseStatistic(Arrays.asList(new TableReference(dataTableVO.getId())));
        countVs.stream()
                .filter(countVO -> Objects.equals(countVO.getId(), dataTableVO.getId().toString()))
                .findAny()
                .ifPresent(countVO -> dataTableVO.setFeatureCount(countVO.getCount()));
        return RestfulResultsV2.ok(dataTableVO);
    }

    @Override
    public RestfulResultsV2<JSONObject> getDataOverview(DataTableOverviewDto dto) {
        PreConditionCheck.checkNotNull(dto.getTableId(), "数据表ID不能为空");
        DataTable dataTable = dataTableMapper.selectById(dto.getTableId());
        PreConditionCheck.checkNotNull(dataTable.getDataSourceId(), "数据源id为空");
        DataSource dataSource = dataSourceMapper.selectById(dataTable.getDataSourceId());
        DataSourceType type = dataSource.getType();
        SourceInfo sourceInfo = SourceInfoConverter.fromJson(dataSource.getSourceInfo(), type);
        Repository<RecordInfo> repository = RepositoryUtil.getRepositoryByDataBaseInfo((AbstractDbSourceInfo) sourceInfo);
        PageInfo pageInfo = ExpressionUtils.generatePageInfo(dto);
        Expression expression = ExpressionUtils.generateExpression(dto);
        Either<TrsCrudException, PageList<RecordInfo>> pageResult = repository.findPageList(dataTable.getTableName(), expression, pageInfo);
        if (pageResult.isRight()){
            PageList<RecordInfo> recordInfoPageList = pageResult.get();
            List<JSONObject> list = dealContentsToJson(recordInfoPageList.getContents())
                    .stream()
                    .map(d -> JSONObject.parseObject(JSON.toJSONString(d)))
                    .collect(Collectors.toList());
            return RestfulResultsV2.ok(list).addTotalCount(recordInfoPageList.getTotal())
                    .addPageNum(pageInfo.getPageNum()).addPageSize(pageInfo.getPageSize());
        }
        return RestfulResultsV2.ok(new ArrayList<>()).addTotalCount(0L).addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize());
    }

    @Override
    public RestfulResultsV2<JSONObject> getData(DataTableOverviewDto dto, Expression expression) {
        PreConditionCheck.checkNotNull(dto.getTableId(), "数据表ID不能为空");
        DataTable dataTable = dataTableMapper.selectById(dto.getTableId());
        PreConditionCheck.checkNotNull(dataTable.getDataSourceId(), "数据源id为空");
        DataSource dataSource = dataSourceMapper.selectById(dataTable.getDataSourceId());
        DataSourceType type = dataSource.getType();
        SourceInfo sourceInfo = SourceInfoConverter.fromJson(dataSource.getSourceInfo(), type);
        Repository<RecordInfo> repository = RepositoryUtil.getRepositoryByDataBaseInfo((AbstractDbSourceInfo) sourceInfo);
        PageInfo pageInfo = ExpressionUtils.generatePageInfo(dto);
        Either<TrsCrudException, PageList<RecordInfo>> pageResult = repository.findPageList(dataTable.getTableName(), expression, pageInfo);
        if (pageResult.isRight()){
            PageList<RecordInfo> recordInfoPageList = pageResult.get();
            List<JSONObject> list = dealContentsToJson(recordInfoPageList.getContents())
                    .stream()
                    .map(d -> JSONObject.parseObject(JSON.toJSONString(d)))
                    .collect(Collectors.toList());
            return RestfulResultsV2.ok(list).addTotalCount(recordInfoPageList.getTotal())
                    .addPageNum(pageInfo.getPageNum()).addPageSize(pageInfo.getPageSize());
        }
        return RestfulResultsV2.ok(new ArrayList<>()).addTotalCount(0L).addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize());
    }

    @Override
    public void refreshTableById(Long tableId) {
        try {
            commonService.refreshTableById(tableId);
        } catch (Exception e) {
            log.error("数据源刷新失败", e);
            throw new RuntimeException("刷新数据表失败");
        }
    }

    @Override
    public Map<String, JSONObject> getDataOverviewByPrimaryKey(Long tableId, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        PreConditionCheck.checkNotNull(tableId, "数据表ID不能为空");
        DataTable dataTable = dataTableMapper.selectById(tableId);
        String idField = dataTable.getIdField();
        if (StringUtils.isEmpty(idField)) {
            return new HashMap<>();
        }
        PreConditionCheck.checkNotNull(dataTable.getDataSourceId(), "数据源id为空");
        DataSource dataSource = dataSourceMapper.selectById(dataTable.getDataSourceId());
        List<List<String>> partition = Lists.partition(ids, 200);
        Map<String, JSONObject> result = new HashMap<>(ids.size());
        for (List<String> idPart : partition) {
            Expression expression = Condition(idField, Operator.In, idPart);
            DataSourceType type = dataSource.getType();
            SourceInfo sourceInfo = SourceInfoConverter.fromJson(dataSource.getSourceInfo(), type);
            Repository<RecordInfo> repository = RepositoryUtil.getRepositoryByDataBaseInfo((AbstractDbSourceInfo) sourceInfo);
            Either<TrsCrudException, List<RecordInfo>> pageResult = repository.findList(dataTable.getTableName(), expression);

            if (pageResult.isRight()){
                List<RecordInfo> recordInfoPageList = pageResult.get();
                List<JSONObject> list = dealContentsToJson(recordInfoPageList)
                        .stream()
                        .map(d -> JSONObject.parseObject(JSON.toJSONString(d)))
                        .collect(Collectors.toList());
                for (JSONObject object : list) {
                    result.put(object.getString(idField), object);
                }
            }
        }
        return result;
    }

    private List<Map<String, Object>> dealContentsToJson(List<RecordInfo> contents) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (RecordInfo content : contents) {
            Map<String, Object> fieldValueMap = content.getFieldValueMap();
            list.add(fieldValueMap);
        }
        return list;
    }

    private List<CountVO> getTableUseCountByTable(List<DataTableVO> records) {
        List<TableReference> tbs = records.stream()
                .map(DataTableVO::getId)
                .map(TableReference::new)
                .collect(Collectors.toList());
        return featureFacade.tableUseStatistic(tbs);
    }

}
