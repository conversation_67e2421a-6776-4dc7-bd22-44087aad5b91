package com.trs.police.service.node.formula.function;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.NodeFunction;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static com.trs.police.constant.node.NodeConstants.NUMBER;

/**
 * 提取时间的某个部分
 *
 * <AUTHOR>
 */
public class GetDatePartFunction implements NodeFunction {

    public static final GetDatePartFunction INSTANCE = new GetDatePartFunction();

    @Override
    public FieldValue execute(List<FieldValue> parameters) {
        String time = parameters.get(0).getValue();
        if (StringUtils.isEmpty(time)) {
            return new FieldValue("", NUMBER);
        }
        Date date = TimeUtils.stringToDate(time);
        // data转localDateTime
        LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());

        // 参数2获取到提取时间类型 0 日 1 月 2 年 3 分 4 秒 5 毫秒
        switch (parameters.get(1).getValue()) {
            case "日":
                return new FieldValue(localDateTime.getDayOfMonth() + "", NUMBER);
            case "月":
                return new FieldValue(localDateTime.getMonthValue() + "", NUMBER);
            case "年":
                return new FieldValue(localDateTime.getYear() + "", NUMBER);
            case "分":
                return new FieldValue(localDateTime.getMinute() + "", NUMBER);
            case "秒":
                return new FieldValue(localDateTime.getSecond() + "", NUMBER);
            case "毫秒":
                return new FieldValue(localDateTime.getNano() / 1000000 + "", NUMBER);
            default:
                return new FieldValue("", NUMBER);
        }
    }

    @Override
    public String key() {
        return "get_date_part";
    }
}
