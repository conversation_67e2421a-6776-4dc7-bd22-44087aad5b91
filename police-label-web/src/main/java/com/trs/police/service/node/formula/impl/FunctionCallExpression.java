package com.trs.police.service.node.formula.impl;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.formula.Expression;
import com.trs.police.service.node.formula.FormulaContext;
import com.trs.police.service.node.formula.FunctionFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 函数调用表达式
 *
 * <AUTHOR>
 */
public class FunctionCallExpression implements Expression {
    private final String functionName;
    private final List<Expression> parameters;

    public FunctionCallExpression(String functionName, List<Expression> parameters) {
        this.functionName = functionName;
        this.parameters = parameters;
    }

    @Override
    public FieldValue evaluate(FormulaContext context) {
        // 这里应该通过工厂获取实际的函数实现
        List<FieldValue> evaluatedParams = parameters.stream()
                .map(expr -> expr.evaluate(context))
                .collect(Collectors.toList());

        return FunctionFactory.execute(functionName, evaluatedParams);
    }
}
