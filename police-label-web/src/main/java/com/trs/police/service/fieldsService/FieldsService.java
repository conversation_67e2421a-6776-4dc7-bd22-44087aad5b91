package com.trs.police.service.fieldsService;

import com.trs.police.vo.DataTableFieldVO;

import java.util.List;

/**
 * 字段service
 */
public interface FieldsService {

    /**
     * 数据表字段查询
     *
     * @param tableId 数据表id
     * @param selectedStatus 选中状态 0:未选中 1:选中
     * @return 结果
     */
    List<DataTableFieldVO> getFieldInfo(Integer tableId, Integer selectedStatus);

    /**
     * 数据表字段查询
     *
     * @param tableId 数据表id
     * @return 结果
     */
    List<DataTableFieldVO> getFieldInfo(Integer tableId);
}
