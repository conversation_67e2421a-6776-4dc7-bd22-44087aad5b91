package com.trs.police.service.node.impl.nodeDTO;

import com.trs.common.utils.expression.Expression;
import com.trs.db.sdk.builder.sql.BuildResult;
import com.trs.db.sdk.builder.sql.SqlBuilderFactory;
import com.trs.db.sdk.builder.sql.SqlFetchBuilder;
import com.trs.db.sdk.core.dbtype.DBTypeEnum;
import com.trs.police.common.core.vo.node.ValueWrapper;
import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.node.ControlDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.properties.bean.ControlValue;
import com.trs.police.vo.RowFieldVo;
import com.trs.web.entity.PageInfo;

import java.util.*;
import java.util.stream.Collectors;

/**
 * LabelNodeDTO构建抽象
 */
public abstract class LabelNodeDTOBuilder {
    /**
     * 判断当前节点是否支持
     *
     * @param nodeTypeCode 节点类型
     * @return 是
     */
    abstract boolean supports(Integer nodeTypeCode);

    /**
     * 构建节点
     *
     * @param nodeDTO 节点
     * @param nodeContext 上下文
     * @return 节点
     */
    abstract List<LabelNodeDTO> build(NodeDTO nodeDTO, NodeContext nodeContext);

    /**
     * 获取where条件
     *
     * @param expression 表达式
     * @return sql
     */
    public String where(Expression expression){
        if(expression == null){
            return "";
        }
        DBTypeEnum dbType = DBTypeEnum.valueOfString("Mysql");
        SqlFetchBuilder sqlFetchBuilder = SqlBuilderFactory.select("tableName", dbType);
        BuildResult build = sqlFetchBuilder
                .onColumns("*")
                .where(expression)
                .page(PageInfo.newPage(1,1))
                .build();
        String realWhereSql = build.getRealWhereSql();
        return realWhereSql == null ? null : realWhereSql
                .replaceAll("LIKE '%'", "LIKE '%")
                .replaceAll("'%'\\)", "%'\\)");
    }

    /**
     * 添加控制参数的值
     *
     * @param controls 控制参数
     * @param controlValues 控制参数的值
     * @param nodeContext 上下文
     */
    public void addControlValue(List<ControlDTO> controls, List<ControlValue> controlValues, NodeContext nodeContext){
        // 上下文添加控制参数的值
        for (ControlValue controlValue : Optional.ofNullable(controlValues).orElse(new ArrayList<>())) {
            Optional<ControlDTO> first = controls.stream()
                    .filter(c -> c.getName().equals(controlValue.getControlName()))
                    .findAny();
            if (first.isEmpty()) {
                throw new RuntimeException("特征输入节点配置错误，没有找到控制参数：" + controlValue.getControlName());
            }
            nodeContext.getControlValueMap().put(controlValue.getControlName(), controlValue(first.get().getType(), controlValue));
        }
    }

    private ValueWrapper controlValue(String typeCode, ControlValue controlValue) {
        String[] array = controlValue.getValue().toArray(String[]::new);
        return new ValueWrapper(typeCode, array);
    }

    /**
     * 获取输出字段
     *
     * @param nodeDTO 节点
     * @return 输出字段
     */
    public List<RowFieldVo> getOuputRow(NodeDTO nodeDTO) {
        return nodeDTO.getNodeMeta().getOutputRowMeta().getValueMetaList().stream().map(valueMateBase -> RowFieldVo.of(valueMateBase.getCol(), valueMateBase.getCol())).collect(Collectors.toList());
    }
} 