package com.trs.police.service.feature.application.DTO.vo;

import com.trs.police.service.shared.dict.DictItemDTO;
import com.trs.police.service.shared.user.CurrentUserDTO;
import lombok.Data;

/**
 * 特征视图对象
 *
 * <AUTHOR>
 */
@Data
public class FeatureVO {

    /**
     * 特征ID
     */
    private Long featureId;

    /**
     * 特征名称
     */
    private String featureName;

    /**
     * 特征描述
     */
    private String description;

    /**
     * 所属分类ID
     */
    private Long categoryCode;

    private DictItemDTO categoryDict;

    /**
     * 所属警种code，t_dict，type = 'feature_police_kind'
     */
    private Long policeKind;

    private DictItemDTO policeKindDict;

    /**
     * 所属警种名称
     */
    private String policeKindName;

    /**
     * 业务规则描述
     */
    private String businessRule;

    /**
     * 选用的表格ID
     */
    private Long tableId;

    /**
     * 表格信息
     */
    private FeatureTableItemDTO tableInfo;

    /**
     * 输出字段（JSON结构，包括特征主体、对象类型等）
     */
    private String outputFields;

    /**
     * 颜色
     */
    private String color;

    /**
     * 状态 0=停用 1=启用
     */
    private Integer status;

    /**
     * 关联标签数量
     */
    private Integer labelCount;

    /**
     * 使用次数
     */
    private Integer useCount = 0;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;


    private Long createDeptId;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    /**
     * 是否可以停用
     */
    private Boolean canDisable;

    /**
     * 特征主体编码
     */
    private Long mainObjectCode;

    /**
     * 特征主体
     */
    private DictItemDTO mainObjectDict;

    /**
     * 特征主体字段
     */
    private FiledVO mainObjectField;

    /**
     * 用户信息
     */
    private CurrentUserDTO createUser;

    /**
     * 创建用户名称
     */
    @Deprecated
    private String createUserName;
}
