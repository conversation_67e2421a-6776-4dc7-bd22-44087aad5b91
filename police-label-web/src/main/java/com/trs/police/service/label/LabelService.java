package com.trs.police.service.label;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.vo.CountVO;
import com.trs.police.dto.label.*;
import com.trs.police.dto.label.node.LabelProcessDTO;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.shared.dict.DictItemDTO;
import com.trs.police.vo.label.LabelCategoryStatisticVO;
import com.trs.police.vo.label.LabelListVO;
import com.trs.police.vo.label.application.LabelResultVO;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.police.vo.label.application.LabelVO;

import java.util.List;

/**
 * 标签服务
 *
 * <AUTHOR>
 */
public interface LabelService {

    /**
     * 添加标签
     *
     * @param add 标签
     * @return 新增的标签
     */
    LabelVO addLabel(LabelAddDTO add);

    /**
     * 标签详情
     *
     * @param labelId 标签id
     * @return 标签详情
     */
    LabelVO detail(Long labelId);

    /**
     * 标签数量统计
     *
     * @param dto dto
     * @return 标签数量统计
     */
    List<LabelCategoryStatisticVO> labelCategoryStatistic(LabelCategoryStatisticDTO dto);

    /**
     * 添加码表
     *
     * @param add 码表
     * @return 新增的码表
     */
    DictItemDTO addCategory(LabelDictAddDTO add);

    /**
     * 删除码表
     *
     * @param code id
     */
    void deleteCategory(Long code);


    /**
     * 保存标签
     *
     * @param processDTO 流程dto
     */
    void saveNode(LabelProcessDTO processDTO);

    /**
     * 启用/停用标签
     *
     * @param labelId 标签ID
     * @param status 状态 0=停用 1=启用
     * @return 操作结果
     */
    String toggleLabelStatus(Long labelId, Integer status);

    /**
     * 删除标签
     *
     * @param labelId 标签ID
     * @return 操作结果
     */
    String deleteLabel(Long labelId);

    /**
     * 列表页面
     *
     * @param dto dto
     * @return 结果
     */
    RestfulResultsV2<LabelListVO> page(LabelListDto dto);

    /**
     * 复制标签
     *
     * @param labelId 待复制标签ID
     * @return 新标签ID
     */
    Long copy(Long labelId);

    /**
     * 特征使用统计
     *
     * @param featureIds 特征
     * @return 特征使用数量统计
     */
    List<CountVO> featureUseStatistic(List<Long> featureIds);

    /**
     * 预览标签
     *
     * @param labelId 标签id
     * @return 预览结果
     */
    NodeData previewNode(Long labelId);

    /**
     * 查询标签结果
     *
     * @param dto 查询参数
     * @param searchParams 搜索参数
     * @param pageParams 分页参数
     * @return 标签结果
     */
    RestfulResultsV2<LabelResultVO> queryLabelResult(LabelResultQueryDTO dto, PageParams pageParams, SearchParams searchParams);
}
