package com.trs.police.service.node;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.EmtpyExpression;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.Operator;
import com.trs.common.utils.expression.attribute.IAttributesWrapper;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.common.utils.expression.parser.AbsConditionJsonParser;
import com.trs.common.utils.expression.parser.ConditionStructure;
import com.trs.police.common.core.constant.ValueType;
import com.trs.police.common.core.vo.node.ValueWrapper;
import com.trs.police.dto.node.NodeContext;
import io.vavr.control.Either;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

import static com.trs.common.base.PreConditionCheck.checkNotNull;
import static com.trs.common.utils.expression.ExpressionBuilder.Condition;
import static com.trs.police.common.core.constant.ValueType.GEO_CONTAINS;

/**
 * 过滤节点表达式生成器
 *
 * <AUTHOR>
 */
public class FilterNodeConditionParser extends AbsConditionJsonParser<NodeContext> {

    private static final Map<String, IOperator> OPERATOR_MAP;

    public FilterNodeConditionParser(NodeContext nodeContext) {
        super(nodeContext);
    }

    @Override
    public Expression parseCondition(String jsonArray) {
        return super.parseCondition(jsonArray, this::buildNodeExpression);
    }

    @Override
    public IOperator getContainOperator(ConditionStructure conditionStructure) {
        return OPERATOR_MAP.get(conditionStructure.getOperator());
    }

    @Override
    public ConditionStructure makeConditionStructure(String json) {
        JSONObject object = JSONObject.fromObject(json);
        ConditionStructure structure = new ConditionStructure();
        structure.setKey(object.optString("key"));
        structure.setValue(Arrays.asList(object.getJSONObject("value")));
        structure.setOperator(object.optString("operator"));
        structure.setWeight(Float.parseFloat(StringUtils.showEmpty(object.optString("weight"), "0")));
        structure.setSlop(object.optInt("slop"));
        structure.setSimilarity(object.optInt("similarity"));
        structure.setAnalyzer(object.optString("analyzer"));
        return structure;
    }

    private Expression buildNodeExpression(ConditionStructure conditionProperty) {
        // 获取操作符号
        IOperator operator = getOperator(conditionProperty);
        checkNotNull(operator, String.format("根据operator=%s未能查询出对应的条件！", conditionProperty.getOperator()));
        // 获取值
        JSONObject value = (JSONObject) conditionProperty.getValue().get(0);
        Either<Expression, List<Object>> valuesEither = getValues(value, conditionProperty);
        if(valuesEither.isLeft()){
            return valuesEither.getLeft();
        }
        Object[] valueArray = valuesEither.get().toArray(new Object[0]);
        List<IAttributesWrapper> attributesWrappers = attributesWrappers(operator, conditionProperty);
        return !CollectionUtils.isEmpty(attributesWrappers)
                ? Condition(conditionProperty.getKey(), operator, valueArray, attributesWrappers)
                : Condition(conditionProperty.getKey(), operator, valueArray);
    }

    private IOperator getOperator(ConditionStructure conditionProperty) {
        IOperator operator = OPERATOR_MAP.get(conditionProperty.getOperator());
        JSONObject value = (JSONObject) conditionProperty.getValue().get(0);
        String type = value.getString("type");
        if (GEO_CONTAINS.equals(type)) {
            return Operator.WKT;
        }
        return operator;
    }

    /**
     * 通过value获取到值
     *
     *
     * @param value v <br>
     * @param conditionProperty cp
     * @return 值列表
     */
    private Either<Expression, List<Object>> getValues(JSONObject value, ConditionStructure conditionProperty) {
        if ("empty".equals(conditionProperty.getOperator()) || "notEmpty".equals(conditionProperty.getOperator())) {
            return Either.right(Arrays.asList(""));
        }

        String type = value.getString("type");
        // 控制参数
        if (ValueType.CONTROL_PARAM.equals(type)) {
            String cv = value.optJSONArray("value").get(0).toString();
            Optional<ValueWrapper> valueWrapper = t.getValue(cv);
            // 如果没有传入控制参数，则不过滤
            if (valueWrapper.isEmpty()) {
                return Either.left(new EmtpyExpression());
            }
            String valueString = valueWrapper.get().getTargetValue(0).getValueString();
            if(valueString == null || "null".equals(valueString)){
                return Either.left(new EmtpyExpression());
            }
            Either<Expression, List<Object>> right = Either.right(Arrays.asList(valueString));
            return right;
        }

        return Either.right(value.optJSONArray("value"));
    }

    static {
        OPERATOR_MAP = new HashMap<>();
        OPERATOR_MAP.put("eq", Operator.Equal);
        OPERATOR_MAP.put("ne", Operator.NotEqual);
        OPERATOR_MAP.put("gt", Operator.GreaterThan);
        OPERATOR_MAP.put("lt", Operator.LessThan);
        OPERATOR_MAP.put("ge", Operator.GreaterThanOrEqual);
        OPERATOR_MAP.put("le", Operator.LessThanOrEqual);
        OPERATOR_MAP.put("in", Operator.In);
        OPERATOR_MAP.put("notIn", Operator.NotIn);
        OPERATOR_MAP.put("like", Operator.Like);
        OPERATOR_MAP.put("notLike", Operator.NotLike);
        OPERATOR_MAP.put("notNull", Operator.isNotNull);
        OPERATOR_MAP.put("isNull", Operator.isNull);
        OPERATOR_MAP.put("empty", Operator.Equal);
        OPERATOR_MAP.put("notEmpty", Operator.NotEqual);
        OPERATOR_MAP.put("regularMatch", Operator.Equal);
        OPERATOR_MAP.put("regularNotMatch", Operator.NotEqual);
        OPERATOR_MAP.put("wkt", Operator.WKT);
    }
}
