package com.trs.police.service.node.impl;

import com.trs.police.common.core.vo.node.ValueWrapper;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.ControlDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.dto.node.properties.FeatureInputProperties;
import com.trs.police.dto.node.properties.bean.ControlValue;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;
import com.trs.police.service.node.NodeService;
import com.trs.police.service.shared.feature.FeatureFacade;
import com.trs.web.builder.util.BeanFactoryHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 特征输入节点
 *
 * <AUTHOR>
 */
public class FeatureInputNode extends Node {

    private FeatureFacade featureFacade;

    public FeatureInputNode(NodeMeta nodeMeta, String nodeProperties, FeatureFacade featureFacade) {
        super(nodeMeta, nodeProperties);
        this.featureFacade = featureFacade;
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        FeatureInputProperties property = getPropertyAs(FeatureInputProperties.class);
        ProcessDTO process = featureFacade.getFeatureProcess(property.getFeatureId());

        // 上下文添加控制参数的值
        for (ControlValue controlValue : Optional.ofNullable(property.getControlValue()).orElse(new ArrayList<>())) {
            Optional<ControlDTO> first = process.getControl().stream()
                    .filter(c -> c.getName().equals(controlValue.getControlName()))
                    .findAny();
            if (first.isEmpty()) {
                throw new RuntimeException("特征输入节点配置错误，没有找到控制参数：" + controlValue.getControlName());
            }
            context.addControlValue(controlValue.getControlName(), controlValue(first.get().getType(), controlValue));
        }

        NodeContext featureContext = new NodeContext(process.getControl());
        featureContext.setControlValueMap(context.getControlValueMap());

        NodeService service = BeanFactoryHolder.getBean(NodeService.class).get();
        NodeData nodeData = service.previewNode(process, featureContext);
        nodeData.setNodeMeta(this.nodeMeta);

        // 重新设置本节点的uuid
        nodeData.getHeader()
                .forEach(field -> field.setFromNode(this.nodeMeta.getUuid()));
        return nodeData;
    }

    @Override
    public Integer nodeType() {
        return NodeType.FEATURE_IN;
    }

    private ValueWrapper controlValue(String typeCode, ControlValue controlValue) {
        String[] array = controlValue.getValue().toArray(String[]::new);
        return new ValueWrapper(typeCode, array);
    }
}
