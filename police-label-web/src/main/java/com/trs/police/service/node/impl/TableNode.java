package com.trs.police.service.node.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.EmtpyExpression;
import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.builder.EsSearchBuilderFactory;
import com.trs.es.esbean.builder.EsSearchBuilderImpl;
import com.trs.police.dto.node.properties.TableNodeProperties;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.DataTableOverviewDto;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.FilterNodeConditionParser;
import com.trs.police.service.node.Node;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.datatable.DataTableService;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.vo.DataTableFieldVO;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;


import java.util.List;
import java.util.stream.Collectors;

/**
 * 表格输入节点
 *
 * <AUTHOR>
 */
@Slf4j
public class TableNode extends Node {


    private DataTableService dataTableService;

    private FieldsService fieldsService;

    public TableNode(NodeMeta nodeMeta, String nodeProperties, DataTableService dataTableService, FieldsService fieldsService) {
        super(nodeMeta, nodeProperties);
        this.dataTableService = dataTableService;
        this.fieldsService = fieldsService;
    }

    @Override
    public NodeData process(List<NodeData> inputNode, NodeContext context) {
        TableNodeProperties property = getPropertyAs(TableNodeProperties.class);
        List<DataTableFieldVO> fieldInfo = fieldsService.getFieldInfo(property.getTableId().intValue());
        List<FieldInfoVO> header = fieldInfo.stream()
                .map(f -> new FieldInfoVO(f.getFieldNameCn(), f.getFieldName(), f.getFieldType(), nodeMeta.getUuid()))
                .collect(Collectors.toList());

        NodeData nodeData = new NodeData();
        nodeData.setHeader(header);
        nodeData.setNodeMeta(nodeMeta);

        DataTableOverviewDto dto = new DataTableOverviewDto();
        dto.setTableId(property.getTableId());
        Integer count = BeanFactoryHolder.getEnv().getProperty("label.web.node.table.default.count", Integer.class, 50);
        dto.setPageSize(count);

        // 构造条件
        FilterNodeConditionParser parser = new FilterNodeConditionParser(context);
        Expression expression = StringUtils.isNotEmpty(property.getTokens())
                ? parser.parseCondition(property.getTokens())
                : new EmtpyExpression();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        EsSearchBuilderImpl esSearchBuilder = (EsSearchBuilderImpl) EsSearchBuilderFactory.createNewBuilder(0, 40)
                .where(expression);
        QueryBuilder queryBuilder = esSearchBuilder.getBoolQueryBuilder();
        searchSourceBuilder.query(queryBuilder);
        log.info("执行条件：{}", searchSourceBuilder.toString());
        RestfulResultsV2<JSONObject> dataOverview = dataTableService.getData(dto, expression);
        List<JSONObject> datas = dataOverview.getDatas();
        List<List<FieldValue>> data = datas.stream()
                .map(d -> mapToFieldValue(header, d))
                .collect(Collectors.toList());

        nodeData.setData(data);
        nodeData.setTotalCount(dataOverview.getSummary().getTotal());
        return nodeData;
    }

    @Override
    public Integer nodeType() {
        return NodeType.TABLE;
    }

    private List<FieldValue> mapToFieldValue(List<FieldInfoVO> header, JSONObject jsonObject) {
        return header.stream()
                .map(field -> new FieldValue(jsonObject.getString(field.getEnName()), field))
                .collect(Collectors.toList());
    }
}
