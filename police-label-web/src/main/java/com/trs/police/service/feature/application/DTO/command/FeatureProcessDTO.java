package com.trs.police.service.feature.application.DTO.command;

import com.trs.police.dto.node.ControlDTO;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.OrderDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *  特征流程DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FeatureProcessDTO {

    /**
     *  基本信息
     */
    private FeatureProcessInfoDTO info;

    /**
     *  节点列表
     */
    private List<NodeDTO> nodes;

    /**
     * 节点编排信息
     */
    private List<OrderDTO> nodeOrders;

    /**
     * 控制参数
     */
    private List<ControlDTO> control;

    /**
     * 是否是手动
     */
    private Boolean manual;

}
