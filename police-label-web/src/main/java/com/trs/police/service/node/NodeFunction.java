package com.trs.police.service.node;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.web.builder.base.IKey;

import java.util.List;

/**
 * 函数
 *
 * <AUTHOR>
 */
public interface NodeFunction extends IK<PERSON> {

    /**
     * 执行计算 的到计算结果
     *
     * @param parameters 参数
     * @return 计算结果
     */
    FieldValue execute(List<FieldValue> parameters);

    @Override
    default String desc() {
        return "";
    }
}
