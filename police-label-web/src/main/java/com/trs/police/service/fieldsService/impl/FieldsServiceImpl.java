package com.trs.police.service.fieldsService.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.constant.DbConstant;
import com.trs.police.entity.dataField.DataField;
import com.trs.police.mapper.DataFieldMapper;
import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.vo.DataTableFieldVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 字段service
 */
@Service
public class FieldsServiceImpl implements FieldsService {

    @Autowired
    private DataFieldMapper dataFieldMapper;

    @Override
    public List<DataTableFieldVO> getFieldInfo(Integer tableId,Integer selectedStatus) {
        QueryWrapper<DataField> queryWrapper = new QueryWrapper<DataField>().eq("table_id", tableId)
                .eq(Objects.nonNull(selectedStatus),"selected_status", selectedStatus)
                .ne("status", DbConstant.DELETED);
        List<DataField> dataFields = dataFieldMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(dataFields)){
            return new ArrayList<>();
        }
        List<DataTableFieldVO> tableFieldVoS = new ArrayList<>();
        for (DataField dataField : dataFields) {
            DataTableFieldVO tableFieldVO = new DataTableFieldVO();
            tableFieldVO.setId(dataField.getId());
            tableFieldVO.setFieldName(dataField.getFieldName());
            tableFieldVO.setFieldNameCn(StringUtils.isNotEmpty(dataField.getFieldNameCn())
                    ? dataField.getFieldNameCn() : dataField.getFieldName());
            tableFieldVO.setFieldType(dataField.getFieldType());
            tableFieldVO.setFieldTypeName(DataBaseFieldMappingType.getTypeName(dataField.getFieldType()));
            tableFieldVoS.add(tableFieldVO);
        }
        return tableFieldVoS;
    }

    @Override
    public List<DataTableFieldVO> getFieldInfo(Integer tableId) {
        QueryWrapper<DataField> queryWrapper = new QueryWrapper<DataField>()
                .eq("table_id", tableId)
                .ne("status", DbConstant.DELETED);
        List<DataField> dataFields = dataFieldMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(dataFields)){
            return new ArrayList<>();
        }
        List<DataTableFieldVO> tableFieldVoS = new ArrayList<>();
        for (DataField dataField : dataFields) {
            DataTableFieldVO tableFieldVO = new DataTableFieldVO();
            tableFieldVO.setFieldName(dataField.getFieldName());
            tableFieldVO.setFieldNameCn(StringUtils.isNotEmpty(dataField.getFieldNameCn())
                    ? dataField.getFieldNameCn() : dataField.getFieldName());
            tableFieldVO.setFieldType(dataField.getFieldType());
            tableFieldVO.setFieldTypeName(DataBaseFieldMappingType.getTypeName(dataField.getFieldType()));
            tableFieldVoS.add(tableFieldVO);
        }
        return tableFieldVoS;
    }

}
