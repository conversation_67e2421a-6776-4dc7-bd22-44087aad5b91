package com.trs.police.service.label.assembler;

import com.trs.police.dto.label.LabelAddDTO;
import com.trs.police.entity.label.LabelDO;
import org.springframework.stereotype.Component;

/**
 * 标签新增转换器
 *
 * <AUTHOR>
 */
@Component
public class LabelAddAssembler {

    /**
     * 转换
     *
     * @param dto dto
     * @return do
     */
    public LabelDO toLabelDO(LabelAddDTO dto) {
        LabelDO labelDO = new LabelDO();
        labelDO.setLabelName(dto.getLabelName());
        labelDO.setEnName(dto.getLabelEnName());
        labelDO.setDescription(dto.getDescription());
        labelDO.setCategoryCode(dto.getCategoryCode());
        labelDO.setPoliceKind(dto.getPoliceKind());
        labelDO.setBusinessRule(dto.getBusinessRule());
        labelDO.setColor(dto.getColor());
        labelDO.setIsCustomColor(dto.getIsCustomColor());
        labelDO.setDeleted(0);
        labelDO.setProcessOrder(null);
        return labelDO;
    }
}
