package com.trs.police.service.dict;

import com.trs.police.dto.dict.DictAddDTO;
import com.trs.police.dto.dict.DictDto;

import java.util.List;

/**
 * 码表服务接口
 *
 * <AUTHOR>
 */
public interface DictService {

    /**
     * 根据类型查询
     *
     * @param type 类型
     * @return {@link DictDto}
     */
    List<DictDto> getDictListByType(String type);

    /**
     * 根据类型查询
     *
     * @param type 类型
     * @return {@link DictDto}
     */
    List<DictDto> getFlatDictList(String type);


    /**
     * 添加
     *
     * @param dto 参数
     * @return {@link DictDto}
     */
    DictDto add(DictAddDTO dto);

    /**
     * 删除
     *
     * @param id id
     */
    void deleteDict(Long id);
}
