package com.trs.police.service.node.formula.function;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.NodeFunction;

import java.util.List;

/**
 * 复制函数
 *
 * <AUTHOR>
 */
public class CopyFunction implements NodeFunction {

    public static final CopyFunction INSTANCE = new CopyFunction();

    @Override
    public FieldValue execute(List<FieldValue> parameters) {
        return parameters.get(0);
    }

    @Override
    public String key() {
        return "copy";
    }
}
