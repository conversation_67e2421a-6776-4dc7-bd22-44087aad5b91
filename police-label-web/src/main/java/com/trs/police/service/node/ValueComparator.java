package com.trs.police.service.node;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;

import java.math.BigDecimal;
import java.util.Comparator;

/**
 * 值比较器
 *
 * <AUTHOR>
 */
public class ValueComparator {

    /**
     * 比较两个值
     *
     * @param value1 v1
     * @param value2 v2
     * @return 比较结果
     */
    public static int compareValues(FieldValue value1, FieldValue value2) {
        if (StringUtils.isEmpty(value1.getValue()) && StringUtils.isEmpty(value2.getValue())) {
            return 0;
        }
        if (StringUtils.isEmpty(value1.getValue())) {
            return -1;
        }
        if (StringUtils.isEmpty(value2.getValue())) {
            return 1;
        }
        if (value1.getTypeCode().equals(DataBaseFieldMappingType.NUMBER.getFieldType())
                && value2.getTypeCode().equals(DataBaseFieldMappingType.NUMBER.getFieldType())) {
            BigDecimal d1 = BigDecimal.valueOf(Double.valueOf(value1.getValue()));
            BigDecimal d2 = BigDecimal.valueOf(Double.valueOf(value2.getValue()));
            return d1.compareTo(d2);
        }
        if (value1.getTypeCode().equals(DataBaseFieldMappingType.DATETIME.getFieldType())
                && value2.getTypeCode().equals(DataBaseFieldMappingType.DATETIME.getFieldType())) {
            return TimeUtils.stringToDate(value1.getValue()).compareTo(TimeUtils.stringToDate(value2.getValue()));
        }
        if (value1.getTypeCode().equals(DataBaseFieldMappingType.STRING.getFieldType())
                && value2.getTypeCode().equals(DataBaseFieldMappingType.STRING.getFieldType())) {
            return Comparator.comparing((String str) -> str, String::compareTo).compare(value1.getValue(), value2.getValue());
        }
        // 类型不同无法比较
        return 0;
    }
}
