package com.trs.police.service.label.assembler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.utils.VoParameterConstructor;
import com.trs.police.constant.label.EffectiveTimeEnum;
import com.trs.police.constant.label.LabelUpdateMethodEnum;
import com.trs.police.dto.label.LabelOutputDTO;
import com.trs.police.dto.label.node.LabelProcessDTO;
import com.trs.police.entity.label.LabelComputeResultDO;
import com.trs.police.entity.label.LabelDO;
import com.trs.police.enums.LabelCalculationStatus;
import com.trs.police.mapper.LabelMapper;
import com.trs.police.service.shared.dict.DictFacade;
import com.trs.police.service.shared.dict.DictReference;
import com.trs.police.service.shared.dict.DictTypeRegistry;
import com.trs.police.service.shared.feature.Feature;
import com.trs.police.service.shared.feature.FeatureFacade;
import com.trs.police.service.shared.field.DataTableFieldDTO;
import com.trs.police.service.shared.field.FieldFacade;
import com.trs.police.service.shared.table.TableFacade;
import com.trs.police.service.shared.table.TableReference;
import com.trs.police.service.shared.task.LabelCalculationTaskDTO;
import com.trs.police.service.shared.task.TaskFacade;
import com.trs.police.service.shared.user.CurrentUserDTO;
import com.trs.police.service.shared.user.UserDeptReference;
import com.trs.police.service.shared.user.UserFacade;
import com.trs.police.vo.label.application.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特征转换
 *
 * <AUTHOR>
 */
@Component
public class LabelSearchAssembler {

    @Autowired
    private DictTypeRegistry dictTypeRegistry;

    @Autowired
    private DictFacade dictFacade;

    @Autowired
    private UserFacade userFacade;

    @Autowired
    private FeatureFacade featureFacade;

    @Autowired
    private TaskFacade taskFacade;

    @Autowired
    private FieldFacade fieldFacade;

    @Autowired
    private TableFacade tableFacade;

    @Autowired
    private LabelProcessAssembler processAssembler;

    @Autowired
    private LabelMapper labelMapper;

    /**
     * 特征转换
     *
     * @param label lb
     * @return vo
     */
    public List<LabelVO> toLabelVO(List<LabelDO> label) {
        List<LabelVO> voList = label.stream()
                .map(labelDO -> {
                    LabelVO vo = new LabelVO();
                    vo.setLabelId(labelDO.getId());
                    vo.setLabelName(labelDO.getLabelName());
                    vo.setDescription(labelDO.getDescription());
                    vo.setCategoryCode(labelDO.getCategoryCode());
                    vo.setPoliceKind(labelDO.getPoliceKind());
                    vo.setBusinessRule(labelDO.getBusinessRule());
                    vo.setColor(labelDO.getColor());
                    vo.setIsCustomColor(labelDO.getIsCustomColor());
                    vo.setLabelEnName(labelDO.getEnName());
                    vo.setStatus(labelDO.getStatus());
                    vo.setCreateTime(labelDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    vo.setUpdateMethod(labelDO.getLabelType());
                    vo.setUpdateMethodName(LabelUpdateMethodEnum.getTypeName(labelDO.getLabelType()));
                    vo.setLabelType(labelDO.getLabelType());
                    vo.setCycleTimeType(labelDO.getCycleTimeType());
                    vo.setCycleTime(labelDO.getCycleTime());
                    vo.setEffectiveTimeType(labelDO.getEffectiveTimeType());
                    vo.setEffectiveTime(labelDO.getEffectiveTime());
                    vo.setHitRate(labelDO.getHits());
                    if (EffectiveTimeEnum.TERM.getCode().equals(labelDO.getEffectiveTimeType()) && Objects.nonNull(labelDO.getEffectiveTime())) {
                        LocalDateTime endTtime = labelDO.getCreateTime().plusDays(labelDO.getEffectiveTime());
                        vo.setValidityPeriod(endTtime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    vo.setBusinessRule(labelDO.getBusinessRule());
                    if (Objects.nonNull(labelDO.getLastFinishTime())) {
                        vo.setLastFinishTime(labelDO.getLastFinishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    if (Objects.nonNull(labelDO.getLastRunTime())) {
                        vo.setLastRunTime(labelDO.getLastRunTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    vo.setCreateUserId(labelDO.getCreateUserId());
                    vo.setCreateDeptId(labelDO.getCreateDeptId());
                    List<LabelMainObjectVO> mainObjects = (Objects.isNull(labelDO.getMainObject()) || labelDO.getMainObject().isEmpty())
                            ? new ArrayList<>()
                            : JSON.parseArray(labelDO.getMainObject(), LabelMainObjectVO.class);
                    vo.setMainObjectCode(mainObjects.size() > 0 ? mainObjects.get(0).getMainObjectTypeCode() : null);
                    vo.setFeatureIds((Objects.isNull(labelDO.getFeatureId()) || labelDO.getFeatureId().isEmpty()) ? new ArrayList<>() : JSONArray.parseArray(labelDO.getFeatureId(), Long.class));

                    // 返回基本信息
                    LabelBaseInfoVO info = new LabelBaseInfoVO();
                    info.setLabelId(labelDO.getId());
                    info.setLabelType(labelDO.getLabelType());
                    info.setCycleTimeType(labelDO.getCycleTimeType());
                    info.setCycleTime(labelDO.getCycleTime());
                    info.setEffectiveTimeType(labelDO.getEffectiveTimeType());
                    info.setEffectiveTime(labelDO.getEffectiveTime());
                    info.setMainObject(JSON.parseArray(labelDO.getMainObject(), LabelMainObjectVO.class));
                    info.setName(labelDO.getLabelName());
                    info.setEnName(labelDO.getEnName());
                    info.setPoliceKind(labelDO.getPoliceKind());
                    info.setBusinessRule(labelDO.getBusinessRule());
                    info.setCategoryCode(labelDO.getCategoryCode());
                    info.setColor(labelDO.getColor());

                    JSONObject process = StringUtil.isEmpty(labelDO.getProcessOrder()) ? new JSONObject() : JSON.parseObject(labelDO.getProcessOrder());
                    process.put("info", info);
                    vo.setProcessOrder(process);
                    if (StringUtils.isNotEmpty(labelDO.getProcessOrderSnapshot())) {
                        JSONObject processS = JSON.parseObject(labelDO.getProcessOrderSnapshot());
                        LabelOutputDTO outputDTO = processAssembler.labelOutputDTO(processS.toJavaObject(LabelProcessDTO.class));
                        LabelBaseInfoVO infoS = new LabelBaseInfoVO();
                        infoS.setLabelId(labelDO.getId());
                        infoS.setLabelType(outputDTO.getLabelType());
                        infoS.setCycleTimeType(outputDTO.getCycleTimeType());
                        infoS.setCycleTime(outputDTO.getCycleTime());
                        infoS.setEffectiveTimeType(outputDTO.getEffectiveTimeType());
                        infoS.setEffectiveTime(outputDTO.getEffectiveTime());
                        List<LabelMainObjectVO> mainOb = Optional.ofNullable(outputDTO.getMainObject())
                                .orElse(new ArrayList<>())
                                .stream()
                                .map(m -> new LabelMainObjectVO(m.getEnName(), m.getFromNode(), m.getMainObjectTypeCode()))
                                .collect(Collectors.toList());
                        infoS.setMainObject(mainOb);
                        infoS.setName(outputDTO.getName());
                        infoS.setEnName(outputDTO.getEnName());
                        infoS.setPoliceKind(outputDTO.getPoliceKind());
                        infoS.setBusinessRule(outputDTO.getBusinessRule());
                        infoS.setCategoryCode(outputDTO.getCategoryCode());
                        infoS.setColor(outputDTO.getColor());
                        processS.put("info", infoS);
                        vo.setProcessOrderSnapshot(processS);
                    }
                    return vo;
                })
                .collect(Collectors.toList());
        List<UserDeptReference> userDeptReferenceList = label.stream()
                .map(LabelDO::getCreateUserDept)
                .map(u -> new UserDeptReference(u.getUserId(), u.getDeptId()))
                .collect(Collectors.toList());
        final List<CurrentUserDTO> users = userFacade.findCurrentUsers(userDeptReferenceList);

        final List<Feature> features = featureFacade.featureList(voList.stream().map(LabelVO::getFeatureIds).flatMap(List::stream).collect(Collectors.toList()));

        final List<LabelCalculationTaskDTO> tasks = taskFacade.allLabelCalculationTasks();

        VoParameterConstructor.of(voList)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getLabelCategory()),
                        (v, d) -> Objects.equals(v.getCategoryCode(), d.getCode())
                )
                .consumer(LabelVO::setCategoryDict)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getPoliceKind()),
                        (v, d) -> Objects.equals(v.getPoliceKind(), d.getCode())
                )
                .consumer(LabelVO::setPoliceKindDict)
                .singleValueMatcher(d -> users, (lb, u) -> u.equals(new CurrentUserDTO(lb.getCreateUserId(), lb.getCreateDeptId())))
                .consumer(LabelVO::setCreateUser)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getFeatureMainObject()),
                        (v, d) -> Objects.equals(v.getMainObjectCode(), d.getCode())
                )
                .consumer(LabelVO::setMainObjectDict)
                .multiValueMatcher(vs -> features, (lb, f) -> lb.getFeatureIds().contains(f.getFeatureId()))
                .consumer(LabelVO::setFeatures)
                .singleValueMatcher(vs -> tasks, (lb, t) -> Objects.equals(lb.getLabelId(), t.getLabelId()))
                .consumer((v, t) -> {
                    Optional<LabelCalculationStatus> statusEnum = Optional.of(t.getStatusEnum());
                        statusEnum.ifPresent(en -> {
                            v.setUpdateStatusName(en.getDescription());
                            v.setUpdateStatus(en.getCode());
                        });
                        v.setRunErrorMessage(t.getErrorMessage());
                    // 设置执行时间
                    if (Objects.nonNull(t.getStartTime())) {
                        v.setLastFinishTime(t.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    if (Objects.nonNull(t.getEndTime())) {
                        v.setLastRunTime(t.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                })
                .build();
        return voList;
    }

    /**
     * 构造标签结果
     *
     * @param dos dos
     * @return vo
     */
    public List<LabelResultVO> buildResult(List<LabelComputeResultDO> dos) {
        List<LabelResultVO> vo = dos.stream()
                .map(this::buildResultSingle)
                .collect(Collectors.toList());
        List<LabelResultRelatedVO> rd = vo.stream()
                .map(LabelResultVO::getRelatedData)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 构造表头
        Map<Long, List<DataTableFieldDTO>> tableFieldMap = new HashMap<>();
        for (LabelResultRelatedVO r : rd) {
            if (Objects.isNull(r.getTableId()) || tableFieldMap.containsKey(r.getTableId())) {
                continue;
            }
            tableFieldMap.put(r.getTableId(), fieldFacade.findByTable(new TableReference(r.getTableId())));

        }
        for (LabelResultRelatedVO r : rd) {
            if (Objects.nonNull(r.getTableId())) {
                r.setFiledInfo(tableFieldMap.get(r.getTableId()));
                String primaryKey = tableFacade.getPrimaryKey(r.getTableId());
                r.setPrimaryKey(primaryKey);
            }
        }

        // 设置关联的数据
        Map<Long, List<LabelResultRelatedVO>> groupByTableId = rd.stream()
                .filter(r -> Objects.nonNull(r.getTableId()))
                .filter(r -> CollectionUtils.isNotEmpty(r.getIds()))
                .collect(Collectors.groupingBy(r -> r.getTableId()));
        for (Map.Entry<Long, List<LabelResultRelatedVO>> entry : groupByTableId.entrySet()) {
            List<String> ids = entry.getValue()
                    .stream()
                    .map(LabelResultRelatedVO::getIds)
                    .flatMap(List::stream)
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, JSONObject> data = tableFacade.getDataOverviewByPrimaryKey(entry.getKey(), ids);
            for (LabelResultRelatedVO r : entry.getValue()) {
                List<JSONObject> d = r.getIds()
                        .stream()
                        .map(data::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                r.setData(d);
            }
        }

        // 标签基本信息
        List<String> collect = dos.stream()
                .map(LabelComputeResultDO::getLabelName)
                .distinct()
                .collect(Collectors.toList());
        List<LabelDO> label = collect.isEmpty() ? new ArrayList<>() :  labelMapper.selectList(
                Wrappers.lambdaQuery(LabelDO.class)
                        .in(LabelDO::getLabelName, collect)
        );
        Map<String, LabelDO> groupByName = label.stream()
                .collect(Collectors.toMap(LabelDO::getLabelName, d -> d, (a, b) -> b));
        vo.forEach(v -> {
            if (groupByName.containsKey(v.getLabelName())) {
                LabelDO labelDO = groupByName.get(v.getLabelName());
                LabelSimpleVO simpleVO = new LabelSimpleVO();
                simpleVO.setId(labelDO.getId());
                simpleVO.setName(labelDO.getLabelName());
                simpleVO.setColor(labelDO.getColor());
                simpleVO.setDescription(labelDO.getDescription());
                v.setLabelInfo(simpleVO);
            }
        });
        return vo;
    }

    private LabelResultVO buildResultSingle(LabelComputeResultDO d) {
        LabelResultVO vo = new LabelResultVO();
        vo.setId(d.getId());
        vo.setObjectNumber(d.getObjectNumber());
        vo.setObjectType(d.getObjectType());
        vo.setLabelName(d.getLabelName());
        vo.setPoliceKindReference(new DictReference(d.getPoliceKind(), dictTypeRegistry.getPoliceKind()));
        vo.setPoliceKind(null);
        vo.setFrequency(d.getFrequency());
        vo.setRelatedObjectNumber(StringUtil.isEmpty(d.getRelatedObjectNumber()) ? new ArrayList<>() : Arrays.asList(d.getRelatedObjectNumber().split(";")));
        String sourceDataIds = d.getSourceDataIds();
        if (StringUtil.isEmpty(sourceDataIds)) {
            vo.setRelatedData(new ArrayList<>());
        }
        List<SourceDataVO> sd = JSON.parseArray(sourceDataIds, SourceDataVO.class);
        List<LabelResultRelatedVO> reList = sd.stream()
                .map(s -> {
                    LabelResultRelatedVO r = new LabelResultRelatedVO();
                    r.setIds(s.getIds());
                    r.setTableId(s.getTable_id());
                    return r;
                })
                .collect(Collectors.toList());
        vo.setRelatedData(reList);
        return vo;
    }
}
