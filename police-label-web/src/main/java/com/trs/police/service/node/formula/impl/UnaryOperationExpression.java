package com.trs.police.service.node.formula.impl;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.node.Operator;
import com.trs.police.service.node.formula.Expression;
import com.trs.police.service.node.formula.FormulaContext;

import java.util.Objects;

/**
 * 一元运算符表达式
 *
 * <AUTHOR>
 */
public class UnaryOperationExpression implements Expression {
    private final Operator operator;
    private final Expression operand;

    public UnaryOperationExpression(Operator operator, Expression operand) {
        this.operator = operator;
        this.operand = operand;
    }

    @Override
    public FieldValue evaluate(FormulaContext context) {
        FieldValue value = operand.evaluate(context);

        switch (operator) {
            case ADD: // +
                return value;
            case SUBTRACT: // -
                if (Objects.isNull(value.getValue()) || value.getValue().isEmpty()) {
                    value.setValue("0");
                }
                value.setValue("-" + value.getValue());
                return value;
            default:
                throw new UnsupportedOperationException("Unsupported unary operator: " + operator);
        }
    }
}
