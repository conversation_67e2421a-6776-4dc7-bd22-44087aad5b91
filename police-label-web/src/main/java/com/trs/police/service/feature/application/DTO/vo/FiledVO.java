package com.trs.police.service.feature.application.DTO.vo;

import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
public class FiledVO {
    /**
     * 来源节点 uuid
     */
    private String fromNode;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 名称
     */
    private String enName;

    /**
     * 类型编码 {@link DataBaseFieldMappingType}
     */
    private String typeCode;

    public FiledVO(String fromNode, String cnName, String enName, String typeCode) {
        this.fromNode = fromNode;
        this.cnName = cnName;
        this.enName = enName;
        this.typeCode = typeCode;
    }
}
