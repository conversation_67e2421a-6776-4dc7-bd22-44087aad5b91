package com.trs.police.service.node.impl.nodeDTO;

import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.OrderDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * LabelNodeDTO构建工厂
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Component
public class LabelNodeDTOBuilderFactory {
    @Autowired
    private List<LabelNodeDTOBuilder> labelNodeDtoBuilders;

    /**
     * 根据策略构建节点
     *
     * @param nodeDTO 节点
     * @param nodeContext 节点上下文
     * @return 节点列表
     */
    public List<LabelNodeDTO> buildByStrategy(NodeDTO nodeDTO, NodeContext nodeContext) {
        for (LabelNodeDTOBuilder builder : labelNodeDtoBuilders) {
            if (builder.supports(nodeDTO.getNodeMeta().getNodeTypeCode())) {
                return builder.build(nodeDTO, nodeContext);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 排序节点
     *
     * @param nodes 节点列表
     * @param nodeOrders 节点排序
     * @return 节点列表
     */
    public List<NodeDTO> sortNodes(List<NodeDTO> nodes, List<OrderDTO> nodeOrders){
        // 构建节点映射
        Map<String, NodeDTO> nodeMap = nodes.stream()
                .collect(Collectors.toMap(node -> node.getNodeMeta().getUuid(), node -> node));

        // 构建排序后的节点列表
        List<NodeDTO> sortedNodes = new ArrayList<>();

        // 找到起始节点（没有前置节点的节点）
        Set<String> hasPreNode = nodeOrders.stream()
                .map(order -> order.getTo())
                .collect(Collectors.toSet());

        // 从起始节点开始排序
        NodeDTO currentNode = nodes.stream()
                .filter(node -> !hasPreNode.contains(node.getNodeMeta().getUuid()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("找不到起始节点"));

        while (currentNode != null) {
            sortedNodes.add(currentNode);
            String currentUuid = currentNode.getNodeMeta().getUuid();
            // 查找下一个节点
            currentNode = nodeOrders.stream()
                    .filter(order -> order.getFrom().equals(currentUuid))
                    .map(order -> nodeMap.get(order.getTo()))
                    .findFirst()
                    .orElse(null);
            // 设置上一个节点ID
            if(currentNode!=null){
                currentNode.setLastNodeId(currentUuid);
            }
        }
        return sortedNodes;
    }
}
