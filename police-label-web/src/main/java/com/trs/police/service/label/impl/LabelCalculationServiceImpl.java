package com.trs.police.service.label.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.police.entity.label.LabelCalculationTaskDO;
import com.trs.police.entity.label.LabelDO;
import com.trs.police.enums.LabelCalculationStatus;
import com.trs.police.mapper.LabelCalculationTaskMapper;
import com.trs.police.mapper.LabelMapper;
import com.trs.police.service.label.LabelCalculationExecutor;
import com.trs.police.service.label.LabelCalculationService;
import com.trs.police.utils.CycleTimeUtils;
import com.trs.police.vo.LabelComputeStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 标签计算服务实现
 */
@Slf4j
@Service
public class LabelCalculationServiceImpl implements LabelCalculationService {

    @Autowired
    private LabelCalculationTaskMapper taskMapper;

    @Autowired
    private LabelMapper labelMapper;

    @Autowired
    private LabelCalculationExecutor calculationExecutor;

    @Override
    public String triggerManualCalculation(Long labelId) {
        log.info("手动触发标签计算: labelId={}", labelId);

        // 验证标签是否存在
        LabelDO label = labelMapper.selectById(labelId);
        if (label == null) {
            throw new IllegalArgumentException("标签不存在: " + labelId);
        }

        // 检查是否有正在运行的任务
        List<LabelCalculationTaskDO> runningTasks = getRunningTasksByLabelId(labelId);
        if (!runningTasks.isEmpty()) {
            log.warn("标签已有正在运行的计算任务: labelId={}, runningTaskCount={}", labelId, runningTasks.size());
            throw new IllegalStateException("标签已有正在运行的计算任务，请等待完成后再试");
        }

        String taskId = generateTaskId(labelId);

        // 异步执行计算任务
        calculationExecutor.executeCalculation(taskId, labelId, 0); // 0=手动触发

        log.info("手动触发标签计算任务已提交: taskId={}, labelId={}", taskId, labelId);
        return taskId;
    }

    @Override
    public String triggerScheduledCalculation(Long labelId) {
        log.info("定时触发标签计算: labelId={}", labelId);

        // 验证标签是否存在且启用
        LabelDO label = labelMapper.selectById(labelId);
        if (label == null || label.getStatus() != 1) {
            log.warn("标签不存在或未启用: labelId={}", labelId);
            return null;
        }

        // 检查是否有正在运行的任务
        List<LabelCalculationTaskDO> runningTasks = getRunningTasksByLabelId(labelId);
        if (!runningTasks.isEmpty()) {
            log.warn("标签已有正在运行的计算任务，跳过定时触发: labelId={}", labelId);
            return null;
        }

        String taskId = generateTaskId(labelId);

        // 异步执行计算任务
        calculationExecutor.executeCalculation(taskId, labelId, 1); // 1=定时触发

        log.info("定时触发标签计算任务已提交: taskId={}, labelId={}", taskId, labelId);
        return taskId;
    }

    @Override
    public LabelComputeStatusVO getTaskStatus(Long labelId) {
        PreConditionCheck.checkNotNull(labelId, "标签ID不能为空");

        LabelCalculationTaskDO task = taskMapper.findByLabelId(labelId);
        if (task == null) {
            return new LabelComputeStatusVO("UNKNOWN", null);
        }

        return new LabelComputeStatusVO(task.getStatus(), task.getErrorMessage());
    }

    @Override
    public boolean cancelTask(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return false;
        }

        try {
            LabelCalculationTaskDO task = taskMapper.findByTaskId(taskId);
            if (task == null) {
                log.warn("任务不存在: taskId={}", taskId);
                return false;
            }

            // 只能取消等待中或运行中的任务
            if (!task.getStatusEnum().isRunning() && task.getStatusEnum() != LabelCalculationStatus.PENDING) {
                log.warn("任务状态不允许取消: taskId={}, status={}", taskId, task.getStatus());
                return false;
            }

            // 更新任务状态为已取消
            taskMapper.updateTaskStatus(taskId, LabelCalculationStatus.CANCELLED.getCode(),
                                      LocalDateTime.now(), "用户手动取消", LocalDateTime.now());

            log.info("任务已取消: taskId={}", taskId);
            return true;

        } catch (Exception e) {
            log.error("取消任务失败: taskId={}", taskId, e);
            return false;
        }
    }

    @Override
    public List<LabelCalculationTaskDO> getCalculationHistory(Long labelId, Integer limit) {
        if (labelId == null) {
            throw new IllegalArgumentException("标签ID不能为空");
        }

        if (limit == null || limit <= 0) {
            limit = 10; // 默认返回最近10条
        }

        return taskMapper.findByLabelIdOrderByCreateTimeDesc(labelId, limit);
    }

    @Override
    public List<Long> getScheduledLabels() {
        // 查询所有启用的自动标签
        QueryWrapper<LabelDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1) // 启用状态
                   .eq("label_type", 1) // 自动标签
                   .eq("deleted", 0) // 未删除
                   .isNotNull("cycle_time_type")
                   .isNotNull("cycle_time")
                   .gt("cycle_time", 0);

        List<LabelDO> labels = labelMapper.selectList(queryWrapper);
        return labels.stream().map(LabelDO::getId).collect(Collectors.toList());
    }

    @Override
    public boolean shouldCalculate(Long labelId) {
        if (labelId == null) {
            return false;
        }

        try {
            // 查询标签信息
            LabelDO label = labelMapper.selectById(labelId);
            if (label == null || label.getStatus() != 1 || label.getLabelType() != 1) {
                return false; // 标签不存在、未启用或非自动标签
            }

            // 检查周期配置
            if (label.getCycleTimeType() == null || label.getCycleTime() == null || label.getCycleTime() <= 0) {
                return false; // 周期配置无效
            }

            // 获取最近一次执行时间
            LocalDateTime lastRunTime = taskMapper.getLastRunTime(labelId);

            // 判断是否需要执行
            return CycleTimeUtils.shouldExecute(lastRunTime, label.getCycleTimeType(), label.getCycleTime());

        } catch (Exception e) {
            log.error("检查标签是否需要计算失败: labelId={}", labelId, e);
            return false;
        }
    }

    /**
     * 获取标签正在运行的任务
     *
     * @param labelId 标签ID
     * @return 正在运行的任务列表
     */
    private List<LabelCalculationTaskDO> getRunningTasksByLabelId(Long labelId) {
        QueryWrapper<LabelCalculationTaskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("label_id", labelId)
                   .eq("status", LabelCalculationStatus.RUNNING.getCode())
                   .eq("deleted", 0);
        return taskMapper.selectList(queryWrapper);
    }

    /**
     * 生成任务ID
     *
     * @param labelId 标签ID
     * @return 任务ID
     */
    private String generateTaskId(Long labelId) {
        return "TASK_" + labelId + "_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
}