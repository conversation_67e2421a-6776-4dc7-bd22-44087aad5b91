package com.trs.police.service.node.impl.nodeDTO;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.properties.LabelOutputProperties;
import com.trs.police.dto.node.properties.bean.MainObject;
import com.trs.police.vo.RowFieldVo;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签输出节点
 */
@Component
public class LabelOutputNodeDTOBuilder extends LabelNodeDTOBuilder {
    @Override
    public boolean supports(Integer nodeTypeCode) {
        return NodeType.LABEL_OUTPUT.equals(nodeTypeCode);
    }

    @Override
    public List<LabelNodeDTO> build(NodeDTO nodeDTO, NodeContext nodeContext) {
        LabelOutputProperties properties = JSONObject.parseObject(nodeDTO.getNodeProperties(), LabelOutputProperties.class);
        PreConditionCheck.checkArgument(!CollectionUtils.isEmpty(properties.getMainObject()), "标签打标对象为空");
        MainObject mainObject = properties.getMainObject().get(0);
        properties.setObjectNumberCol("`"+mainObject.getFromNode()+"."+mainObject.getEnName()+"`");
        properties.setObjectType(mainObject.getMainObjectTypeCode().intValue());
        if(!CollectionUtils.isEmpty(properties.getRelatedObject())){
            MainObject relatedObject = properties.getRelatedObject().get(0);
            if (StringUtils.isNotEmpty(relatedObject.getEnName())) {
                properties.setRelatedObjectNumberCol("`"+relatedObject.getFromNode()+"."+relatedObject.getEnName()+"`");
            }
        }
        // 保存上一个节点的输出字段，在标签结果中保存起来
        NodeDTO lastNode = nodeContext.getNodes().stream().filter(node -> node.getNodeMeta().getUuid().equals(nodeDTO.getLastNodeId())).findFirst().get();
        List<RowFieldVo> outputRow = lastNode.getNodeMeta().getOutputRowMeta().getValueMetaList().stream().map(valueMateBase -> RowFieldVo.of(valueMateBase.getCol(), valueMateBase.getCnName())).collect(Collectors.toList());
        return Collections.singletonList(new LabelNodeDTO(NodeType.LABEL_OUTPUT, JSONObject.toJSONString(properties), outputRow, nodeDTO.getLastNodeId()));
    }
} 