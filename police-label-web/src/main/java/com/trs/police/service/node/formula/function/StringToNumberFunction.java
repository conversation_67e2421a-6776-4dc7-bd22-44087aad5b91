package com.trs.police.service.node.formula.function;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.NodeFunction;

import java.util.List;

import static com.trs.police.constant.node.NodeConstants.NUMBER;

/**
 * 字符串转数字
 *
 * <AUTHOR>
 */
public class StringToNumberFunction implements NodeFunction {

    public static final StringToNumberFunction INSTANCE = new StringToNumberFunction();

    @Override
    public FieldValue execute(List<FieldValue> parameters) {
        return new FieldValue(parameters.get(0).getValue(), NUMBER);
    }

    @Override
    public String key() {
        return "string_to_number";
    }
}
