package com.trs.police.service.feature.domain.repository;

import com.trs.police.common.core.vo.CountVO;
import com.trs.police.service.feature.domain.entity.Feature;
import com.trs.police.service.feature.domain.value.FeatureCategoryCount;
import com.trs.police.service.feature.domain.value.FeatureCategoryStatisticDTO;
import com.trs.police.service.feature.domain.value.FeatureSearch;
import com.trs.police.service.shared.table.TableReference;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 * 特征查询
 */
public interface FeatureQueryRepository {

    /**
     * 查询特征列表
     *
     * @param search 查询参数
     * @return RestfulResultsV2
     */
    RestfulResultsV2<Feature> findFeatures(FeatureSearch search);

    /**
     * 根据ids查询
     *
     * @param ids ids
     * @return 特征列表
     */
    List<Feature> findByIds(List<Long> ids);

    /**
     * 根据id查询
     *
     * @param featureId id
     * @return 特征
     */
    Feature selectById(Long featureId);

    /**
     * 分类统计
     *
     * @param dto 参数
     * @return 分类统计
     */
    List<FeatureCategoryCount> categoryCount(FeatureCategoryStatisticDTO dto);

    /**
     * 表格使用统计
     *
     * @param tbs 表格
     * @return 表格数量统计
     */
    List<CountVO> tableUseStatistic(List<TableReference> tbs);
}
