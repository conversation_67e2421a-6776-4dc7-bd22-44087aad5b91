package com.trs.police.service.node;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.EmtpyExpression;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.attribute.IAttributesWrapper;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.common.utils.expression.parser.AbsConditionJsonParser;
import com.trs.common.utils.expression.parser.ConditionStructure;
import com.trs.police.common.core.entity.node.ControlValue;
import com.trs.police.common.core.entity.node.Value;
import com.trs.police.common.core.vo.node.ValueWrapper;
import com.trs.police.dto.node.NodeContext;
import io.vavr.control.Either;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.trs.common.base.PreConditionCheck.checkNotNull;
import static com.trs.common.utils.expression.ExpressionBuilder.Condition;
import static com.trs.common.utils.expression.ExpressionBuilder.Not;

/**
 * 条件解析器
 *
 * <AUTHOR>
 */
public abstract class AbstractConditionParser extends AbsConditionJsonParser<NodeContext> {


    public AbstractConditionParser(NodeContext nodeContext) {
        super(nodeContext);
    }

    @Override
    public Expression parseCondition(String jsonArray) {
        return super.parseCondition(jsonArray, this::buildNodeExpression);
    }

    @Override
    public IOperator getContainOperator(ConditionStructure conditionStructure) {
        return getOperatorMap().get(conditionStructure.getOperator());
    }

    @Override
    public ConditionStructure makeConditionStructure(String json) {
        JSONObject object = JSONObject.fromObject(json);
        ConditionStructure structure = new ConditionStructure();
        structure.setKey(object.optString("key"));
        ValueWrapper valueWrapper = JSON.parseObject(object.getString("value"), ValueWrapper.class);
        structure.setValue(Collections.singletonList(valueWrapper));
        structure.setOperator(object.optString("operator"));
        structure.setWeight(Float.parseFloat(StringUtils.showEmpty(object.optString("weight"), "0")));
        structure.setSlop(object.optInt("slop"));
        structure.setSimilarity(object.optInt("similarity"));
        structure.setAnalyzer(object.optString("analyzer"));
        return structure;
    }

    protected abstract Map<String, IOperator> getOperatorMap();

    protected abstract OperatorWrapper getOperatorWrapper(ConditionStructure conditionProperty);

    private Expression buildNodeExpression(ConditionStructure conditionProperty) {
        // 获取操作符号
        OperatorWrapper operator = getOperatorWrapper(conditionProperty);
        checkNotNull(operator.getOperator(), String.format("根据operator=%s未能查询出对应的条件！", conditionProperty.getOperator()));
        // 获取值
        ValueWrapper value = (ValueWrapper) conditionProperty.getValue().get(0);
        Either<Expression, List<Object>> valuesEither = getValues(value, conditionProperty);
        if(valuesEither.isLeft()){
            return valuesEither.getLeft();
        }
        Object[] valueArray = valuesEither.get().toArray(new Object[0]);
        List<IAttributesWrapper> attributesWrappers = attributesWrappers(operator.getOperator(), conditionProperty);
        Expression expression =  !CollectionUtils.isEmpty(attributesWrappers)
                ? Condition(conditionProperty.getKey(), operator.getOperator(), valueArray, attributesWrappers)
                : Condition(conditionProperty.getKey(), operator.getOperator(), valueArray);
        return Boolean.TRUE.equals(operator.getIsNot()) ? Not(expression) : expression;
    }

    /**
     * 通过value获取到值
     *
     *
     * @param values v <br>
     * @param conditionProperty cp
     * @return 值列表
     */
    private Either<Expression, List<Object>> getValues(ValueWrapper values, ConditionStructure conditionProperty) {
        if ("empty".equals(conditionProperty.getOperator()) || "notEmpty".equals(conditionProperty.getOperator())) {
            return Either.right(Arrays.asList(""));
        }
        // 如果是控制参数，并且没有填写值，返回空的条件
        Value v0 = values.getTargetValue(0);
        if (v0 instanceof ControlValue) {
            ValueWrapper controlValue = t.getControlValueMap().get(v0.getValueString());
            if (Objects.isNull(controlValue)) {
                return Either.left(new EmtpyExpression());
            }
        }
        // 过滤的字段
        List<Object> vs = IntStream.range(0, values.getValue().length)
                .mapToObj(vIndex -> {
                    Value v = values.getTargetValue(vIndex);
                    return v instanceof ControlValue ? t.getControlValueMap().get(v.getValueString()).getTargetValue(vIndex) : v;
                })
                .map(Value::getValueString)
                .collect(Collectors.toList());
        return vs.isEmpty() ? Either.left(new EmtpyExpression()) : Either.right(vs);
    }

    @Data
    @NoArgsConstructor
    protected static class OperatorWrapper {

        private IOperator operator;

        private Boolean isNot;

        public OperatorWrapper(IOperator operator) {
            this.operator = operator;
            this.isNot = Boolean.FALSE;
        }

        public OperatorWrapper(IOperator operator, Boolean isNot) {
            this.operator = operator;
            this.isNot = isNot;
        }
    }
}
