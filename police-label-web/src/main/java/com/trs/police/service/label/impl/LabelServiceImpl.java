package com.trs.police.service.label.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonParser;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.db.sdk.pojo.AggregationItems;
import com.trs.db.sdk.pojo.AggregationResult;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.utils.TreeUtils;
import com.trs.police.common.core.utils.VoParameterConstructor;
import com.trs.police.common.core.vo.CountVO;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.*;
import com.trs.police.dto.label.node.LabelProcessDTO;
import com.trs.police.dto.node.properties.FeatureInputProperties;
import com.trs.police.dto.node.properties.LabelOutputProperties;
import com.trs.police.entity.label.LabelComputeResultDO;
import com.trs.police.entity.label.LabelDO;
import com.trs.police.mapper.LabelComputeResultRepository;
import com.trs.police.mapper.LabelMapper;
import com.trs.police.mapper.LabelStatisticMapper;
import com.trs.police.mapper.UserMapper;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.label.LabelService;
import com.trs.police.service.label.assembler.LabelAddAssembler;
import com.trs.police.service.label.assembler.LabelProcessAssembler;
import com.trs.police.service.label.assembler.LabelSearchAssembler;
import com.trs.police.service.shared.dict.*;
import com.trs.police.utils.LabelUtils;
import com.trs.police.vo.label.LabelCategoryStatisticVO;
import com.trs.police.vo.label.LabelListVO;
import com.trs.police.vo.label.application.LabelResultVO;
import com.trs.police.vo.label.application.LabelVO;
import com.trs.police.vo.label.domain.CategoryCount;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.entity.PageInfo;
import com.trs.web.entity.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.And;
import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 *标签服务实现类
 */
@Slf4j
@Service
public class LabelServiceImpl implements LabelService {

    @Autowired
    private DictFacade dictFacade;

    @Autowired
    private DictTypeRegistry dictTypeRegistry;

    @Autowired
    private LabelStatisticMapper labelStatisticMapper;

    @Autowired
    private LabelMapper labelMapper;

    @Autowired
    private LabelAddAssembler labelAddAssembler;

    @Autowired
    private LabelSearchAssembler labelSearchAssembler;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private LabelProcessAssembler labelProcessAssembler;

    @Autowired
    private LabelComputeResultRepository labelComputeResultRepository;

    @Override
    public LabelVO addLabel(LabelAddDTO add) {
        // 插入数据库
        LabelDO labelDO = labelAddAssembler.toLabelDO(add);
        int insert = labelMapper.insert(labelDO);
        // 初始化流程节点
        String s = labelProcessAssembler.buildDefaultProcess(labelDO);
        labelDO.setProcessOrder(s);
        labelMapper.updateById(labelDO);
        // 返回标签信息
        return labelSearchAssembler.toLabelVO(Arrays.asList(labelDO)).get(0);
    }

    @Override
    public LabelVO detail(Long labelId) {
        LabelDO labelDO = labelMapper.selectById(labelId);
        return labelSearchAssembler.toLabelVO(Arrays.asList(labelDO)).get(0);
    }

    @Override
    public List<LabelCategoryStatisticVO> labelCategoryStatistic(LabelCategoryStatisticDTO dto) {
        List<DictItemDTO> ds = dictFacade.getDicts(dictTypeRegistry.getLabelCategory());
        // 数量统计
        List<CategoryCount> categoryCounts = labelStatisticMapper.categoryCount(dto);
        // 构造vo
        List<LabelCategoryStatisticVO> vos = ds.stream()
                .map(dict -> {
                    LabelCategoryStatisticVO vo = new LabelCategoryStatisticVO();
                    BeanUtils.copyProperties(dict, vo);
                    vo.setId(dict.getId().intValue());
                    vo.setCount(0);
                    return vo;
                })
                .collect(Collectors.toList());
        // 统计数量
        VoParameterConstructor
                .of(vos)
                .singleValueMatcher(vs -> categoryCounts, (vo, cc) -> vo.getCode().equals(cc.getCategoryCode()))
                .consumer((vo, cc) -> vo.setCount(cc.getCount()))
                .build();
        // 构造树
        List<LabelCategoryStatisticVO> build = TreeUtils.treeBuilder(vos)
                .build();
        // 设置层级
        setLevel(build, 1);
        // 虚拟一个根节点
        LabelCategoryStatisticVO root = buildRoot(build);
        // 重新计数，加上子节点数量
        addChildCount(root);
        return Arrays.asList(root);
    }

    @Override
    public DictItemDTO addCategory(LabelDictAddDTO add) {
        SharedDictAddDTO dto = new SharedDictAddDTO();
        BeanUtils.copyProperties(add, dto);
        dto.setType(dictTypeRegistry.getLabelCategory());
        return dictFacade.addDict(dto);
    }

    @Override
    public void deleteCategory(Long code) {
        DictItemDTO dictItemDTO = checkDeleteAble(code);
        dictFacade.deleteDict(dictItemDTO.getId());
    }

    @Override
    public void saveNode(LabelProcessDTO processDTO) {
        LabelDO labelDO = labelMapper.selectById(processDTO.getInfo().getLabelId());
        // 保存流程信息
        String labelProcess = labelProcessAssembler.toLabelProcess(processDTO);
        if (Boolean.TRUE.equals(processDTO.getManual())) {
            labelDO.setProcessOrder(labelProcess);
            labelDO.setProcessOrderSnapshot(null);
        } else {
            labelDO.setProcessOrderSnapshot(labelProcess);
        }
        // 修改基本信息
        LabelOutputDTO output = labelProcessAssembler.labelOutputDTO(processDTO);
        if (Objects.nonNull(output.getLabelId()) && Boolean.TRUE.equals(processDTO.getManual())) {
            labelDO.setMainObject(JSON.toJSONString(output.getMainObject()));
            labelDO.setRelatedObject(JSON.toJSONString(output.getRelatedObject()));
            labelDO.setLabelType(output.getLabelType());
            labelDO.setCycleTimeType(output.getCycleTimeType());
            labelDO.setCycleTime(output.getCycleTime());
            labelDO.setEffectiveTimeType(output.getEffectiveTimeType());
            labelDO.setEffectiveTime(output.getEffectiveTime());
            labelDO.setLabelName(output.getName());
            labelDO.setEnName(output.getEnName());
            labelDO.setPoliceKind(output.getPoliceKind());
            labelDO.setBusinessRule(output.getBusinessRule());
            labelDO.setCategoryCode(output.getCategoryCode());
            labelDO.setColor(output.getColor());
        }
        // 找到使用的特征id列表
        List<Long> collect = processDTO.getNodes()
                .stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.FEATURE_IN))
                .map(node -> {
                    FeatureInputProperties properties = JSON.parseObject(node.getNodeProperties(), FeatureInputProperties.class);
                    return properties.getFeatureId();
                })
                .distinct()
                .collect(Collectors.toList());
        labelDO.setFeatureId(JSON.toJSONString(collect));
        labelMapper.updateById(labelDO);
        if (StringUtils.isEmpty(labelDO.getProcessOrderSnapshot())) {
            labelMapper.update(
                    null,
                    Wrappers.lambdaUpdate(LabelDO.class)
                            .eq(LabelDO::getId, labelDO.getId())
                            .set(LabelDO::getProcessOrderSnapshot, null)
            );
        }
    }

    private DictItemDTO checkDeleteAble(Long code) {
        DictItemDTO dict = dictFacade.getDict(new DictReference(code, dictTypeRegistry.getLabelCategory()));
        Long count = labelMapper.selectCount(
                Wrappers.lambdaQuery(LabelDO.class)
                        .eq(LabelDO::getCategoryCode, dict.getCode())
                        .eq(LabelDO::getDeleted, 0)
        );
        PreConditionCheck.checkArgument(count == 0, "该分类下有标签，请先删除标签");
        return dict;
    }

    private void setLevel(List<LabelCategoryStatisticVO> vos, Integer level) {
        for (LabelCategoryStatisticVO vo : vos) {
            vo.setLevel(level);
            if (vo.getChildren() != null) {
                setLevel(vo.getChildren(), level + 1);
            }
        }
    }

    private void addChildCount(LabelCategoryStatisticVO vo) {
        if (vo.getChildren() != null) {
            for (LabelCategoryStatisticVO child : vo.getChildren()) {
                addChildCount(child);
            }
            vo.setCount(vo.getChildren().stream().mapToInt(LabelCategoryStatisticVO::getCount).sum() + vo.getCount());
        }
    }

    private LabelCategoryStatisticVO buildRoot(List<LabelCategoryStatisticVO> vos) {
        LabelCategoryStatisticVO vo = new LabelCategoryStatisticVO();
        vo.setId(null);
        vo.setPid(null);
        vo.setName("全部");
        vo.setLevel(0);
        vo.setChildren(vos);
        vo.setCount(0);
        return vo;
    }

    @Override
    public String toggleLabelStatus(Long labelId, Integer status) {
        if (labelId == null) {
            throw new IllegalArgumentException("标签ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw new IllegalArgumentException("状态参数无效，必须为0或1");
        }

        LabelDO label = labelMapper.selectById(labelId);
        if (label == null || label.getDeleted() == 1) {
            throw new IllegalArgumentException("标签不存在");
        }

        label.setStatus(status);
        int result = labelMapper.updateById(label);
        return "操作成功";
    }

    @Override
    public String deleteLabel(Long labelId) {
        if (labelId == null) {
            throw new IllegalArgumentException("标签ID不能为空");
        }

        LabelDO label = labelMapper.selectById(labelId);
        if (label == null || label.getDeleted() == 1) {
            throw new IllegalArgumentException("标签不存在");
        }

        // 逻辑删除，将deleted字段设置为1
        label.setDeleted(1);
        int result = labelMapper.updateById(label);
        return "删除成功";
    }

    @Override
    public RestfulResultsV2<LabelListVO> page(LabelListDto dto) {
        // 重新修码表参数，因为要求子分类也被查询出来
        LabelUtils.initListDto(dto,dictFacade, dictTypeRegistry);
        // 创建分页对象
        Page<LabelDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        // 使用XML中定义的分页查询方法
        Page<LabelDO> labelPage = labelMapper.findLabelPage(page, dto);
        // 转换为VO
        List<LabelVO> labelVO = labelSearchAssembler.toLabelVO(labelPage.getRecords());
        List<LabelListVO> voList = labelVO.stream()
                .map(vo -> {
                    LabelListVO labelListVO = new LabelListVO();
                    BeanUtils.copyProperties(vo, labelListVO);
                    return labelListVO;
                })
                .collect(Collectors.toList());

        // 返回带分页信息的结果
        RestfulResultsV2<LabelListVO> ok = RestfulResultsV2.ok(voList);
        ok
            .addTotalCount(labelPage.getTotal())
            .addPageNum(dto.getPageNum())
            .addPageSize(dto.getPageSize());
        return ok;
    }

    @Override
    public Long copy(Long labelId) {
        // 更新标签
        LabelDO labelDO = labelMapper.selectById(labelId);
        labelDO.setId(null);
        labelDO.setLabelName(labelDO.getLabelName() + "_copy");
        labelDO.setEnName(labelDO.getEnName() + "_copy");
        // 保存标签
        labelMapper.insert(labelDO);
        // 更新流程
        LabelProcessDTO processDTO = labelProcessAssembler.toLabelProcessDTO(labelDO.getProcessOrder());
        processDTO.getInfo().setLabelId(labelDO.getId());
        processDTO.getNodes().stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.LABEL_OUTPUT))
                .findAny()
                .ifPresent(node ->{
                    node.getNodeMeta().setName(labelDO.getLabelName());
                    LabelOutputProperties properties = JSON.parseObject(node.getNodeProperties(), LabelOutputProperties.class);
                    properties.setName(labelDO.getLabelName());
                    properties.setEnName(labelDO.getEnName());
                    properties.setLabelId(labelDO.getId());
                    node.setNodeProperties(JSON.toJSONString(properties));
                });
        labelDO.setProcessOrder(JSON.toJSONString(processDTO));
        labelMapper.updateById(labelDO);
        return labelDO.getId();
    }

    @Override
    public List<CountVO> featureUseStatistic(List<Long> featureIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(featureIds)) {
            return new ArrayList<>();
        }
        List<CountVO> vs = labelStatisticMapper.featureUseStatistic(featureIds);
        return vs;
    }

    @Override
    public NodeData previewNode(Long labelId) {
        // 查询最新批次的数据
        AggregationItems.buildAggsKey(Operator.Max, "update_time");
        AggregationItems aggregationItems = new AggregationItems();
        aggregationItems.setGroupColumn("label_name");
        aggregationItems.addItem(Operator.Max, "update_time");
        // 构建基础条件
        LabelDO labelDO = labelMapper.selectById(labelId);
        Expression expression = ExpressionBuilder.And(
                Condition("label_name", Operator.Equal, labelDO.getLabelName()),
                Condition("police_kind", Operator.Equal, labelDO.getPoliceKind()));
        AggregationResult aggResult = labelComputeResultRepository.aggregate(expression, aggregationItems);
        Number number = aggResult.getAggregationMap().get("MAX(update_time)");
        if(number == null){
            return null;
        }
        Date date = new Date(number.longValue());
        String lastUdpateTime;
        // 是否转换时区的配置
        Boolean convertTimezone = BeanFactoryHolder.getEnv().getProperty("com.trs.label.convert.timezone", Boolean.class, true);
        if(convertTimezone){
            lastUdpateTime = TimeUtils.dateToString(TimeUtils.befOrAft(date, -8, Calendar.HOUR), TimeUtils.YYYYMMDD_HHMMSS);
        }else {
            lastUdpateTime = TimeUtils.dateToString(date, TimeUtils.YYYYMMDD_HHMMSS);
        }
        expression = ExpressionBuilder.And(expression, ExpressionBuilder.Condition("update_time", Operator.Equal, lastUdpateTime));
        PageList<LabelComputeResultDO> pageList = labelComputeResultRepository.findPageList(expression, PageInfo.newPage(1, 2000));
        if(pageList.getContents().isEmpty()){
            return null;
        }
        // 表头
        Boolean hasRealtedObject = StringUtils.isNotEmpty(labelDO.getRelatedObject())
                && !"[]".equals(labelDO.getRelatedObject())
                && !"null".equals(labelDO.getRelatedObject());
        List<FieldInfoVO> header = labelComputeResultHead(hasRealtedObject);
        // 数据
        List<List<FieldValue>> data = pageList.getContents().stream().map(d->labelComputeResultValue(d, hasRealtedObject)).collect(Collectors.toList());
        NodeData nodeData = new NodeData();
        nodeData.setHeader(header);
        nodeData.setData(data);
        nodeData.setTotalCount(pageList.getTotal());

        return nodeData;
    }

    @Override
    public RestfulResultsV2<LabelResultVO> queryLabelResult(LabelResultQueryDTO dto, PageParams pageParams, SearchParams searchParams) {
        // 查询列表
        if (CollectionUtils.isNotEmpty(dto.getObjectNumber())) {
            Integer limit = BeanFactoryHolder.getEnv().getProperty("label.web.query.count", Integer.class, 200);
            if (dto.getObjectNumber().size() > limit) {
                throw new TRSException("查询数量超出限制！");
            }
        }
        Expression expression = And(
                Condition(CollectionUtils.isNotEmpty(dto.getObjectNumber()), "object_number", Operator.In, dto.getObjectNumber()),
                Condition(StringUtils.isNotEmpty(dto.getLabelName()), "label_name", Operator.Equal, dto.getLabelName())
        );
        PageList<LabelComputeResultDO> pageList = labelComputeResultRepository.findPageList(expression, PageInfo.newPage(pageParams.getPageNumber(), pageParams.getPageSize()));
        // 构造成返回结果
        List<LabelResultVO> vos = labelSearchAssembler.buildResult(pageList.getContents());
        RestfulResultsV2<LabelResultVO> ok = RestfulResultsV2.ok(vos);
        ok.addTotalCount(pageList.getTotal());
        return ok;
    }

    private List<FieldInfoVO> labelComputeResultHead = Arrays.asList(
            new FieldInfoVO("主体证号", "objectNumber", DataBaseFieldMappingType.STRING.getTypeName(), null),
            new FieldInfoVO("额外信息", "extraInfo", DataBaseFieldMappingType.STRING.getTypeName(), null),
            new FieldInfoVO("创建时间", "createTime", DataBaseFieldMappingType.DATETIME.getTypeName(), null),
            new FieldInfoVO("更新时间", "updateTime", DataBaseFieldMappingType.DATETIME.getTypeName(), null),
            new FieldInfoVO("过期时间", "expireTime", DataBaseFieldMappingType.DATETIME.getTypeName(), null),
            new FieldInfoVO("数据源表", "sourceTable", DataBaseFieldMappingType.STRING.getTypeName(), null),
            new FieldInfoVO("数据源ID", "sourceDataIds", DataBaseFieldMappingType.STRING.getTypeName(), null)
    );

    private List<FieldInfoVO> labelComputeResultHead(Boolean hasRealtedObject){
        if(hasRealtedObject){
            labelComputeResultHead = Arrays.asList(
                    new FieldInfoVO("主体证号", "objectNumber", DataBaseFieldMappingType.STRING.getTypeName(), null),
                    new FieldInfoVO("客体证号", "relatedObjectNumber", DataBaseFieldMappingType.STRING.getTypeName(), null),
                    new FieldInfoVO("额外信息", "extraInfo", DataBaseFieldMappingType.STRING.getTypeName(), null),
                    new FieldInfoVO("创建时间", "createTime", DataBaseFieldMappingType.DATETIME.getTypeName(), null),
                    new FieldInfoVO("更新时间", "updateTime", DataBaseFieldMappingType.DATETIME.getTypeName(), null),
                    new FieldInfoVO("过期时间", "expireTime", DataBaseFieldMappingType.DATETIME.getTypeName(), null),
                    new FieldInfoVO("数据源表", "sourceTable", DataBaseFieldMappingType.STRING.getTypeName(), null),
                    new FieldInfoVO("数据源ID", "sourceDataIds", DataBaseFieldMappingType.STRING.getTypeName(), null));
        }
        return labelComputeResultHead;
    }

    private List<FieldValue> labelComputeResultValue(LabelComputeResultDO item, Boolean hasRealtedObject){
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        if (hasRealtedObject){
            return Arrays.asList(
                    new FieldValue(item.getObjectNumber()==null?"": item.getObjectNumber().toString(), labelComputeResultHead.get(0)),
                    new FieldValue(item.getRelatedObjectNumber()==null?"": item.getRelatedObjectNumber().toString(), labelComputeResultHead.get(1)),
                    new FieldValue(StringUtils.isEmpty(item.getExtraInfo())?"":gson.toJson(JsonParser.parseString(item.getExtraInfo())), labelComputeResultHead.get(2)),
                    new FieldValue(item.getCreateTime(), labelComputeResultHead.get(3)),
                    new FieldValue(item.getUpdateTime(), labelComputeResultHead.get(4)),
                    new FieldValue(item.getExpireTime(), labelComputeResultHead.get(5)),
                    new FieldValue(item.getSourceTable(), labelComputeResultHead.get(6)),
                    new FieldValue(item.getSourceDataIds(), labelComputeResultHead.get(7))
            );
        }
        return Arrays.asList(
                new FieldValue(item.getObjectNumber()==null?"": item.getObjectNumber().toString(), labelComputeResultHead.get(0)),
                new FieldValue(StringUtils.isEmpty(item.getExtraInfo())?"":gson.toJson(JsonParser.parseString(item.getExtraInfo())), labelComputeResultHead.get(1)),
                new FieldValue(item.getCreateTime(), labelComputeResultHead.get(2)),
                new FieldValue(item.getUpdateTime(), labelComputeResultHead.get(3)),
                new FieldValue(item.getExpireTime(), labelComputeResultHead.get(4)),
                new FieldValue(item.getSourceTable(), labelComputeResultHead.get(5)),
                new FieldValue(item.getSourceDataIds(), labelComputeResultHead.get(6))
        );
    }
}
