package com.trs.police.service.feature.application.service.impl;

import com.trs.common.utils.StringUtils;
import com.trs.police.mapper.DataTableMapper;
import com.trs.police.service.feature.application.DTO.command.FeatureAddDTO;
import com.trs.police.service.feature.domain.value.FeatureBaseInfo;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureSearchDTO;
import com.trs.police.service.feature.application.DTO.vo.FeatureDetailVO;
import com.trs.police.service.feature.application.assembler.FeatureAddAssembler;
import com.trs.police.service.feature.application.assembler.FeatureNodeAssembler;
import com.trs.police.service.feature.application.assembler.FeatureSearchAssembler;
import com.trs.police.service.feature.domain.entity.Feature;
import com.trs.police.service.feature.domain.repository.FeatureQueryRepository;
import com.trs.police.service.feature.domain.repository.FeatureRepository;
import com.trs.police.service.feature.domain.value.FeatureSearch;
import com.trs.police.service.feature.application.service.FeatureService;
import com.trs.police.service.feature.application.DTO.vo.FeatureVO;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 特征服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class FeatureServiceImpl implements FeatureService {

    @Autowired
    private FeatureQueryRepository featureQueryRepository;

    @Autowired
    private FeatureRepository featureRepository;

    @Autowired
    private FeatureSearchAssembler featureSearchAssembler;

    @Autowired
    private FeatureAddAssembler featureAddAssembler;

    @Autowired
    private FeatureNodeAssembler featureNodeAssembler;

    @Autowired
    private DataTableMapper dataTableMapper;

    @Override
    public RestfulResultsV2<FeatureVO> add(FeatureAddDTO dto) {
        Feature feature = featureAddAssembler.toFeature(dto);
        Feature save = featureRepository.add(feature);
        if(StringUtils.isNotEmpty(dto.getIdField())){
            dataTableMapper.updateIdField(dto.getTableId(), dto.getIdField());
        }
        return RestfulResultsV2.ok(featureSearchAssembler.toVO(Arrays.asList(save)).get(0));
    }

    @Override
    public RestfulResultsV2<FeatureVO> findFeatures(FeatureSearchDTO searchDTO) {
        FeatureSearch domain = featureSearchAssembler.toDomain(searchDTO);
        RestfulResultsV2<Feature> features = featureQueryRepository.findFeatures(domain);
        List<FeatureVO> vo = featureSearchAssembler.toVO(features.getDatas());
        RestfulResultsV2<FeatureVO> ok = RestfulResultsV2.ok(vo);
        ok.addTotalCount(features.getSummary().getTotal());
        return ok;
    }

    @Override
    public RestfulResultsV2<FeatureVO> findFeaturesByIds(List<Long> ids) {
        List<Feature> byIds = featureQueryRepository.findByIds(ids);
        return RestfulResultsV2.ok(featureSearchAssembler.toVO(byIds));
    }

    @Override
    @Transactional
    public boolean deleteFeature(Long featureId) {
        Feature feature = featureQueryRepository.selectById(featureId);
        return featureRepository.delete(feature);
    }

    @Override
    public boolean toggleFeatureStatus(Long id, Integer status) {
        Feature feature = featureQueryRepository.selectById(id);
        feature.toggleStatus(status);
        featureRepository.update(feature);
        return true;
    }

    @Override
    @Transactional
    public FeatureVO updateLabelCount(Long featureId, Integer labelCount) {
        Feature feature = featureQueryRepository.selectById(featureId);
        feature.updateLabelCount(labelCount);
        Feature update = featureRepository.update(feature);
        return featureSearchAssembler.toVO(Arrays.asList(update)).get(0);
    }

    @Override
    public void saveNode(FeatureProcessDTO processDTO) {
        Feature feature = featureQueryRepository.selectById(processDTO.getInfo().getFeatureId());
        String string = featureNodeAssembler.toString(processDTO);
        feature.updateProcess(string, processDTO.getManual());
        FeatureBaseInfo featureBaseInfo = featureNodeAssembler.toFeatureBaseInfo(processDTO);
        feature.updateByProcess(featureBaseInfo);
        featureRepository.update(feature);
    }

    @Override
    public FeatureDetailVO detail(Long featureId) {
        Feature feature = featureQueryRepository.selectById(featureId);
        return featureSearchAssembler.toDetailVO(Arrays.asList(feature)).get(0);
    }

    @Override
    public Long copy(Long featureId) {
        // 复制
        Feature feature = featureQueryRepository.selectById(featureId);
        Feature copy = featureAddAssembler.copy(feature);
        // 保存
        Feature save = featureRepository.add(copy);
        // 更新流程信息
        FeatureProcessDTO processDTO = featureNodeAssembler.toDTO(save.getProcessOrder());
        featureNodeAssembler.updateIdAndName(processDTO, save.getFeatureId(), save.getFeatureName());
        save.setProcessOrder(featureNodeAssembler.toString(processDTO));
        featureRepository.update(save);
        // 返回
        return save.getFeatureId();
    }
}
