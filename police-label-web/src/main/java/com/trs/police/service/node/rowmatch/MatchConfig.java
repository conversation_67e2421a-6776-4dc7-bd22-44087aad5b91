package com.trs.police.service.node.rowmatch;

import com.trs.police.dto.node.ValueMateBase;
import lombok.Data;

/**
 * Match字段配置项
 */
@Data
public class MatchConfig {

    /**
     * 表A字段信息
     */
    private ValueMateBase a;

    /**
     * 表B字段信息
     */
    private ValueMateBase b;

    /**
     * 输出字段信息
     */
    private ValueMateBase out;

    /**
     * 比较类型 等于 eq 不等于 neq 大于 gt 大于等于 gte 小于 le 小于等于 lte
     */
    private String matchType;
}
