package com.trs.police.service.feature.application.DTO.vo;

import com.trs.police.common.core.vo.Tree;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * 标签分类统计
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FeatureCategoryStatisticVO implements Tree {

    /**
     * id
     */
    private Integer id;
    /**
     * 父节点id
     */
    private Long pid;
    /**
     * code
     */
    private Long code;
    /**
     * p_code
     */
    private Long pCode;
    /**
     * 名称
     */
    private String name;
    /**
     * 顺序
     */
    private Integer showNumber;

    /**
     * 码表描述
     */
    private String dictDesc;

    /**
     * 背景色
     */
    private String color;

    /**
     * type
     */
    private String type;

    /**
     * 子节点
     */
    private List<FeatureCategoryStatisticVO> children;

    /**
     * 其它标志
     */
    private String flag;


    private String pName;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 数量
     */
    private Integer count;

    @Override
    public Long getParentId() {
        return pid;
    }

    @Override
    public String key() {
        return "";
    }

    @Override
    public String getTreeLevel() {
        return String.valueOf(level);
    }

    @Override
    public Optional<Integer> sort() {
        return Optional.ofNullable(showNumber);
    }

    @Override
    public void setChildren(List<? extends Tree> children) {
        this.children = (List<FeatureCategoryStatisticVO>) children;
    }
}
