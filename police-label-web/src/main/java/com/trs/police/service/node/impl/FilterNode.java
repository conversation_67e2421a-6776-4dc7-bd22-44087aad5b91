package com.trs.police.service.node.impl;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.entity.node.GeoValue;
import com.trs.police.common.core.entity.node.comparable.ComparableValue;
import com.trs.police.common.core.entity.node.ControlValue;
import com.trs.police.common.core.entity.node.TableFieldValue;
import com.trs.police.common.core.entity.node.Value;
import com.trs.police.common.core.entity.node.comparable.NumberValue;
import com.trs.police.common.core.entity.node.comparable.StringValue;
import com.trs.police.common.core.utils.GeoUtils;
import com.trs.police.constant.GeoEnum;
import com.trs.police.dto.node.properties.FilterNodeProperties;
import com.trs.police.common.core.vo.node.*;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.Node;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


import com.alibaba.fastjson.JSON;
import com.trs.police.service.node.ValueComparator;
import com.trs.police.utils.GeoPointParser;
import com.trs.police.common.core.vo.Geometries;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;


/**
 * 过滤节点
 *
 * <AUTHOR>
 */
@Slf4j
public class FilterNode extends Node {

    public FilterNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    public NodeData process(List<NodeData> inputNode, NodeContext context) {
        FilterNodeProperties property = getPropertyAs(FilterNodeProperties.class);
        List<List<FieldValue>> resultData = inputNode.get(0)
                .getData()
                .stream()
                .filter(data -> isSatisfy(data, property, context))
                .collect(Collectors.toList());
        NodeData result = new NodeData();
        result.setTotalCount(BigInteger.valueOf(resultData.size()).longValue());
        result.setHeader(inputNode.get(0).getHeader());
        result.setData(resultData);
        result.setNodeMeta(nodeMeta);
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.FILTER;
    }

    private Boolean isSatisfy(List<FieldValue> rowData, FilterNodeProperties filterNodeProperties, NodeContext context) {
        if (filterNodeProperties.getTokens() == null || filterNodeProperties.getTokens().length == 0) {
            return true;
        }
        Stack<Boolean> resultStack = new Stack<>();
        Stack<String> operatorStack = new Stack<>();
        // 解析token
        for (String token : filterNodeProperties.getTokens()) {
            if ("(".equals(token)) {
                operatorStart(resultStack, operatorStack, token);
            } else if (")".equals(token)) {
                operatorEnd(resultStack, operatorStack, token);
            } else if ("且".equals(token) || "或".equals(token) || "非".equals(token)) {
                logic(resultStack, operatorStack, token);
            } else {
                condition(rowData, context, resultStack, operatorStack, token);
            }
        }
        while (!operatorStack.isEmpty()) {
            evaluateOperation(resultStack, operatorStack.pop());
        }
        if (resultStack.size() > 1) {
            return resultStack.stream().allMatch(Boolean::booleanValue);
        }
        return resultStack.isEmpty() ? true : resultStack.pop();
    }

    private void operatorStart(Stack<Boolean> resultStack, Stack<String> operatorStack, String token) {
        operatorStack.push(token);
    }

    private void operatorEnd(Stack<Boolean> resultStack, Stack<String> operatorStack, String token) {
        while (!operatorStack.isEmpty() && !"(".equals(operatorStack.peek())) {
            evaluateOperation(resultStack, operatorStack.pop());
        }
        if (!operatorStack.isEmpty() && "(".equals(operatorStack.peek())) {
            operatorStack.pop();
        }
    }

    private void logic(Stack<Boolean> resultStack, Stack<String> operatorStack, String token) {
        while (!operatorStack.isEmpty() && !"(".equals(operatorStack.peek())
                && !"非".equals(operatorStack.peek())) { // 非操作优先级最高
            evaluateOperation(resultStack, operatorStack.pop());
        }
        operatorStack.push(token);
    }

    private void condition(List<FieldValue> rowData, NodeContext context, Stack<Boolean> resultStack, Stack<String> operatorStack, String token) {

        FilterNodeProperties.Condition condition = JSON.parseObject(token, FilterNodeProperties.Condition.class);
        String operator = condition.getOperator();
        ValueWrapper values = condition.getValue();

        // 找到表中对应字段的值
        Optional<FieldValue> fieldValue = rowData.stream()
                .filter(f -> f.getId().equals(condition.getId()))
                .findFirst();
        if (fieldValue.isEmpty()) {
            throw new RuntimeException(String.format("节点：%s未能匹配到对应字段: %s", nodeMeta.getName(), condition.getId()));
        }

        boolean conditionResult = fieldValue.map(dbValue -> {
            // 如果是控制参数，并且没有填写值，返回true
            Value v0 = values.getTargetValue(0);
            if (v0 instanceof ControlValue) {
                ValueWrapper controlValue = context.getControlValueMap().get(v0.getValueString());
                if (Objects.isNull(controlValue)) {
                    return true;
                }
            }
            // 表中字段
            String value = dbValue.getValue();
            // 过滤的字段
            List<String> vs = IntStream.range(0, values.getValue().length)
                    .mapToObj(vIndex -> {
                        Value v = values.getTargetValue(vIndex);
                        return v instanceof ControlValue ? context.getControlValueMap().get(v.getValueString()).getTargetValue(vIndex) : v;
                    })
                    .map(Value::getValueString)
                    .collect(Collectors.toList());
            switch (operator) {
                case "in":
                    if (DataBaseFieldMappingType.GEOMETRY.getFieldType().equals(dbValue.getTypeCode()) || v0 instanceof GeoValue) {
                        return vs.stream().anyMatch(v -> geoContains(value, v));
                    }
                    return vs.contains(value);
                case "notIn":
                    if (DataBaseFieldMappingType.GEOMETRY.getFieldType().equals(dbValue.getTypeCode()) || v0 instanceof GeoValue) {
                        return vs.stream().noneMatch(v -> geoContains(value, v));
                    }
                    return !vs.contains(value);
                case "like":
                    return value.contains(vs.get(0));
                case "notLike":
                    return !value.contains(vs.get(0));
                case "notNull":
                case "notEmpty":
                    return StringUtils.isNotEmpty(value);
                case "isNull":
                    return StringUtils.isEmpty(value);
                case "empty":
                    return StringUtils.isEmpty(value);
                case "regularMatch":
                    return value.matches(vs.get(0));
                case "regularNotMatch":
                    return !value.matches(vs.get(0));
                case "wkt":
                    return vs.stream().anyMatch(v -> geoContains(value, v));
                case "startWith":
                    return value.startsWith(vs.get(0));
                case "endWith":
                    return value.endsWith(vs.get(0));
                case "notStartWith":
                    return !value.startsWith(vs.get(0));
                case "notEndWith":
                    return !value.endsWith(vs.get(0));
                default:
                    return isSatisfyValue(operator, dbValue, v0, rowData, context);
            }
        }).orElse(false);
        resultStack.push(conditionResult);
    }

    private void evaluateOperation(Stack<Boolean> resultStack, String operator) {
        if ("非".equals(operator)) {
            if (resultStack.isEmpty()) {
                return;
            }
            boolean value = resultStack.pop();
            resultStack.push(!value);
        } else {
            if (resultStack.size() < 2) {
                return;
            }
            boolean right = resultStack.pop();
            boolean left = resultStack.pop();

            if ("且".equals(operator)) {
                resultStack.push(left && right);
            } else if ("或".equals(operator)) {
                resultStack.push(left || right);
            }
        }
    }

    /**
     * 比较两个值
     *
     * @param op 比较类型
     * @param v1 数据库字段
     * @param v2 输入字段
     * @param rowData 单行数据
     * @param context 上下文
     * @return 比较结果
     */
    private Boolean isSatisfyValue(String op, FieldValue v1, Value v2, List<FieldValue> rowData, NodeContext context) {
        Integer compareResult = null;
        if (v2 instanceof ComparableValue) {
            // 可比较的字段
            compareResult =  compareValues(v1, (ComparableValue) v2);
        } else if (v2 instanceof TableFieldValue) {
            // 表格字段
            TableFieldValue t = (TableFieldValue) v2;
            FieldValue fileValue = rowData.stream()
                    .filter(f -> f.getId().equals(new FieldInfoVO(t.getFiledName(), null, null, t.getFromNode()).getId()))
                    .findAny()
                    .get();
            compareResult = ValueComparator.compareValues(v1, fileValue);
        }
        if (compareResult == null) {
            return false;
        }
        // 根据操作符号决定是否满足条件
        switch (op) {
            case "eq":
                return compareResult == 0;
            case "ne":
                return compareResult != 0;
            case "gt":
                return compareResult > 0;
            case "lt":
                return compareResult < 0;
            case "ge":
                return compareResult >= 0;
            case "le":
                return compareResult <= 0;
            default:
                return false;
        }
    }

    private int compareValues(FieldValue value1, ComparableValue value) {
        if (Objects.isNull(value1.getValue())) {
            return -1;
        }
        if (DataBaseFieldMappingType.NUMBER.getFieldType().equals(value1.getTypeCode()) && value instanceof StringValue) {
            return -new NumberValue(value.getValueString()).compareTo(value1.getValue());
        }
        return -value.compareTo(value1.getValue());
    }

    private Boolean geoContains(String input, String polygon) {
        if (polygon.trim().startsWith("POLYGON")) {
            return geoPolygonContains(input, polygon);
        }
        if (polygon.trim().startsWith("{")) {
            Geometries geometries = JSON.parseObject(polygon, Geometries.class);
            GeoEnum geoEnum = GeoEnum.fromCodeOrThrow(geometries.getType());
            switch (geoEnum) {
                case POLYGON:
                    return geoPolygonContains(input, geometries.getGeometry());
                case CIRCLE:
                    return geoPolygonContains(input, GeoUtils.convertCircleToPolygon(geometries));
                default:
                    throw new RuntimeException("暂不支持的geo类型");
            }
        }
        throw new RuntimeException("暂不支持的geo类型");
    }

    private Boolean geoPolygonContains(String input, String polygon) {
        try {
            if (StringUtils.isEmpty(input) || StringUtils.isEmpty(polygon)) {
                return false;
            }
            WKTReader reader = new WKTReader();
            Geometry read = reader.read(GeoPointParser.parseToWkt(input));
            return reader.read(polygon).contains(read);
        } catch (Exception e) {
            log.error("判断点是否在多边形内失败！点：{} 多边形：{}", input, polygon, e);
            return false;
        }
    }

}
