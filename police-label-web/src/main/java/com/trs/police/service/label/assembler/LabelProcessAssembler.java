package com.trs.police.service.label.assembler;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.dto.Position;
import com.trs.police.constant.NodeType;
import com.trs.police.constant.label.TimeType;
import com.trs.police.dto.label.LabelMainObject;
import com.trs.police.dto.label.LabelOutputDTO;
import com.trs.police.dto.label.node.LabelBaseInfo;
import com.trs.police.dto.label.node.LabelProcessDTO;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.RowMeta;
import com.trs.police.dto.node.properties.LabelOutputProperties;
import com.trs.police.entity.label.LabelDO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 标签流程组装器
 *
 * <AUTHOR>
 */
@Component
public class LabelProcessAssembler {

    /**
     * 标签节点dto转字符串
     *
     * @param dto dto
     * @return 字符串
     */
    public String toLabelProcess(LabelProcessDTO dto) {
        // 修改properties名称
        Optional<NodeDTO> any = dto.getNodes()
                .stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.LABEL_OUTPUT))
                .findAny();
        if (any.isPresent()) {
            String nodeProperties = any.get().getNodeProperties();
            LabelOutputProperties properties = JSON.parseObject(nodeProperties, LabelOutputProperties.class);
            properties.setName(any.get().getNodeMeta().getName());
            any.get().setNodeProperties(JSON.toJSONString(properties));
        }
        // 转化成json字符串
        return JSON.toJSONString(dto);
    }

    /**
     * 获取到标签流程
     *
     * @param json json
     * @return 标签流程
     */
    public LabelProcessDTO toLabelProcessDTO(String json) {
        return JSON.parseObject(json, LabelProcessDTO.class);
    }

    /**
     * 构造默认的标签流程
     *
     * @param label 标签
     * @return 默认流程
     */
    public String buildDefaultProcess(LabelDO label) {
        LabelBaseInfo info = new LabelBaseInfo();
        info.setLabelId(label.getId());

        // 节点信息
        NodeMeta meta = new NodeMeta();
        meta.setName(label.getLabelName());
        meta.initUid();
        meta.setNodeTypeCode(NodeType.LABEL_OUTPUT);
        meta.setPosition(new Position("400", "0"));
        meta.setOutputRowMeta(new RowMeta(new ArrayList<>()));

        // 节点配置
        LabelOutputProperties outputProperties = new LabelOutputProperties();
        outputProperties.setLabelId(label.getId());
        outputProperties.setMainObject(new ArrayList<>());
        outputProperties.setRelatedObject(new ArrayList<>());
        outputProperties.setName(label.getLabelName());
        outputProperties.setEnName(label.getEnName());
        outputProperties.setPoliceKind(label.getPoliceKind());
        outputProperties.setCategoryCode(label.getCategoryCode());
        outputProperties.setBusinessRule(label.getBusinessRule());
        outputProperties.setColor(label.getColor());
        // 默认打标方式设置成自动一天
        outputProperties.setLabelType(1);
        outputProperties.setCycleTimeType(TimeType.DAY.getCode());
        outputProperties.setCycleTime(1);

        // 输出节点
        NodeDTO out = new NodeDTO();
        out.setNodeMeta(meta);
        out.setNodeProperties(JSON.toJSONString(outputProperties));

        // 默认的流程
        LabelProcessDTO dto = new LabelProcessDTO();
        dto.setInfo(info);
        dto.setNodes(Arrays.asList(out));
        dto.setControl(new ArrayList<>());
        dto.setNodeOrders(new ArrayList<>());

        return toLabelProcess(dto);
    }

    /**
     * 获取标签输出节点信息
     *
     * @param dto dto
     * @return 输出节点信息
     */
    public LabelOutputDTO labelOutputDTO(LabelProcessDTO dto) {
        Optional<NodeDTO> any = dto.getNodes()
                .stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.LABEL_OUTPUT))
                .findAny();
        if (any.isEmpty()) {
            return new LabelOutputDTO();
        }
        String nodeProperties = any.get().getNodeProperties();
        LabelOutputProperties properties = JSON.parseObject(nodeProperties, LabelOutputProperties.class);
        LabelOutputDTO labelOutputDTO = new LabelOutputDTO();
        labelOutputDTO.setLabelId(properties.getLabelId());
        labelOutputDTO.setLabelType(properties.getLabelType());
        labelOutputDTO.setCycleTimeType(properties.getCycleTimeType());
        labelOutputDTO.setCycleTime(properties.getCycleTime());
        labelOutputDTO.setEffectiveTimeType(properties.getEffectiveTimeType());
        labelOutputDTO.setEffectiveTime(properties.getEffectiveTime());
        labelOutputDTO.setName(any.get().getNodeMeta().getName());
        labelOutputDTO.setEnName(properties.getEnName());
        labelOutputDTO.setPoliceKind(properties.getPoliceKind());
        labelOutputDTO.setBusinessRule(properties.getBusinessRule());
        labelOutputDTO.setCategoryCode(properties.getCategoryCode());
        List<LabelMainObject> objectList = properties.getMainObject()
                .stream()
                .map(mainObject -> new LabelMainObject(mainObject.getEnName(), mainObject.getFromNode(), mainObject.getMainObjectTypeCode()))
                .collect(Collectors.toList());
        labelOutputDTO.setMainObject(objectList);
        if(properties.getRelatedObject() != null){
            List<LabelMainObject> relatedObjectList = properties.getRelatedObject()
                    .stream()
                    .map(mainObject -> new LabelMainObject(mainObject.getEnName(), mainObject.getFromNode(), mainObject.getMainObjectTypeCode()))
                    .collect(Collectors.toList());
            labelOutputDTO.setRelatedObject(relatedObjectList);
        }
        return labelOutputDTO;
    }
}
