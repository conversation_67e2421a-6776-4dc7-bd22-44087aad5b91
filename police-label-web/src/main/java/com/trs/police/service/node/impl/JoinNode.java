package com.trs.police.service.node.impl;

import com.trs.police.common.core.vo.node.*;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.RelevancyNodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;
import com.trs.police.service.node.rowmatch.MatchResult;
import com.trs.police.service.node.rowmatch.RowMatcher;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 交集
 *
 * <AUTHOR>
 */
@Slf4j
public class JoinNode extends Node {

    public JoinNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        RelevancyNodeProperties property = getPropertyAs(RelevancyNodeProperties.class);
        // 拿到a表b表
        Optional<NodeData> nodeA = findNode(inputNode, property.getTableA());
        Optional<NodeData> nodeB = findNode(inputNode, property.getTableB());
        if (nodeA.isEmpty() || nodeB.isEmpty()) {
            throw new RuntimeException("关联节点未找到");
        }

        List<MatchResult> match = RowMatcher.match(nodeA.get(), nodeB.get(), property.getMatch());

        // 生成新的数据
        List<FieldInfoVO> header = new ArrayList<>();
        header.addAll(nodeA.get().getHeader());
        header.addAll(nodeB.get().getHeader());

        List<Row> data = match.stream()
                .filter(result -> !result.getMatched().isEmpty())
                .map(result -> {
                    return result.getMatched()
                            .stream()
                            .map(rowB -> {
                                List<FieldValue> row = new ArrayList<>();
                                row.addAll(result.getFrom().getRowData());
                                row.addAll(rowB.getRowData());
                                return new Row(row);
                            })
                            .collect(Collectors.toList());
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 构造结果
        NodeData result = new NodeData();
        result.setNodeMeta(nodeMeta);
        result.setTotalCount(Integer.valueOf(data.size()).longValue());
        result.setHeader(header);
        result.setRowList(data);
        return result;
    }

    @Override
    public Integer nodeType() {
        throw new RuntimeException("交集节点仅限内部调用");
    }
}
