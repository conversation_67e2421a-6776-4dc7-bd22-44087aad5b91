package com.trs.police.service.node.formula.function;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.NodeFunction;

import java.util.List;

/**
 * 绝对值
 *
 * <AUTHOR>
 */
public class AbsNodeFunction implements NodeFunction {

    public static final AbsNodeFunction INSTANCE = new AbsNodeFunction();

    @Override
    public FieldValue execute(List<FieldValue> parameters) {
        Double value = Double.valueOf(parameters.get(0).getValue());
        return new FieldValue(String.valueOf(Math.abs(value)), parameters.get(0).getFieldInfo());
    }

    @Override
    public String key() {
        return "abs";
    }
}
