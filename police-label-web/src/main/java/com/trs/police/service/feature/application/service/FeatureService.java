package com.trs.police.service.feature.application.service;

import com.trs.police.service.feature.application.DTO.command.FeatureAddDTO;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureSearchDTO;
import com.trs.police.service.feature.application.DTO.vo.FeatureDetailVO;
import com.trs.police.service.feature.application.DTO.vo.FeatureVO;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 * 特征服务接口
 *
 * <AUTHOR>
 */
public interface FeatureService {

    /**
     * 新增特征
     *
     * @param dto 特征新增请求
     * @return 新增结果
     */
    RestfulResultsV2<FeatureVO> add(FeatureAddDTO dto);


    /**
     * 分页查询特征
     *
     * @param searchDTO 检索参数
     * @return 特征分页列表
     */
    RestfulResultsV2<FeatureVO> findFeatures(FeatureSearchDTO searchDTO);

    /**
     * 分页查询特征
     *
     * @param ids 检索参数
     * @return 特征分页列表
     */
    RestfulResultsV2<FeatureVO> findFeaturesByIds(List<Long> ids);

    /**
     * 删除特征
     *
     * @param featureId 特征ID
     * @return 删除结果
     */
    boolean deleteFeature(Long featureId);

    /**
     * 启用/停用特征
     *
     * @param id     特征ID
     * @param status 状态 0=停用 1=启用
     * @return 操作结果
     */
    boolean toggleFeatureStatus(Long id, Integer status);

    /**
     * 更新特征关联标签数量
     *
     * @param featureId  特征ID
     * @param labelCount 关联标签数量
     * @return 更新后的特征
     */
    FeatureVO updateLabelCount(Long featureId, Integer labelCount);

    /**
     * 保存特征节点信息
     *
     * @param processDTO 参数
     */
    void saveNode(FeatureProcessDTO processDTO);

    /**
     * 获取特征详情
     *
     * @param featureId 特征ID
     * @return 特征详情
     */
    FeatureDetailVO detail(Long featureId);

    /**
     * 复制特征
     *
     * @param featureId 待复制特征ID
     * @return 新特征ID
     */
    Long copy(Long featureId);
}
