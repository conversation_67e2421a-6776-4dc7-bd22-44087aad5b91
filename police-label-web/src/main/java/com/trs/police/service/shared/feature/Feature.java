package com.trs.police.service.shared.feature;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 特征
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class Feature {

    private Long featureId;

    private String featureName;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        Feature feature = (Feature) o;
        return Objects.equals(featureId, feature.featureId);
    }
}
