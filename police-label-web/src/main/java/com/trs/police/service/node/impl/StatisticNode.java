package com.trs.police.service.node.impl;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.constant.StatisticType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ValueMateBase;
import com.trs.police.dto.node.properties.StatisticNodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;
import io.vavr.control.Try;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.police.service.node.GroupHelper.getGroupKey;

/**
 * 统计节点
 *
 * <AUTHOR>
 */
public class StatisticNode extends Node {

    public StatisticNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        StatisticNodeProperties property = getPropertyAs(StatisticNodeProperties.class);
        if (StringUtils.isEmpty(property.getOutValue().getFromNode())) {
            property.getOutValue().setFromNode(nodeMeta.getUuid());
        }
        // 执行分组
        Map<String, List<List<FieldValue>>> collect = inputNode.get(0)
                .getData()
                .stream()
                .collect(Collectors.groupingBy(getGroupKey(property.getGroupField())));
        // 执行统计
        StatisticType type = StatisticType.of(property.getStatisticType());
        switch (type) {
            case AVG:
                return avg(inputNode, context, property, collect);
            case SUM:
                return sum(inputNode, context, property, collect);
            case COUNT:
                return count(inputNode, context, property, collect);
            case MAX:
                return max(inputNode, context, property, collect);
            case MIN:
                return min(inputNode, context, property, collect);
            default:
                throw new RuntimeException("统计类型出错");
        }
    }

    @Override
    public Integer nodeType() {
        return NodeType.STATISTIC;
    }

    private NodeData avg(List<NodeData> inputNode, NodeContext context, StatisticNodeProperties property, Map<String, List<List<FieldValue>>> group) {
        // 根据输出字段构造列头
        ValueMateBase statisticValue = property.getStatisticValue();
        DataBaseFieldMappingType type = DataBaseFieldMappingType.getType(statisticValue.getTypeCode());
        FieldInfoVO fieldInfo = new FieldInfoVO(property.getOutValue().getCnName(), property.getOutValue().getEnName(), null, nodeMeta.getUuid());
        switch (type) {
            case STRING:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.STRING.getFieldType());
                break;
            case NUMBER:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.NUMBER.getFieldType());
                break;
            case DATETIME:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.DATETIME.getFieldType());
                break;
            default:
                throw new RuntimeException("统计字段类型出错");
        }

        // 根据分组映射到输出字段
        List<List<FieldValue>> out = group.values()
                .stream()
                .map(g -> {
                    // 组内取第一条
                    List<FieldValue> row = g.get(0);
                    // 计算总和
                    BigDecimal total = g.stream()
                            .map(r -> r.stream().filter(c -> c.getEnName().equals(statisticValue.getEnName())).findAny().get())
                            .map(v -> {
                                if (StringUtils.isEmpty(v.getValue())) {
                                    return BigDecimal.ZERO;
                                }
                                switch (type) {
                                    case STRING:
                                        return Try.of(() -> BigDecimal.valueOf(Double.valueOf(v.getValue()))).getOrElse(BigDecimal.ZERO);
                                    case NUMBER:
                                        return Try.of(() -> BigDecimal.valueOf(Double.valueOf(v.getValue()))).getOrElse(BigDecimal.ZERO);
                                    case DATETIME:
                                        return BigDecimal.valueOf(LocalDateTime.parse(v.getValue())
                                                .atZone(ZoneId.systemDefault())
                                                .toEpochSecond());
                                    default:
                                        throw new RuntimeException("统计字段类型出错");
                                }
                            })
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);
                    // 构造统计输出字段
                    BigDecimal divide = total.divide(BigDecimal.valueOf(g.size()), 4, BigDecimal.ROUND_HALF_UP);
                    List<FieldValue> outRow = row.stream()
                            .filter(c -> !c.getId().equals(property.getOutValue().getId()))
                            .collect(Collectors.toList());
                    switch (type) {
                        case DATETIME:
                            outRow.add(new FieldValue(TimeUtils.dateToString(new Date(divide.longValue())), fieldInfo));
                            break;
                        default:
                            outRow.add(new FieldValue(divide.toString(), fieldInfo));
                            break;
                    }
                    return outRow;
                })
                .collect(Collectors.toList());

        // 构造输出数据
        NodeData data = new NodeData();
        data.setData(out);
        List<FieldInfoVO> hearder = inputNode.get(0).getHeader()
                .stream()
                .filter(h -> !h.getEnName().equals(property.getOutValue().getEnName()))
                .collect(Collectors.toList());
        hearder.add(fieldInfo);
        data.setHeader(hearder);
        data.setNodeMeta(nodeMeta);
        data.setTotalCount(out.stream().count());
        return data;
    }

    private NodeData sum(List<NodeData> inputNode, NodeContext context, StatisticNodeProperties property, Map<String, List<List<FieldValue>>> group) {
        // 根据输出字段构造列头
        ValueMateBase statisticValue = property.getStatisticValue();
        DataBaseFieldMappingType type = DataBaseFieldMappingType.getType(statisticValue.getTypeCode());
        FieldInfoVO fieldInfo = new FieldInfoVO(property.getOutValue().getCnName(), property.getOutValue().getEnName(), null, nodeMeta.getUuid());
        switch (type) {
            case STRING:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.STRING.getFieldType());
                break;
            case NUMBER:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.NUMBER.getFieldType());
                break;
            default:
                throw new RuntimeException("统计字段类型出错");
        }

        // 根据分组映射到输出字段
        List<List<FieldValue>> out = group.values()
                .stream()
                .map(g -> {
                    List<FieldValue> row = g.get(0);
                    BigDecimal total = g.stream()
                            .map(r -> r.stream().filter(c -> c.getEnName().equals(statisticValue.getEnName())).findAny().get())
                            .map(v -> {
                                if (StringUtils.isEmpty(v.getValue())) {
                                    return BigDecimal.ZERO;
                                }
                                switch (type) {
                                    case STRING:
                                        return Try.of(() -> BigDecimal.valueOf(Double.valueOf(v.getValue()))).getOrElse(BigDecimal.ZERO);
                                    case NUMBER:
                                        return Try.of(() -> BigDecimal.valueOf(Double.valueOf(v.getValue()))).getOrElse(BigDecimal.ZERO);
                                    case DATETIME:
                                        return BigDecimal.valueOf(LocalDateTime.parse(v.getValue())
                                                .atZone(ZoneId.systemDefault())
                                                .toEpochSecond());
                                    default:
                                        throw new RuntimeException("统计字段类型出错");
                                }
                            })
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);
                    FieldValue fieldValue = new FieldValue(total.toString(), fieldInfo);
                    List<FieldValue> outRow = row.stream()
                            .filter(c -> !c.getId().equals(property.getOutValue().getId()))
                            .collect(Collectors.toList());
                    outRow.add(fieldValue);
                    return outRow;
                })
                .collect(Collectors.toList());

        // 构造输出数据
        NodeData data = new NodeData();
        data.setData(out);
        List<FieldInfoVO> hearder = inputNode.get(0).getHeader()
                .stream()
                .filter(h -> !h.getEnName().equals(property.getOutValue().getEnName()))
                .collect(Collectors.toList());
        hearder.add(fieldInfo);
        data.setHeader(hearder);
        data.setNodeMeta(nodeMeta);
        data.setTotalCount(out.stream().count());
        return data;
    }

    private NodeData count(List<NodeData> inputNode, NodeContext context, StatisticNodeProperties property, Map<String, List<List<FieldValue>>> group) {
        // 根据输出字段构造列头
        FieldInfoVO fieldInfo = new FieldInfoVO(property.getOutValue().getCnName(), property.getOutValue().getEnName(), DataBaseFieldMappingType.NUMBER.getFieldType(), nodeMeta.getUuid());

        // 根据分组映射到输出字段
        List<List<FieldValue>> out = group.values()
                .stream()
                .map(g -> {
                    // 组内取第一条
                    List<FieldValue> row = g.get(0);
                    BigDecimal total = BigDecimal.valueOf(g.size());
                    FieldValue fieldValue = new FieldValue(total.toString(), fieldInfo);
                    List<FieldValue> outRow = row.stream()
                            .filter(c -> !c.getId().equals(property.getOutValue().getId()))
                            .collect(Collectors.toList());
                    outRow.add(fieldValue);
                    return outRow;
                })
                .collect(Collectors.toList());

        // 构造输出数据
        NodeData data = new NodeData();
        data.setData(out);
        List<FieldInfoVO> hearder = inputNode.get(0).getHeader()
                .stream()
                .filter(h -> !h.getEnName().equals(property.getOutValue().getEnName()))
                .collect(Collectors.toList());
        hearder.add(fieldInfo);
        data.setHeader(hearder);
        data.setNodeMeta(nodeMeta);
        data.setTotalCount(out.stream().count());
        return data;
    }

    private NodeData max(List<NodeData> inputNode, NodeContext context, StatisticNodeProperties property, Map<String, List<List<FieldValue>>> group) {
        // 根据输出字段构造列头
        ValueMateBase statisticValue = property.getStatisticValue();
        DataBaseFieldMappingType type = DataBaseFieldMappingType.getType(statisticValue.getTypeCode());
        FieldInfoVO fieldInfo = new FieldInfoVO(property.getOutValue().getCnName(), property.getOutValue().getEnName(), DataBaseFieldMappingType.NUMBER.getFieldType(), nodeMeta.getUuid());
        switch (type) {
            case STRING:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.STRING.getFieldType());
                break;
            case NUMBER:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.NUMBER.getFieldType());
                break;
            default:
                throw new RuntimeException("统计字段类型出错");
        }

        // 根据分组映射到输出字段
        List<List<FieldValue>> out = group.values()
                .stream()
                .map(g -> {
                    List<FieldValue> row = g.get(0);
                    BigDecimal total = g.stream()
                            .map(r -> r.stream().filter(c -> c.getEnName().equals(statisticValue.getEnName())).findAny().get())
                            .map(v -> {
                                if (StringUtils.isEmpty(v.getValue())) {
                                    return BigDecimal.ZERO;
                                }
                                switch (type) {
                                    case STRING:
                                        return Try.of(() -> BigDecimal.valueOf(Double.valueOf(v.getValue()))).getOrElse(BigDecimal.ZERO);
                                    case NUMBER:
                                        return Try.of(() -> BigDecimal.valueOf(Double.valueOf(v.getValue()))).getOrElse(BigDecimal.ZERO);
                                    case DATETIME:
                                        return BigDecimal.valueOf(LocalDateTime.parse(v.getValue())
                                                .atZone(ZoneId.systemDefault())
                                                .toEpochSecond());
                                    default:
                                        throw new RuntimeException("统计字段类型出错");
                                }
                            })
                            .max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                    FieldValue fieldValue = new FieldValue(total.toString(), fieldInfo);
                    List<FieldValue> outRow = row.stream()
                            .filter(c -> !c.getId().equals(property.getOutValue().getId()))
                            .collect(Collectors.toList());
                    outRow.add(fieldValue);
                    return outRow;
                })
                .collect(Collectors.toList());

        // 构造输出数据
        NodeData data = new NodeData();
        data.setData(out);
        List<FieldInfoVO> hearder = inputNode.get(0).getHeader()
                .stream()
                .filter(h -> !h.getEnName().equals(property.getOutValue().getEnName()))
                .collect(Collectors.toList());
        hearder.add(fieldInfo);
        data.setHeader(hearder);
        data.setNodeMeta(nodeMeta);
        data.setTotalCount(out.stream().count());
        return data;
    }

    private NodeData min(List<NodeData> inputNode, NodeContext context, StatisticNodeProperties property, Map<String, List<List<FieldValue>>> group) {
        // 根据输出字段构造列头
        ValueMateBase statisticValue = property.getStatisticValue();
        DataBaseFieldMappingType type = DataBaseFieldMappingType.getType(statisticValue.getTypeCode());
        FieldInfoVO fieldInfo = new FieldInfoVO(property.getOutValue().getCnName(), property.getOutValue().getEnName(), DataBaseFieldMappingType.NUMBER.getFieldType(), nodeMeta.getUuid());
        switch (type) {
            case STRING:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.STRING.getFieldType());
                break;
            case NUMBER:
                fieldInfo.setTypeCode(DataBaseFieldMappingType.NUMBER.getFieldType());
                break;
            default:
                throw new RuntimeException("统计字段类型出错");
        }

        List<List<FieldValue>> out = group.values()
                .stream()
                .map(g -> {
                    List<FieldValue> row = g.get(0);
                    BigDecimal total = g.stream()
                            .map(r -> r.stream().filter(c -> c.getEnName().equals(statisticValue.getEnName())).findAny().get())
                            .map(v -> {
                                if (StringUtils.isEmpty(v.getValue())) {
                                    return BigDecimal.ZERO;
                                }
                                switch (type) {
                                    case STRING:
                                        return Try.of(() -> BigDecimal.valueOf(Double.valueOf(v.getValue()))).getOrElse(BigDecimal.ZERO);
                                    case NUMBER:
                                        return Try.of(() -> BigDecimal.valueOf(Double.valueOf(v.getValue()))).getOrElse(BigDecimal.ZERO);
                                    case DATETIME:
                                        return BigDecimal.valueOf(LocalDateTime.parse(v.getValue())
                                                .atZone(ZoneId.systemDefault())
                                                .toEpochSecond());
                                    default:
                                        throw new RuntimeException("统计字段类型出错");
                                }
                            })
                            .min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                    FieldValue fieldValue = new FieldValue(total.toString(), fieldInfo);
                    List<FieldValue> outRow = row.stream()
                            .filter(c -> !c.getId().equals(property.getOutValue().getId()))
                            .collect(Collectors.toList());
                    outRow.add(fieldValue);
                    return outRow;
                })
                .collect(Collectors.toList());

        // 构造输出数据
        NodeData data = new NodeData();
        data.setData(out);
        List<FieldInfoVO> hearder = inputNode.get(0).getHeader()
                .stream()
                .filter(h -> !h.getEnName().equals(property.getOutValue().getEnName()))
                .collect(Collectors.toList());
        hearder.add(fieldInfo);
        data.setHeader(hearder);
        data.setNodeMeta(nodeMeta);
        data.setTotalCount(out.stream().count());
        return data;
    }
}
