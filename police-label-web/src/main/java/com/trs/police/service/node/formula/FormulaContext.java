package com.trs.police.service.node.formula;

import com.trs.police.common.core.vo.node.Row;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 公式上下文
 */
@Data
@NoArgsConstructor
public class FormulaContext {

    /**
     * 节点数据
     */
    private NodeData nodeData;

    /**
     * 当前行
     */
    private Row currentRow;

    /**
     * 其它上下文数据
     */
    private Map<String, Object> context = new HashMap<>();

    public FormulaContext(NodeData nodeData) {
        this.nodeData = nodeData;
    }

    public FormulaContext(NodeData nodeData, Row currentRow) {
        this.nodeData = nodeData;
        this.currentRow = currentRow;
    }
}
