package com.trs.police.service.shared.table;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.entity.datatable.DataTable;
import com.trs.police.mapper.DataTableMapper;
import com.trs.police.service.datatable.DataTableService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表
 */
@Component
public class TableFacade {

    @Autowired
    private DataTableMapper tableMapper;

    @Autowired
    private DataTableService tableService;

    /**
     * 根据id查询列表
     *
     * @param ids id列表
     * @return 外观列表
     */
    public List<TableItemDTO> findByIds(List<Long> ids) {
        List<DataTable> dataTableList = CollectionUtils.isEmpty(ids) ? new ArrayList<>() : tableMapper.selectBatchIds(ids);
        return dataTableList.stream()
                .map(t -> {
                    TableItemDTO tableItemDTO = new TableItemDTO();
                    tableItemDTO.setTableId(t.getId());
                    tableItemDTO.setTableName(t.getTableName());
                    tableItemDTO.setTableNameCn(t.getTableNameCn());
                    return tableItemDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取主键字段
     *
     * @param tableId 表哥id
     * @return 主键字段
     */
    public String getPrimaryKey(Long tableId) {
        DataTable dataTable = tableMapper.selectById(tableId);
        return dataTable.getIdField();
    }

    /**
     * 数据表详情页
     *
     * @param tableId tableId
     * @param ids dto
     * @return 数据表预览页
     */
    public Map<String, JSONObject> getDataOverviewByPrimaryKey(Long tableId, List<String> ids) {
        return tableService.getDataOverviewByPrimaryKey(tableId, ids);
    }
}
