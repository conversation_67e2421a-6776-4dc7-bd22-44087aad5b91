package com.trs.police.service.shared.dict;

import org.springframework.stereotype.Component;

/**
 * 字典注册器
 *
 * <AUTHOR>
 */
@Component
public class DictTypeRegistry {

    /**
     * 特征分类
     *
     * @return 特征分类
     */
    public String getFeatureCategory() {
        return "feature_category";
    }

    /**
     * 获取警种
     *
     * @return 警种
     */
    public String getPoliceKind() {
        return "feature_police_kind";
    }

    /**
     * 特征主体
     *
     * @return 特征主体
     */
    public String getFeatureMainObject() {
        return "feature_main_object";
    }

    /**
     * 标签分类
     *
     * @return 标签分类
     */
    public String getLabelCategory() {
        return "label_category";
    }
}
