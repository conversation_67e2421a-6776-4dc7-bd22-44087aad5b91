package com.trs.police.service.node.impl.nodeDTO;

import com.alibaba.fastjson.JSON;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.OrderDTO;
import com.trs.police.dto.node.properties.RelevancyNodeProperties;
import com.trs.police.vo.RowFieldVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 集合输入节点
 */
@Component
public class UnionNodeDTOBuilder extends LabelNodeDTOBuilder {
    @Override
    public boolean supports(Integer nodeTypeCode) {
        return NodeType.RELEVANCY.equals(nodeTypeCode);
    }

    @Override
    public List<LabelNodeDTO> build(NodeDTO nodeDTO, NodeContext nodeContext) {
        RelevancyNodeProperties property = JSON.parseObject(nodeDTO.getNodeProperties(), RelevancyNodeProperties.class);
        String otherNodeUuid = nodeDTO.getLastNodeId().equals(property.getTableA()) ? property.getTableB() : property.getTableA();
        List<LabelNodeDTO> otherLabelNodes = buildOtherWithOrders(otherNodeUuid, nodeContext);
        List<RowFieldVo> outputRow = nodeDTO.getNodeMeta().getOutputRowMeta().getValueMetaList().stream().map(valueMateBase -> RowFieldVo.of(valueMateBase.getCol(), valueMateBase.getCol())).collect(Collectors.toList());
        return Collections.singletonList(new LabelNodeDTO(NodeType.RELEVANCY, nodeDTO.getNodeProperties(), outputRow, otherLabelNodes, nodeDTO.getLastNodeId()));
    }

    /**
     * 构建其他节点（包含完整的父节点查找和排序功能）
     *
     * @param uuid 目标节点UUID
     * @param nodeContext 上下文
     * @return 按顺序排列的父节点LabelNodeDTO列表
     */
    private List<LabelNodeDTO> buildOtherWithOrders(String uuid, NodeContext nodeContext) {
        // 找到所有父节点的UUID列表（按照从根节点到当前节点的顺序）
        List<String> uuids = findAllNodesInOrder(uuid, nodeContext.getNodeOrders());
        // 获取LabelNodeDTOBuilderFactory实例
        LabelNodeDTOBuilderFactory labelNodeDtoBuilderFactory = BeanFactoryHolder.getBean(LabelNodeDTOBuilderFactory.class).get();
        // 按顺序转换所有节点为LabelNodeDTO
        List<LabelNodeDTO> result = new ArrayList<>();
        for (int i = 0; i < uuids.size(); i++) {
            String currentId = uuids.get(i);
            String parentUuid = i == 0 ? null : uuids.get(i - 1);
            NodeDTO nodeDTO = nodeContext.getNodes().stream()
                    .filter(node -> node.getNodeMeta().getUuid().equals(currentId))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("未找到节点: " + currentId));
            nodeDTO.setLastNodeId(parentUuid);
            List<LabelNodeDTO> parentLabelNodes = labelNodeDtoBuilderFactory.buildByStrategy(nodeDTO, nodeContext);
            result.addAll(parentLabelNodes);
        }

        return result;
    }

    /**
     * 找到指定节点的所有父节点，包含节点自己，按照从根节点到当前节点的顺序返回
     *
     * @param uuid 目标节点UUID
     * @param nodeOrders 节点排序关系
     * @return 从根节点到目标节点的完整路径UUID列表（包含目标节点本身）
     */
    private List<String> findAllNodesInOrder(String uuid, List<OrderDTO> nodeOrders) {
        List<String> pathFromTargetToRoot = new ArrayList<>();
        String currentUuid = uuid;

        // 从当前节点向上追溯到根节点，构建从目标到根的路径
        while (currentUuid != null) {
            pathFromTargetToRoot.add(currentUuid);
            // 查找当前节点的直接父节点
            String parentUuid = findDirectParent(currentUuid, nodeOrders);
            currentUuid = parentUuid;
        }

        // 反转列表，得到从根节点到目标节点的顺序
        List<String> pathFromRootToTarget = new ArrayList<>();
        for (int i = pathFromTargetToRoot.size() - 1; i >= 0; i--) {
            pathFromRootToTarget.add(pathFromTargetToRoot.get(i));
        }

        return pathFromRootToTarget;
    }

    /**
     * 查找指定节点的直接父节点
     *
     * @param uuid 目标节点UUID
     * @param nodeOrders 节点排序关系
     * @return 直接父节点的UUID，如果没有父节点则返回null
     */
    private String findDirectParent(String uuid, List<OrderDTO> nodeOrders) {
        return nodeOrders.stream()
                .filter(order -> order.getTo().equals(uuid))
                .map(OrderDTO::getFrom)
                .findFirst()
                .orElse(null);
    }
} 