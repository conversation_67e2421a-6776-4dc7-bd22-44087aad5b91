package com.trs.police.service.shared.user.ysimpl;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 组织机构信息表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_dept")
public class Dept extends AbstractBaseEntity {

    private static final long serialVersionUID = 5002999587408687379L;

    /**
     * 机构代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 机构名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 机构名称简称
     */
    @TableField(value = "short_name")
    private String shortName;

    /**
     * 上级部门id
     */
    @TableField(value = "pid")
    private Long pid;

    /**
     * 警种代码
     */
    @TableField(value = "police_kind")
    private Long policeKind;

    /**
     * 排序使用
     */
    @TableField(value = "show_order")
    private Integer showOrder;

    /**
     * 部门签章图片位置
     */
    @TableField(value = "signature")
    private Long signature;

    /**
     * 状态
     */
    @TableField(value = "deleted")
    @TableLogic
    private Short deleted;

    /**
     * 组织类别
     */
    @TableField(value = "type")
    private Long type;

    @TableField(value = "child_type")
    private Long childType;

    /**
     * 所属区域
     */
    @TableField(value = "district_code")
    private String districtCode;

    /**
     * 所属区域（名称）
     */
    @TableField(exist = false)
    private String districtName;

    /**
     * 所属区域（简称）
     */
    @TableField(exist = false)
    private String districtShortName;

    /**
     * 区域主要名称
     */
    @TableField(exist = false)
    private String districtMainName;

    /**
     * 部门级别
     */
    @TableField("level")
    private Integer level;

    /**
     * 部门级别
     */
    @TableField("path")
    private String path;
    /**
     * 子结点
     */
    @TableField(exist = false)
    private List<Dept> children;

    /**
     * 是否有子节点
     */
    @TableField(exist = false)
    private Boolean hasChild = false;

    /**
     * 组织签章
     */
    @TableField("signet")
    private String signet;

    /**
     * 是否虚拟组织
     */
    @TableField(value = "is_virtual_dept")
    private Integer isVirtualDept;
}
