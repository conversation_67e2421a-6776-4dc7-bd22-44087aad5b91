package com.trs.police.service.node.impl;

import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.RelevancyNodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;

import java.util.List;

import static com.trs.police.constant.NodeType.RELEVANCY;

/**
 * 关联节点代理
 *
 * <AUTHOR>
 */
public class RelevancyNodeProxy extends Node {

    public RelevancyNodeProxy(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        RelevancyNodeProperties property = getPropertyAs(RelevancyNodeProperties.class);
        switch (property.getType()) {
            case 1:
                return new JoinNode(nodeMeta, nodeProperties).output(inputNode, context);
            case 2:
                return new UnionNode(nodeMeta, nodeProperties).output(inputNode, context);
            case 3:
                return new UnionNode(nodeMeta, nodeProperties).output(inputNode, context);
            default:
                throw new RuntimeException("未知的关联类型");
        }
    }

    @Override
    public Integer nodeType() {
        return RELEVANCY;
    }
}
