package com.trs.police.service.node.formula;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.constant.node.Operator;
import com.trs.police.dto.node.FunctionMeta;
import com.trs.police.service.node.formula.impl.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 公式解析器
 *
 * <AUTHOR>
 */
public class FormulaParser {
    private static final ObjectMapper MAPPER = new ObjectMapper();

    /**
     * 解析公式
     *
     * @param json 公式json
     * @return 公式
     * @throws Exception 解析异常
     */
    public static Expression parse(String json) throws Exception {
        JsonNode root = MAPPER.readTree(json);
        if (root.isArray()) {
            return parseGroup(root);
        }
        throw new IllegalArgumentException("Invalid formula format");
    }

    private static Expression parseGroup(JsonNode node) throws Exception {
        List<Expression> expressions = new ArrayList<>();
        for (JsonNode child : node) {
            expressions.add(parseExpression(child));
        }

        // 将运算符和操作数组合成二元运算表达式
        return buildExpressionTree(expressions);
    }

    private static Expression buildExpressionTree(List<Expression> expressions) {
        if (expressions.isEmpty()) {
            throw new IllegalArgumentException("Empty expression group");
        }

        // 处理一元运算符
        List<Expression> processedExpressions = new ArrayList<>();
        for (int i = 0; i < expressions.size(); i++) {
            Expression current = expressions.get(i);

            if (current instanceof OperatorExpression) {
                OperatorExpression opExpr = (OperatorExpression) current;
                Operator operator = opExpr.getOperator();

                // 检查是否是一元运算符（位于开头或前一个元素也是运算符）
                if (i == 0 || (i > 0 && expressions.get(i - 1) instanceof OperatorExpression)) {
                    // 确保后面有操作数
                    if (i + 1 >= expressions.size()) {
                        throw new IllegalArgumentException("Unary operator " + operator + " missing operand");
                    }

                    Expression operand = expressions.get(i + 1);
                    // 创建一元运算表达式
                    processedExpressions.add(new UnaryOperationExpression(operator, operand));
                    i++; // 跳过已处理的operand
                } else {
                    // 二元运算符，保留原样
                    processedExpressions.add(current);
                }
            } else {
                processedExpressions.add(current);
            }
        }

        // 构建二元表达式树
        Expression result = processedExpressions.get(0);

        for (int i = 1; i < processedExpressions.size(); i += 2) {
            if (i + 1 >= processedExpressions.size()) {
                throw new IllegalArgumentException("Malformed expression: operator without right operand");
            }

            Expression opExpr = processedExpressions.get(i);
            if (!(opExpr instanceof OperatorExpression)) {
                throw new IllegalArgumentException("Expected operator but got: " + opExpr.getClass().getSimpleName());
            }

            Operator operator = ((OperatorExpression) opExpr).getOperator();
            Expression right = processedExpressions.get(i + 1);

            result = new BinaryOperationExpression(result, operator, right);
        }

        return result;
    }

    private static Expression parseExpression(JsonNode node) throws Exception {
        String type = node.get("type").asText();
        switch (type) {
            case "string":
            case "number":
            case "datetime":
                return new ConstantExpression(node.get("value").asText(), type);
            case "prop":
                FieldInfoVO fieldInfoVO = MAPPER.treeToValue(node.get("metadata"), FieldInfoVO.class);
                return new PropertyExpression(fieldInfoVO);
            case "operator":
                // 返回一个特殊的OperatorExpression
                return new OperatorExpression(Operator.fromSymbol(node.get("operator").asText()));
            case "group":
                return parseGroup(node.get("expressions"));
            case "function":
                return parseFunction(node);
            default:
                throw new IllegalArgumentException("Unknown expression type: " + type);
        }
    }

    private static Expression parseFunction(JsonNode node) throws Exception {
        JsonNode paramsNode = node.get("params");
        List<Expression> params = new ArrayList<>();

        for (JsonNode paramGroup : paramsNode) {
            if (paramGroup.isArray()) {
                // 参数组是一个表达式序列，需要构建表达式树
                List<Expression> paramExpressions = new ArrayList<>();
                for (JsonNode paramExpr : paramGroup) {
                    paramExpressions.add(parseExpression(paramExpr));
                }
                // 如果不包含操作符，那么直接就是函数的参数，否则还可能是个表达式
                boolean anyMatch = paramExpressions.stream()
                        .anyMatch(paramExpr -> paramExpr instanceof OperatorExpression);
                if (anyMatch) {
                    params.add(buildExpressionTree(paramExpressions));
                } else {
                    params.addAll(paramExpressions);
                }
            } else {
                // 单个参数表达式
                params.add(parseExpression(paramGroup));
            }
        }

        // 解析元数据（如果有）
        FunctionMeta metadata = node.has("metadata")
                ? MAPPER.treeToValue(node.get("metadata"), FunctionMeta.class)
                : null;

        return new FunctionCallExpression(metadata.getId(), params);
    }
}
