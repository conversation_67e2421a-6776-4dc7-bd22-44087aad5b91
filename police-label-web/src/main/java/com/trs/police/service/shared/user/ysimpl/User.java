package com.trs.police.service.shared.user.ysimpl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chenhaiyang.plugin.mybatis.sensitive.annotation.SensitiveEncryptEnabled;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 系统用户信息表
 *
 * <AUTHOR>
 */
@SensitiveEncryptEnabled
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_user")
public class User extends AbstractBaseEntity {

    private static final long serialVersionUID = -4352868070794165001L;

    /**
     * 用户状态：有效
     */
    public static final Byte STATUS_VALID = 1;
    /**
     * 用户状态：锁定
     */
    public static final Byte STATUS_LOCKED = 0;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @TableField(value = "username")
    private String username;

    /**
     * 真实姓名
     */
    @TableField(value = "real_name")
    private String realName;

    /**
     * 身份证号码
     */
    @TableField(value = "idcard")
    private String idNumber;

    /**
     * 出生日期
     */
    @TableField(value = "birthday")
    private LocalDate birthday;

    /**
     * 性别
     */
    @TableField(value = "gender")
    private String gender;

    /**
     * 办公电话
     */
    @TableField(value = "telephone")
    private String telephone;

    /**
     * 手机号码
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 警号
     */
    @TableField(value = "police_code")
    private String policeCode;

    /**
     * 账号有效期限,默认三年
     */
    @TableField(value = "valid_date")
    private LocalDateTime validDate;

    /**
     * 账号状态
     */
    @TableField(value = "status")
    private Byte status;

    /**
     * 个人签章图片id
     */
    @TableField(value = "signature")
    private Long signature;

    /**
     * 个人头像图片位置
     */
    @TableField(value = "avatar")
    private String avatar;

    /**
     * 账户密码,默认trsadmin
     */
    @TableField(value = "password")
    private String password;

    /**
     * 职务
     */
    @TableField(value = "duty")
    private String duty;

    /**
     * 证件号码
     */
    @TableField(value = "zjhm")
    private String zjhm;

    /**
     * 完整性加密信息
     */
    @TableField(value = "mac")
    private String mac;

    /**
     * 个人签章
     */
    @TableField(value = "signet")
    private String signet;

    /**
     * 岗位代码
     */
    @TableField(value = "post_code")
    private Long postCode;
}
