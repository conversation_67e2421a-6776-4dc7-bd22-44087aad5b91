package com.trs.police.service.node.impl.nodeDTO;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.expression.EmtpyExpression;
import com.trs.common.utils.expression.Expression;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.properties.FilterNodeProperties;
import com.trs.police.dto.node.properties.SparkFilterProperties;
import com.trs.police.service.node.SparkFilterNodeConditionParser;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 过滤节点
 */
@Component
public class FilterNodeDTOBuilder extends LabelNodeDTOBuilder {
    @Override
    public boolean supports(Integer nodeTypeCode) {
        return NodeType.FILTER.equals(nodeTypeCode);
    }

    @Override
    public List<LabelNodeDTO> build(NodeDTO nodeDTO, NodeContext nodeContext) {
        FilterNodeProperties properties = JSONObject.parseObject(nodeDTO.getNodeProperties(), FilterNodeProperties.class);
        SparkFilterNodeConditionParser parser = new SparkFilterNodeConditionParser(nodeContext);
        Expression expression = (properties.getTokens()!=null && properties.getTokens().length>0)
                ? parser.parseCondition(JSONObject.toJSONString(properties.getTokens()))
                : new EmtpyExpression();
        String where = where(expression);
        SparkFilterProperties sparkFilterProperties = new SparkFilterProperties(where);
        return Collections.singletonList(new LabelNodeDTO(NodeType.FILTER, JSONObject.toJSONString(sparkFilterProperties), getOuputRow(nodeDTO), nodeDTO.getLastNodeId()));
    }
} 