package com.trs.police.service.datatable;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.expression.Expression;
import com.trs.police.dto.DataTableDTO;
import com.trs.police.dto.DataTableOverviewDto;
import com.trs.police.dto.TableSelectionDTO;
import com.trs.police.vo.DataTableVO;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;
import java.util.Map;


/**
 * 数据表服务接口
 *
 * <AUTHOR>
 */
public interface DataTableService {

    /**
     * 根据数据源获取该数据源下的全部数据表
     *
     * @param dto  数据表DTO
     * @return 数据表列表
     */
    RestfulResultsV2<DataTableVO> getAllDataTable(TableSelectionDTO dto);

    /**
     * 修改数据表
     *
     * @param id  数据表ID
     * @param dto 数据表dto
     */
    void updateDataSource(Long id, DataTableDTO dto);

    /**
     * 选中数据表
     *
     * @param dto  dto
     */
    void selectTables(TableSelectionDTO dto);

    /**
     * 删除数据表
     *
     * @param id  数据表ID
     */
    void delete(Long id);


    /**
     * 数据表详情页
     *
     * @param id id
     * @return 数据表详情页
     */
    RestfulResultsV2<DataTableVO> datatableDetail(Integer id);

    /**
     * 数据表详情页
     *
     * @param dto dto
     * @return 数据表预览页
     */
    RestfulResultsV2<JSONObject> getDataOverview(DataTableOverviewDto dto);

    /**
     * 数据表详情页
     *
     * @param dto dto
     * @param expression 条件
     * @return 数据表预览页
     */
    RestfulResultsV2<JSONObject> getData(DataTableOverviewDto dto, Expression expression);

    /**
     * 刷新表信息
     *
     * @param tableId 表ID
     */
    void refreshTableById(Long tableId);

    /**
     * 数据表详情页
     *
     * @param tableId tableId
     * @param ids dto
     * @return 数据表预览页
     */
    Map<String, JSONObject> getDataOverviewByPrimaryKey(Long tableId, List<String> ids);
}
