package com.trs.police.service.shared.dict;

import com.trs.police.dto.dict.DictAddDTO;
import com.trs.police.dto.dict.DictDto;
import com.trs.police.service.dict.DictService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 对外暴露接口，理论上是http接口，但是这里只有系统内部调用，所以直接注册成一个bean
 */
@Component
public class DictFacade {

    @Autowired
    private DictService dictService;

    /**
     * 批量获取字典信息
     *
     * @param type 字典类型
     * @return 字典信息
     */
    public List<DictItemDTO> getDicts(String type) {
        return dictService.getFlatDictList(type).stream()
                .map(d -> {
                    DictItemDTO itemDTO = new DictItemDTO();
                    BeanUtils.copyProperties(d, itemDTO);
                    return itemDTO;
                })
                .collect(Collectors.toList());
    }


    /**
     * 获取字典信息
     *
     * @param dictReference 字典应用
     * @return 字典信息
     */
    public DictItemDTO getDict(DictReference dictReference) {
        return getDicts(dictReference.getType())
                .stream()
                .filter(d -> d.getCode().equals(dictReference.getCode()))
                .findAny()
                .get();
    }

    /**
     * 添加字典信息
     *
     * @param dto 字典信息
     * @return 字典信息
     */
    public DictItemDTO addDict(SharedDictAddDTO dto) {
        DictAddDTO dictAddDTO = new DictAddDTO();
        BeanUtils.copyProperties(dto, dictAddDTO);
        DictDto dictDto = dictService.add(dictAddDTO);
        DictItemDTO itemDTO = new DictItemDTO();
        BeanUtils.copyProperties(dictDto, itemDTO);
        return itemDTO;
    }

    /**
     * 删除码表
     *
     * @param id id
     */
    public void deleteDict(Long id) {
        dictService.deleteDict(id);
    }
}
