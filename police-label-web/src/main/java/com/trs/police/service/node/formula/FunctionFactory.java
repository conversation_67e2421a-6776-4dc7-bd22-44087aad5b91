package com.trs.police.service.node.formula;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.NodeFunction;
import com.trs.police.service.node.formula.function.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 函数工厂
 *
 * <AUTHOR>
 */
public class FunctionFactory {

    private static Map<String, NodeFunction> functions = Arrays.asList(
                    AbsNodeFunction.INSTANCE,
                    DateDiffNodeFunction.INSTANCE,
                    GetDatePartFunction.INSTANCE,
                    CurdateNodeFunction.INSTANCE,
                    MergeLatLonFunction.INSTANCE,
                    StringToNumberFunction.INSTANCE,
                    CopyFunction.INSTANCE
    )
            .stream()
            .collect(Collectors.toMap(NodeFunction::key, f -> f));


    /**
     * 执行函数
     *
     * @param functionName 函数名称
     * @param parameters   参数
     * @return 函数执行结果
     */
    public static FieldValue execute(String functionName, List<FieldValue> parameters) {
        return functions.get(functionName).execute(parameters);
    }
}
