package com.trs.police.service.node.formula.impl;

import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.formula.Expression;
import com.trs.police.service.node.formula.FormulaContext;

import java.util.Objects;

import static com.trs.police.constant.node.NodeConstants.*;

/**
 * 常量解析
 *
 * <AUTHOR>
 */
public class ConstantExpression implements Expression {


    private final FieldValue value;

    public ConstantExpression(String value, String type) {
        Objects.requireNonNull(type, "type cannot be null");
        DataBaseFieldMappingType tp = DataBaseFieldMappingType.getType(type);
        Objects.requireNonNull(tp, "type is not supported");
        switch (tp) {
            case NUMBER:
                this.value = new FieldValue(value, NUMBER);
                break;
            case STRING:
                this.value = new FieldValue(value, STRING);
                break;
            case DATETIME:
                this.value = new FieldValue(value, DATETIME);
                break;
            default:
                throw new IllegalArgumentException("Unknown type: " + type);
        }
    }

    @Override
    public FieldValue evaluate(FormulaContext context) {
        return value;
    }
}
