package com.trs.police.service.shared.label;

import com.trs.police.common.core.vo.CountVO;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.node.LabelProcessDTO;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.service.label.LabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 特征装饰器
 *
 * <AUTHOR>
 */
@Component
public class LabelFacade {

    @Autowired
    private LabelService labelService;

    /**
     * 获取特征的编排信息
     *
     * @param labelId 特征id
     * @return 特征编排信息
     */
    public ProcessDTO getProcess(Long labelId) {
        LabelProcessDTO javaObject = labelService.detail(labelId)
                .getProcessOrder()
                .toJavaObject(LabelProcessDTO.class);
        ProcessDTO dto = new ProcessDTO();
        dto.setNodes(javaObject.getNodes());
        dto.setControl(javaObject.getControl());
        dto.setNodeOrders(javaObject.getNodeOrders());
        String uuid = javaObject.getNodes().stream()
                .filter(node -> node.getNodeMeta().getNodeTypeCode().equals(NodeType.LABEL_OUTPUT))
                .findAny()
                .map(NodeDTO::getNodeMeta)
                .map(NodeMeta::getUuid)
                .get();
        dto.setCurrentNode(uuid);
        return dto;
    }

    /**
     * 特征使用统计
     *
     * @param featureIds 特征
     * @return 特征使用数量统计
     */
    public List<CountVO> featureUseStatistic(List<Long> featureIds) {
        return labelService.featureUseStatistic(featureIds);
    }
}