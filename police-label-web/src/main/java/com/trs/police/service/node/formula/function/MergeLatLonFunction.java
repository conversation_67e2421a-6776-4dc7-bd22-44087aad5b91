package com.trs.police.service.node.formula.function;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.node.NodeConstants;
import com.trs.police.service.node.NodeFunction;
import com.trs.police.utils.GeoPointParser;

import java.util.List;

/**
 * 合并经纬度
 *
 * <AUTHOR>
 */
public class MergeLatLonFunction implements NodeFunction {

    public static final MergeLatLonFunction INSTANCE = new MergeLatLonFunction();

    @Override
    public FieldValue execute(List<FieldValue> parameters) {
        String lo = parameters.get(0).getValue();
        String lat = parameters.get(1).getValue();
        return new FieldValue(GeoPointParser.parseToWkt(String.format("%s %s", lat, lo)), NodeConstants.GEO);
    }

    @Override
    public String key() {
        return "merge_lat_lon";
    }
}
