package com.trs.police.service.baseService;

import com.trs.police.common.core.vo.TableVO;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.police.vo.common.FormulaVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * DDL服务接口
 *
 * <AUTHOR>
 */
public interface CommonService {



    /**
     * 保存数据源中的全部表和字段信息
     *
     * @param entity 数据源实体
     * @throws Exception Exception
     */
    void saveTableAndFieldData(DataSource entity) throws Exception;

    /**
     * 保存数据源中的全部表和字段信息
     *
     * @param entity 数据源实体
     * @throws Exception Exception
     */
    void refreshTableAndFieldData(DataSource entity) throws Exception;

    /**
     * 刷新表信息
     *
     * @param tableId 表ID
     */
    void refreshTableById(Long tableId) throws Exception;

    /**
     * 获取数据源中的表信息
     *
     * @param sourceInfo 数据源信息
     * @return 表信息
     * @throws Exception Exception
     */
    List<TableVO> getTableInfo(SourceInfo sourceInfo) throws Exception;

    /**
     * 上传kerberos文件
     *
     * @param userName 用户名
     * @param type     类型
     * @param file     文件
     */
    void uploadKerberosFile(String userName,String type,MultipartFile file);


    /**
     * 获取Kerberos文件列表
     *
     * @param userName userName
     * @param type type
     * @return kerberos路径
     */
    List<String> getKerberosFileList(String userName, String type);

    /**
     * 获取公式列表
     *
     * @return 公式列表
     */
    List<FormulaVO> getFormulaList();
}
