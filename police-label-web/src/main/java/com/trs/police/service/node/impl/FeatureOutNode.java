package com.trs.police.service.node.impl;

import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.RowMeta;
import com.trs.police.dto.node.properties.FeatureOutPutProperties;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;

import java.util.List;

/**
 * 特征输出节点
 *
 * <AUTHOR>
 */
public class FeatureOutNode extends Node  {

    public FeatureOutNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        FeatureOutPutProperties propertyAs = getPropertyAs(FeatureOutPutProperties.class);
        NodeData input = inputNode.get(0);
        NodeData outPut = new NodeData();
        outPut.setNodeMeta(nodeMeta);
        outPut.setTotalCount(input.getTotalCount());
        outPut.setData(input.getData());
        outPut.setHeader(input.getHeader());
        // 输出节点字段改成和输入字段一致
        RowMeta outputRowMeta = input.getNodeMeta().getOutputRowMeta();
        nodeMeta.setOutputRowMeta(outputRowMeta);
        return outPut;
    }

    @Override
    public Integer nodeType() {
        return NodeType.FEATURE_OUT;
    }
}
