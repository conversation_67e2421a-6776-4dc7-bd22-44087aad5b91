package com.trs.police.service.shared.table;

import com.trs.police.service.shared.field.FieldReference;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 表引用
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class TableReference {

    /**
     * 表id
     */
    private Long tableId;

    /**
     * 引用字段 （这个表拥有的字段，和业务无关）
     */
    private List<FieldReference> fieldReferences;

    public TableReference(Long tableId) {
        this.tableId = tableId;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        TableReference that = (TableReference) o;
        return Objects.equals(tableId, that.tableId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(tableId);
    }
}
