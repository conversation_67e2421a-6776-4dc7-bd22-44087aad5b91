package com.trs.police.service.shared.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.entity.label.LabelCalculationTaskDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 标签计算任务Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LabelCalculationTaskFacadeMapper extends BaseMapper<LabelCalculationTaskDO> {

    /**
     * 获取所有标签最新的执行状态
     *
     * @return 标签执行状态
     */
    List<LabelCalculationTaskDO> allLabelCalculationTasks();
}
