package com.trs.police.service.feature.infrastructure.repository.converter;

import com.alibaba.fastjson.JSON;
import com.trs.police.service.feature.domain.entity.Feature;
import com.trs.police.entity.feature.FeatureDO;
import com.trs.police.service.feature.domain.value.Filed;
import com.trs.police.service.shared.dict.DictReference;
import com.trs.police.service.shared.dict.DictTypeRegistry;
import com.trs.police.service.shared.table.TableReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 转换器
 */
@Component
public class FeatureConverter {
    
    private final DictTypeRegistry dictTypeRegistry;

    public FeatureConverter(DictTypeRegistry dictTypeRegistry) {
        this.dictTypeRegistry = dictTypeRegistry;
    }

    /**
     * 将DO转换为领域实体
     *
     * @param featureDO 特征DO
     * @return 特征实体
     */
    public Feature toFeature(FeatureDO featureDO) {
        if (featureDO == null) {
            return null;
        }
        Feature feature = new Feature();
        feature.setFeatureId(featureDO.getId());
        feature.setFeatureName(featureDO.getFeatureName());
        feature.setDescription(featureDO.getDescription());
        feature.setBusinessRule(featureDO.getBusinessRule());
        feature.setTable(new TableReference(featureDO.getTableId()));
        feature.setOutputFields(featureDO.getOutputFields());
        feature.setColor(featureDO.getColor());
        feature.setStatus(featureDO.getStatus());
        feature.setCreateTime(featureDO.getCreateTime());
        feature.setCreateUserId(featureDO.getCreateUserId());
        feature.setCreateDeptId(featureDO.getCreateDeptId());
        feature.setUpdateTime(featureDO.getUpdateTime());
        feature.setLabelCount(featureDO.getLabelCount());
        feature.setDeleted(featureDO.getDeleted());

        DictReference category = new DictReference();
        category.setCode(featureDO.getCategoryCode());
        category.setType(dictTypeRegistry.getFeatureCategory());
        feature.setCategory(category);
        
        DictReference policeKind = new DictReference();
        policeKind.setCode(featureDO.getPoliceKind());
        policeKind.setType(dictTypeRegistry.getPoliceKind());
        feature.setPoliceKind(policeKind);
        feature.setMainObject(new DictReference(featureDO.getMainObjectCode(), dictTypeRegistry.getFeatureMainObject()));
        feature.setProcessOrder(featureDO.getProcessOrder());
        if (null != featureDO.getMainObjectField() && !featureDO.getMainObjectField().isEmpty()) {
            feature.setMainObjectField(JSON.parseObject(featureDO.getMainObjectField(), Filed.class));
        }
        feature.setProcessOrderSnapshot(featureDO.getProcessOrderSnapshot());
        return feature;
    }

    /**
     * 将领域实体转换为DO
     *
     * @param feature 特征实体
     * @return 特征DO
     */
    public FeatureDO toFeatureDO(Feature feature) {
        if (feature == null) {
            return null;
        }
        FeatureDO featureDO = new FeatureDO();
        featureDO.setId(feature.getFeatureId());
        featureDO.setFeatureName(feature.getFeatureName());
        featureDO.setDescription(feature.getDescription());
        featureDO.setBusinessRule(feature.getBusinessRule());
        featureDO.setTableId(feature.getTable().getTableId());
        featureDO.setOutputFields(feature.getOutputFields());
        featureDO.setColor(feature.getColor());
        featureDO.setStatus(feature.getStatus());
        featureDO.setCreateTime(feature.getCreateTime());
        featureDO.setCreateUserId(feature.getCreateUserId());
        featureDO.setUpdateTime(feature.getUpdateTime());
        featureDO.setLabelCount(feature.getLabelCount());
        featureDO.setDeleted(feature.getDeleted());

        if (feature.getCategory() != null) {
            featureDO.setCategoryCode(feature.getCategory().getCode());
        }
        if (feature.getPoliceKind() != null) {
            featureDO.setPoliceKind(feature.getPoliceKind().getCode());
        }
        if (feature.getMainObject() != null) {
            featureDO.setMainObjectCode(feature.getMainObject().getCode());
        }

        featureDO.setProcessOrder(feature.getProcessOrder());
        featureDO.setProcessOrderSnapshot(feature.getProcessOrderSnapshot());
        featureDO.setMainObjectField(Objects.isNull(feature.getMainObjectField()) ? null : JSON.toJSONString(feature.getMainObjectField()));
        return featureDO;
    }
}