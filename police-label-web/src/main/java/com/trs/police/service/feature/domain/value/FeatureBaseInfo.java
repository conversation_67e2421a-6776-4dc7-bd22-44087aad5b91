package com.trs.police.service.feature.domain.value;

import com.trs.police.service.shared.dict.DictReference;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 特征基本信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FeatureBaseInfo {

    private String featureName;

    private String description;

    private DictReference categoryCode;

    private DictReference policeKind;

    private DictReference mainObject;

    private String mainObjectFieldEnName;

    private Filed mainObjectField;
}
