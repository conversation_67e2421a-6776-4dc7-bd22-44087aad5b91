package com.trs.police.service.node.formula.impl;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.node.Operator;
import com.trs.police.service.node.formula.Expression;
import com.trs.police.service.node.formula.FormulaContext;
import com.trs.police.service.node.formula.OperationFactory;

/**
 * 二元运算符表达式
 *
 * <AUTHOR>
 */
public class BinaryOperationExpression implements Expression {
    private final Expression left;
    private final Operator operator;
    private final Expression right;

    public BinaryOperationExpression(Expression left, Operator operator, Expression right) {
        this.left = left;
        this.operator = operator;
        this.right = right;
    }

    @Override
    public FieldValue evaluate(FormulaContext context) {
        FieldValue leftValue = left.evaluate(context);
        FieldValue rightValue = right.evaluate(context);

        // 这里应该通过工厂获取实际的计算逻辑
        return OperationFactory.calculate(leftValue, operator, rightValue);
    }
}
