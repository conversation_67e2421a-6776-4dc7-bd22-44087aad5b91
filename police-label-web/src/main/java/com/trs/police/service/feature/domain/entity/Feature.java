package com.trs.police.service.feature.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.police.service.feature.domain.value.FeatureBaseInfo;
import com.trs.police.service.feature.domain.value.Filed;
import com.trs.police.service.shared.dict.DictReference;
import com.trs.police.service.shared.field.FieldReference;
import com.trs.police.service.shared.table.TableReference;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 特征领域实体
 */
@Data
@NoArgsConstructor
public class Feature {

    /**
     * 特征ID
     */
    private Long featureId;

    /**
     * 特征名称
     */
    private String featureName;

    /**
     * 特征描述
     */
    private String description;

    /**
     * 所属分类
     */
    private DictReference category;

    /**
     * 所属警种
     */
    private DictReference policeKind;

    /**
     * 业务规则描述
     */
    private String businessRule;

    /**
     * 选用的表格ID
     */
    private TableReference table;

    /**
     * 输出字段（JSON结构，包括特征主体、对象类型等）
     */
    private String outputFields;

    /**
     * 颜色
     */
    private String color;

    /**
     * 状态 0=停用 1=启用
     */
    private Integer status;

    /**
     * 标签数量
     */
    private Integer labelCount = 0;

    /**
     * 是否已删除 0=未删除 1=已删除
     */
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 创建用户id
     */
    private Long createDeptId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 特征主体
     */
    private DictReference mainObject;

    /**
     * 引用的字段(表格输入字段)
     */
    private List<FieldReference> inputFiledReferences;

    /**
     * 流程编排信息
     */
    private String processOrder;

    /**
     * 特征主题关联的字段
     */
    private Filed mainObjectField;

    private String processOrderSnapshot;

    /**
     * 更新标签数量
     *
     * @param labelCount 标签数量
     */
    public void updateLabelCount(Integer labelCount) {
        if (this.deleted == 1) {
            throw new IllegalArgumentException("特征不存在");
        }
        this.labelCount = labelCount;
    }

    /**
     * 切换特征状态
     *
     * @param status 状态
     */
    public void toggleStatus(Integer status) {
        if (this.deleted == 1) {
            throw new IllegalArgumentException("特征不存在");
        }
        if (!canBeModified()) {
            throw new IllegalArgumentException("该特征已关联标签，无法修改状态");
        }
        this.status = status;
    }

    /**
     * 删除特征
     */
    public void delete() {
        if (this.deleted == 1) {
            throw new IllegalArgumentException("特征不存在");
        }
        if (!canBeDeleted()) {
            throw new IllegalArgumentException("该特征已关联标签，无法删除");
        }
        this.deleted = 1;
    }

    /**
     * 检查特征是否可以被删除
     * 仅当特征无标签关联时可删除
     *
     * @return 是否可以删除
     */
    @JsonIgnore
    public boolean canBeDeleted() {
        return canBeModified();
    }

    /**
     * 判断特征是否可以被修改
     * <p>
     * 逻辑说明：
     * - 如果关联标签数量为 null 或等于 0，则返回 true，表示可以修改。
     * - 否则返回 false，表示不能修改。
     *
     * @return true 如果可以修改；false 如果不能修改
     */
    public boolean canBeModified() {
        if (this.deleted == 1) {
            throw new IllegalArgumentException("特征不存在");
        }
        return labelCount == 0;
    }

    /**
     * 更新流程编排信息
     *
     * @param processOrderJson 流程编排信息
     * @param manual 是否是人工点击
     */
    public void updateProcess(String processOrderJson, Boolean manual) {
        if (Boolean.TRUE.equals(manual)) {
            this.processOrder = processOrderJson;
            this.processOrderSnapshot = null;
        } else {
            this.processOrderSnapshot = processOrderJson;
        }
    }

    /**
     * 根据输出节点配置更新特征信息
     *
     * @param baseInfo 输出节点的配置信息
     */
    public void updateByProcess(FeatureBaseInfo baseInfo) {
        if (Objects.nonNull(baseInfo)) {
            this.featureName = baseInfo.getFeatureName();
            this.description = baseInfo.getDescription();
            this.policeKind = baseInfo.getPoliceKind();
            this.category = baseInfo.getCategoryCode();
            this.mainObject = baseInfo.getMainObject();
            this.mainObjectField = baseInfo.getMainObjectField();
        }
    }
}
