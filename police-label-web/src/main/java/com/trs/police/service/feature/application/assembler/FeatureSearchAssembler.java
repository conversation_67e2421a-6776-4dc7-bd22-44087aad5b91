package com.trs.police.service.feature.application.assembler;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.utils.TreeUtils;
import com.trs.police.common.core.utils.VoParameterConstructor;
import com.trs.police.common.core.vo.CountVO;
import com.trs.police.service.feature.application.DTO.vo.*;
import com.trs.police.service.feature.domain.value.FeatureCategoryCount;
import com.trs.police.service.feature.domain.value.FeatureCategoryStatisticDTO;
import com.trs.police.service.shared.dict.DictFacade;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureSearchDTO;
import com.trs.police.service.feature.domain.entity.Feature;
import com.trs.police.service.feature.domain.value.FeatureSearch;
import com.trs.police.service.shared.dict.DictItemDTO;
import com.trs.police.service.shared.dict.DictTypeRegistry;
import com.trs.police.service.shared.feature.FeatureFacade;
import com.trs.police.service.shared.label.LabelFacade;
import com.trs.police.service.shared.table.TableItemDTO;
import com.trs.police.service.shared.table.TableReference;
import com.trs.police.service.shared.table.TableFacade;
import com.trs.police.service.shared.user.CurrentUserDTO;
import com.trs.police.service.shared.user.UserDeptReference;
import com.trs.police.service.shared.user.UserFacade;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 特征搜索参数转换器
 * 负责在应用层DTO和领域层值对象之间进行转换
 */
@Component
public class FeatureSearchAssembler {

    @Autowired
    private DictFacade dictFacade;

    @Autowired
    private TableFacade tableFacade;

    @Autowired
    private DictTypeRegistry dictTypeRegistry;

    @Autowired
    private LabelFacade labelFacade;

    @Autowired
    private UserFacade userFacade;

    @Autowired
    private FeatureFacade featureFacade;

    /**
     * 将DTO转换为领域值对象
     *
     * @param dto 特征搜索DTO
     * @return 特征搜索值对象
     */
    public FeatureSearch toDomain(FeatureSearchDTO dto) {
        if (dto == null) {
            return null;
        }

        return FeatureSearch.builder()
                .tableName(dto.getTableName())
                .featureName(dto.getFeatureName())
                .categoryCode(dto.getCategoryCode())
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .policeKind(dto.getPoliceKind())
                .tableId(dto.getTableId())
                .featureSubject(dto.getFeatureSubject())
                .mainObjectCode(dto.getMainObjectCode())
                .tableInputFieldIds(dto.getTableInputFieldIds())
                .searchValue(dto.getSearchValue())
                .orderList(dto.getOrderList())
                .pageNum(dto.getPageNum())
                .pageSize(dto.getPageSize())
                .dataSourceTableId(dto.getDataSourceTableId())
                .build();
    }

    /**
     * 将领域值对象转换为VO
     *
     * @param featureList 特征列表
     * @return 特征VO列表
     */
    public List<FeatureVO> toVO(List<Feature> featureList) {
        if (featureList == null) {
            return null;
        }
        List<FeatureVO> voList = featureList.stream()
                .map(feature -> {
                    FeatureVO vo = new FeatureVO();
                    vo.setFeatureId(feature.getFeatureId());
                    vo.setFeatureName(feature.getFeatureName());
                    vo.setDescription(feature.getDescription());
                    vo.setCategoryCode(feature.getCategory().getCode());
                    vo.setPoliceKind(feature.getPoliceKind().getCode());
                    vo.setBusinessRule(feature.getBusinessRule());
                    vo.setTableId(feature.getTable().getTableId());
                    vo.setOutputFields(feature.getOutputFields());
                    vo.setColor(feature.getColor());
                    vo.setStatus(feature.getStatus());
                    if (Objects.nonNull(feature.getCreateTime())) {
                        vo.setCreateTime(TimeUtil.localDateTimeToString(feature.getCreateTime(), null));
                    }
                    if (Objects.nonNull(feature.getUpdateTime())) {
                        vo.setUpdateTime(TimeUtil.localDateTimeToString(feature.getUpdateTime(), null));
                    }
                    vo.setCreateUserId(feature.getCreateUserId());
                    vo.setCreateDeptId(feature.getCreateDeptId());
                    vo.setMainObjectCode(feature.getMainObject().getCode());
                    if (Objects.nonNull(feature.getMainObjectField())) {
                        vo.setMainObjectField(
                                FiledVO.builder()
                                .fromNode(feature.getMainObjectField().getFromNode())
                                .cnName(feature.getMainObjectField().getCnName())
                                .enName(feature.getMainObjectField().getEnName())
                                .typeCode(feature.getMainObjectField().getTypeCode())
                                .build()
                        );
                    }
                    return vo;
                })
                .collect(Collectors.toList());
        List<Long> tid = featureList.stream()
                .map(Feature::getTable)
                .map(TableReference::getTableId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<TableItemDTO> tbs = tableFacade.findByIds(tid);
        // 获取到被标签使用情况
        List<Long> ids = featureList.stream()
                .map(Feature::getFeatureId)
                .collect(Collectors.toList());
        final List<CountVO> useStatistic = labelFacade.featureUseStatistic(ids);

        // 表格特征使用数量
        List<TableReference> tbRf = featureList.stream()
                .map(f -> f.getTable())
                .distinct()
                .collect(Collectors.toList());
        final List<CountVO> tbFeatureUseCounts = featureFacade.tableUseStatistic(tbRf);

        List<UserDeptReference> userDeptReferenceList = voList.stream()
                .map(u -> new UserDeptReference(u.getCreateUserId(), u.getCreateDeptId()))
                .collect(Collectors.toList());
        final List<CurrentUserDTO> users = userFacade.findCurrentUsers(userDeptReferenceList);


        VoParameterConstructor.of(voList)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getFeatureCategory()),
                        (v, d) -> Objects.equals(v.getCategoryCode(), d.getCode())
                )
                .consumer(FeatureVO::setCategoryDict)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getPoliceKind()),
                        (v, d) -> Objects.equals(v.getPoliceKind(), d.getCode())
                )
                .consumer(FeatureVO::setPoliceKindDict)
                .singleValueMatcher(
                        vs -> tbs,
                        (v, d) -> Objects.equals(v.getTableId(), d.getTableId())
                )
                .mapAndConsumer(FeatureTableItemDTO::new, FeatureVO::setTableInfo)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getFeatureMainObject()),
                        (v, d) -> Objects.equals(v.getMainObjectCode(), d.getCode())
                )
                .consumer(FeatureVO::setMainObjectDict)
                .singleValueMatcher(
                        vs -> useStatistic,
                        (v, d) -> Objects.equals(v.getFeatureId().toString(), d.getId())
                )
                .consumer((v, d) -> v.setLabelCount(d.getCount()))
                .singleValueMatcher(d -> users, (lb, u) -> u.equals(new CurrentUserDTO(lb.getCreateUserId(), lb.getCreateDeptId())))
                .consumer((v, u) -> {
                    v.setCreateUser(u);
                    v.setCreateUserName(u.getUser().getUserRealName());
                })
                .singleValueMatcher(v -> tbFeatureUseCounts, (v, c) -> Objects.equals(String.valueOf(v.getTableId()), c.getId()))
                .consumer((v, d) -> Optional.ofNullable(v.getTableInfo()).ifPresent(i -> i.setFeatureCount(d.getCount())))
                .build();
        return voList;
    }

    /**
     * 将领域值对象转换为VO
     *
     * @param featureList 特征列表
     * @return 特征VO列表
     */
    public List<FeatureDetailVO> toDetailVO(List<Feature> featureList) {
        if (featureList == null) {
            return null;
        }
        List<FeatureDetailVO> voList = featureList.stream()
                .map(feature -> {
                    FeatureDetailVO vo = new FeatureDetailVO();
                    vo.setFeatureId(feature.getFeatureId());
                    vo.setFeatureName(feature.getFeatureName());
                    vo.setDescription(feature.getDescription());
                    vo.setCategoryCode(feature.getCategory().getCode());
                    vo.setPoliceKind(feature.getPoliceKind().getCode());
                    vo.setBusinessRule(feature.getBusinessRule());
                    vo.setTableId(feature.getTable().getTableId());
                    vo.setOutputFields(feature.getOutputFields());
                    vo.setColor(feature.getColor());
                    vo.setStatus(feature.getStatus());
                    if (Objects.nonNull(feature.getCreateTime())) {
                        vo.setCreateTime(TimeUtil.localDateTimeToString(feature.getCreateTime(), null));
                    }
                    if (Objects.nonNull(feature.getUpdateTime())) {
                        vo.setUpdateTime(TimeUtil.localDateTimeToString(feature.getUpdateTime(), null));
                    }
                    vo.setCreateUserId(feature.getCreateUserId());
                    vo.setMainObjectCode(feature.getMainObject().getCode());
                    FeatureProcessDTO order = JSON.parseObject(feature.getProcessOrder(), FeatureProcessDTO.class);
                    order.getInfo().setFeatureId(feature.getFeatureId());
                    vo.setProcessOrder(JSON.parseObject(JSON.toJSONString(order)));
                    if (StringUtils.isNotEmpty(feature.getProcessOrderSnapshot())) {
                        FeatureProcessDTO orderS = JSON.parseObject(feature.getProcessOrderSnapshot(), FeatureProcessDTO.class);
                        orderS.getInfo().setFeatureId(feature.getFeatureId());
                        vo.setProcessOrderSnapshot(JSON.parseObject(JSON.toJSONString(orderS)));
                    }
                    return vo;
                })
                .collect(Collectors.toList());
        List<Long> tid = featureList.stream()
                .map(Feature::getTable)
                .map(TableReference::getTableId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 获取到表格被使用情况
        final List<TableItemDTO> tbs = tableFacade.findByIds(tid);
        // 获取到被标签使用情况
        List<Long> ids = featureList.stream()
                .map(Feature::getFeatureId)
                .collect(Collectors.toList());
        final List<CountVO> useStatistic = labelFacade.featureUseStatistic(ids);

        // 表格特征使用数量
        List<TableReference> tbRf = featureList.stream()
                .map(f -> f.getTable())
                .distinct()
                .collect(Collectors.toList());
        final List<CountVO> tbFeatureUseCounts = featureFacade.tableUseStatistic(tbRf);

        VoParameterConstructor.of(voList)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getFeatureCategory()),
                        (v, d) -> Objects.equals(v.getCategoryCode(), d.getCode())
                )
                .consumer(FeatureDetailVO::setCategoryDict)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getPoliceKind()),
                        (v, d) -> Objects.equals(v.getPoliceKind(), d.getCode())
                )
                .consumer(FeatureDetailVO::setPoliceKindDict)
                .singleValueMatcher(
                        vs -> tbs,
                        (v, d) -> Objects.equals(v.getTableId(), d.getTableId())
                )
                .mapAndConsumer(FeatureTableItemDTO::new, FeatureDetailVO::setTableInfo)
                .singleValueMatcher(
                        vs -> dictFacade.getDicts(dictTypeRegistry.getFeatureMainObject()),
                        (v, d) -> Objects.equals(v.getMainObjectCode(), d.getCode())
                )
                .consumer(FeatureDetailVO::setMainObjectDict)
                .singleValueMatcher(
                        vs -> useStatistic,
                        (v, d) -> Objects.equals(v.getFeatureId().toString(), d.getId())
                )
                .consumer((v, d) -> v.setLabelCount(d.getCount()))
                .singleValueMatcher(v -> tbFeatureUseCounts, (v, c) -> Objects.equals(String.valueOf(v.getTableId()), c.getId()))
                .consumer((v, d) -> Optional.ofNullable(v.getTableInfo()).ifPresent(i -> i.setFeatureCount(d.getCount())))
                .build();
        return voList;
    }

    /**
     * 转换为VO
     *
     * @param categoryCounts 分类统计
     * @return 分类统计结果
     */
    public List<FeatureCategoryStatisticVO> toCategoryStatisticVO(List<FeatureCategoryCount> categoryCounts) {
        List<DictItemDTO> ds = dictFacade.getDicts(dictTypeRegistry.getFeatureCategory());
        // 构造vo
        List<FeatureCategoryStatisticVO> vos = ds.stream()
                .map(dict -> {
                    FeatureCategoryStatisticVO vo = new FeatureCategoryStatisticVO();
                    BeanUtils.copyProperties(dict, vo);
                    vo.setId(dict.getId().intValue());
                    vo.setCount(0);
                    return vo;
                })
                .collect(Collectors.toList());
        // 统计数量
        VoParameterConstructor
                .of(vos)
                .singleValueMatcher(vs -> categoryCounts, (vo, cc) -> vo.getCode().equals(cc.getCategoryCode()))
                .consumer((vo, cc) -> vo.setCount(cc.getCount()))
                .build();
        // 构造树
        List<FeatureCategoryStatisticVO> build = TreeUtils.treeBuilder(vos)
                .build();
        // 设置层级
        setLevel(build, 1);
        // 虚拟一个根节点
        FeatureCategoryStatisticVO root = buildRoot(build);
        // 重新计数，加上子节点数量
        addChildCount(root);
        return Arrays.asList(root);
    }

    /**
     * 转换成DTO
     *
     * @param dto 如参
     * @return dto
     */
    public FeatureCategoryStatisticDTO toCategoryStatisticDTO(com.trs.police.service.feature.application.DTO.query.FeatureCategoryStatisticDTO  dto) {
        FeatureCategoryStatisticDTO r = new FeatureCategoryStatisticDTO();
        r.setPoliceKind(dto.getPoliceKind());
        r.setCategoryCode(dto.getCategoryCode());
        r.setMainObjectCode(dto.getMainObjectCode());
        r.setSearchValue(dto.getSearchValue());
        r.setDataSourceTableId(dto.getDataSourceTableId());
        return r;
    }

    private void setLevel(List<FeatureCategoryStatisticVO> vos, Integer level) {
        for (FeatureCategoryStatisticVO vo : vos) {
            vo.setLevel(level);
            if (vo.getChildren() != null) {
                setLevel(vo.getChildren(), level + 1);
            }
        }
    }

    private void addChildCount(FeatureCategoryStatisticVO vo) {
        if (vo.getChildren() != null) {
            for (FeatureCategoryStatisticVO child : vo.getChildren()) {
                addChildCount(child);
            }
            vo.setCount(vo.getChildren().stream().mapToInt(FeatureCategoryStatisticVO::getCount).sum() + vo.getCount());
        }
    }

    private FeatureCategoryStatisticVO buildRoot(List<FeatureCategoryStatisticVO> vos) {
        FeatureCategoryStatisticVO vo = new FeatureCategoryStatisticVO();
        vo.setId(null);
        vo.setPid(null);
        vo.setName("全部");
        vo.setLevel(0);
        vo.setChildren(vos);
        vo.setCount(0);
        return vo;
    }
}
