package com.trs.police.service.label;

import com.trs.police.entity.label.LabelCalculationTaskDO;
import com.trs.police.vo.LabelComputeStatusVO;

import java.util.List;

/**
 * 标签计算服务
 */
public interface LabelCalculationService {

    /**
     * 手动触发标签计算
     *
     * @param labelId 标签ID
     * @return 计算任务ID
     */
    String triggerManualCalculation(Long labelId);

    /**
     * 定时触发标签计算
     *
     * @param labelId 标签ID
     * @return 计算任务ID
     */
    String triggerScheduledCalculation(Long labelId);

    /**
     * 获取计算任务状态
     *
     * @param labelId 任务ID
     * @return 任务状态
     */
    LabelComputeStatusVO getTaskStatus(Long labelId);

    /**
     * 取消计算任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelTask(String taskId);

    /**
     * 获取标签的计算任务历史
     *
     * @param labelId 标签ID
     * @param limit 限制数量
     * @return 任务历史列表
     */
    List<LabelCalculationTaskDO> getCalculationHistory(Long labelId, Integer limit);

    /**
     * 获取所有需要定时计算的标签
     *
     * @return 标签ID列表
     */
    List<Long> getScheduledLabels();

    /**
     * 检查标签是否需要执行计算
     *
     * @param labelId 标签ID
     * @return 是否需要计算
     */
    boolean shouldCalculate(Long labelId);
}