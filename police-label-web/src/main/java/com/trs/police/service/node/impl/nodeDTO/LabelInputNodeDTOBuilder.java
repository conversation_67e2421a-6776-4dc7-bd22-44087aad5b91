package com.trs.police.service.node.impl.nodeDTO;

import com.alibaba.fastjson.JSON;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.label.LabelNodeDTO;
import com.trs.police.dto.label.node.LabelProcessDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.properties.LabelInputProperties;
import com.trs.police.entity.label.LabelDO;
import com.trs.police.mapper.LabelMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签输入节点
 */
@Component
public class LabelInputNodeDTOBuilder extends LabelNodeDTOBuilder {
    @Autowired
    private LabelMapper labelMapper;

    @Override
    public boolean supports(Integer nodeTypeCode) {
        return NodeType.LABEL_INPUT.equals(nodeTypeCode);
    }

    @Override
    public List<LabelNodeDTO> build(NodeDTO nodeDTO, NodeContext nodeContext) {
        LabelInputProperties property = JSON.parseObject(nodeDTO.getNodeProperties(), LabelInputProperties.class);
        LabelDO labelDO = labelMapper.selectById(property.getLabelId());
        LabelProcessDTO labelProcessDTO = JSON.parseObject(labelDO.getProcessOrder(), LabelProcessDTO.class);
        LabelNodeDTOBuilderFactory labelNodeDtoBuilderFactory = BeanFactoryHolder.getBean(LabelNodeDTOBuilderFactory.class).get();
        List<NodeDTO> sortedNodes = labelNodeDtoBuilderFactory.sortNodes(labelProcessDTO.getNodes(), labelProcessDTO.getNodeOrders());
        addControlValue(labelProcessDTO.getControl(), property.getControlValue(), nodeContext);
        return sortedNodes.stream().map(dto -> labelNodeDtoBuilderFactory.buildByStrategy(dto, nodeContext)).flatMap(List::stream).collect(Collectors.toList());
    }
} 