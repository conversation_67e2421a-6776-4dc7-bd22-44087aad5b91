package com.trs.police.service.node;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.dto.node.properties.NodeProperties;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ValueMateBase;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 流程节点
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class Node {

    /**
     * 节点信息
     */
    protected NodeMeta nodeMeta;

    /**
     * 节点属性 json字符串
     */
    protected String nodeProperties;

    public Node(NodeMeta nodeMeta, String nodeProperties) {
        this.nodeMeta = nodeMeta;
        this.nodeProperties = nodeProperties;
    }


    /**
     * 节点类型 {@link NodeType}
     *
     * @return 节点类型
     */
    public abstract Integer nodeType();

    /**
     * 是否是输入节点
     *
     * @return 布尔
     */
    public Boolean isInputNode() {
        return NodeType.TABLE.equals(nodeMeta.getNodeTypeCode());
    }

    /**
     * 根据输入数据获取到输出数据（注意不能有环路，目前系统未做判断
     *
     * @param inputNode 输入节点
     * @param context 上下文
     * @return 数据
     */
    protected abstract NodeData process(List<NodeData> inputNode, NodeContext context);


    /**
     * 根据输入数据获取到输出数据（注意不能有环路，目前系统未做判断)
     *
     * @param inputNode 输入节点
     * @param context 上下文信息
     * @return 数据
     */
    public NodeData output(List<NodeData> inputNode, NodeContext context) {
        long start = System.currentTimeMillis();
        NodeData nodeData = process(inputNode, context);
        log.info("节点：{}-{}耗时{}秒", nodeMeta.getName(), nodeMeta.getUuid(), (System.currentTimeMillis() - start) * 1.0 / 1000);


        // 根据过滤参数过滤出字段
        Set<String> set = nodeMeta.getOutputRowMeta().getValueMetaList()
                .stream()
                .map(ValueMateBase::getId)
                .collect(Collectors.toSet());

        // 表头
        List<FieldInfoVO> header = nodeData.getHeader()
                .stream()
                .filter(h -> set.contains(h.getId()))
                .collect(Collectors.toList());
        nodeData.setHeader(header);

        // 数据
        List<List<FieldValue>> data = nodeData.getData()
                .stream()
                .map(row -> {
                    return row.stream()
                            .filter(c -> set.contains(c.getId()))
                            .collect(Collectors.toList());
                })
                .collect(Collectors.toList());
        nodeData.setData(data);
        return nodeData;
    }

    /**
     * 获取属性
     *
     * @param clazz 类型
     * @param <T> 属性
     * @return 属性
     */
    public <T extends NodeProperties> T getPropertyAs(Class<T> clazz) {
        return JSONObject.parseObject(nodeProperties, clazz);
    }

    /**
     * 查找节点
     *
     * @param inputNode 输入节点
     * @param uuid uuid
     * @return 节点
     */
    protected Optional<NodeData> findNode(List<NodeData> inputNode, String uuid) {
        return inputNode.stream()
                .filter(n -> n.getNodeMeta().getUuid().equals(uuid))
                .findFirst();
    }
}
