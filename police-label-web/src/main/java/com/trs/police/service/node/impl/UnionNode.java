package com.trs.police.service.node.impl;

import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.common.core.vo.node.Row;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ValueMateBase;
import com.trs.police.dto.node.properties.RelevancyNodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;
import com.trs.police.service.node.rowmatch.MatchResult;
import com.trs.police.service.node.rowmatch.RowMatcher;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 并集
 *
 * <AUTHOR>
 */
@Slf4j
public class UnionNode extends Node {

    public UnionNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        RelevancyNodeProperties property = getPropertyAs(RelevancyNodeProperties.class);
        Optional<NodeData> nodeA = findNode(inputNode, property.getTableA());
        Optional<NodeData> nodeB = findNode(inputNode, property.getTableB());
        if (nodeA.isEmpty() || nodeB.isEmpty()) {
            throw new RuntimeException("关联节点未找到");
        }

        // 生成新的数据

        List<FieldInfoVO> header = new ArrayList<>();
        header.addAll(nodeA.get().getHeader());
        header.addAll(nodeB.get().getHeader());


        // 构造结果
        NodeData result = new NodeData();
        result.setNodeMeta(nodeMeta);
        result.setHeader(header);

        // 不去重的并集
        if (Integer.valueOf(2).equals(property.getType())) {
            List<Row> data = getData(nodeA.get(), nodeB.get());
            result.setRowList(data);
            result.setTotalCount(Integer.valueOf(data.size()).longValue());
        }
        // 去重的并集
        if (Integer.valueOf(3).equals(property.getType())) {
            List<Row> data = getDataDistrinct(nodeA.get(), nodeB.get(), property);
            result.setRowList(data);
            result.setTotalCount(Integer.valueOf(data.size()).longValue());
        }
        return result;
    }

    @Override
    public Integer nodeType() {
        throw new RuntimeException("交集节点仅限内部调用");
    }

    // 获取不去重的并集
    private List<Row> getData(NodeData a, NodeData b) {
        List<Row> result = new ArrayList<>();
        // 添加a的所有数据
        List<SameRowIndexMapping> indexMappings = matchSameField(a.getHeader(), b.getHeader());
        for (Row row : a.getRowList()) {
            List<FieldValue> rowData = buildSingleEmptyData(b.getHeader()).getRowData();
            // 拷贝相同字段
            Row rowb = copyValue(row, new Row(rowData), indexMappings);
            result.add(newRow(row, rowb));
        }
        // 添加b的所有数据
        List<SameRowIndexMapping> bToA = matchSameField(b.getHeader(), a.getHeader());
        for (Row row : b.getRowList()) {
            List<FieldValue> rowData = buildSingleEmptyData(a.getHeader()).getRowData();
            // 拷贝相同字段
            Row rowa = copyValue(row, new Row(rowData), bToA);
            result.add(newRow(rowa, row));
        }
        return result;
    }


    // 英文名称相同的列的下标映射
    private List<SameRowIndexMapping> matchSameField(List<FieldInfoVO> headerA, List<FieldInfoVO> headerB) {
        List<SameRowIndexMapping> result = new ArrayList<>();
        for (int i = 0; i < headerA.size(); i++) {
            for (int i1 = 0; i1 < headerB.size(); i1++) {
                FieldInfoVO fieldInfoVO = headerA.get(i);
                FieldInfoVO f = headerB.get(i1);
                if (fieldInfoVO.getEnName().equals(f.getEnName())) {
                    result.add(new SameRowIndexMapping(i, i1));
                }
            }
        }
        return result;
    }

    private Row copyValue(Row a, Row b, List<SameRowIndexMapping> indexMappings) {
        indexMappings.forEach(map -> {
            b.getRowData().get(map.getTo()).setValue(a.getRowData().get(map.getFrom()).getValue());
        });
        return b;
    }

    private Row newRow(Row a, Row b) {
        List<FieldValue> newRow = new ArrayList<>();
        newRow.addAll(a.getRowData());
        newRow.addAll(b.getRowData());
        return new Row(newRow);
    }



    // 去重的并集
    private List<Row> getDataDistrinct(NodeData a, NodeData b, RelevancyNodeProperties property) {
        // 所有匹配的数据
        List<MatchResult> match = RowMatcher.match(a, b, property.getMatch());
        // 构造数据
        List<Row> result = new ArrayList<>();

        // a中没有匹配的数据
        List<SameRowIndexMapping> amb = matchSameField(a.getHeader(), b.getHeader());
        List<Row> newA = match
                .stream()
                .filter(row -> row.getMatched().isEmpty())
                .map(MatchResult::getFrom)
                .map(row -> {
                    Row be = buildSingleEmptyData(b.getHeader());
                    copyValue(row, be, amb);
                    return mergeRow(row, be);
                })
                .collect(Collectors.toList());
        result.addAll(newA);

        // b中没有匹配到的数据
        List<SameRowIndexMapping> bma = matchSameField(b.getHeader(), a.getHeader());
        List<Row> bMatch = match.stream()
                .map(MatchResult::getMatched)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        List<Row> newB = b.getRowList()
                .stream()
                .filter(row -> !bMatch.contains(row))
                .map(row -> {
                    Row ae = buildSingleEmptyData(a.getHeader());
                    copyValue(row, ae, bma);
                    return mergeRow(ae, row);
                })
                .collect(Collectors.toList());
        result.addAll(newB);

        // 交集，根据策略取数据
        List<Row> intersection = getIntersection(a, b, property, match);
        result.addAll(intersection);
        return result;
    }

    private Row buildSingleEmptyData(List<FieldInfoVO> header) {
        return new Row(buildEmptyData(header, 1).get(0));
    }

    private List<List<FieldValue>> buildEmptyData(List<FieldInfoVO> header, Integer size) {
        List<List<FieldValue>> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<FieldValue> row = new ArrayList<>();
            for (FieldInfoVO fieldInfoVO : header) {
                row.add(new FieldValue("", fieldInfoVO));
            }
            result.add(row);
        }
        return result;
    }

    private Row mergeRow(Row a, Row b) {
        List<FieldValue> newRow = new ArrayList<>();
        newRow.addAll(a.getRowData());
        newRow.addAll(b.getRowData());
        return new Row(newRow);
    }

    // 公共部分根据策略去重复
    private List<Row> getIntersection(NodeData a, NodeData b, RelevancyNodeProperties property, List<MatchResult> match) {
        // 选a或者选b策略
        if (Integer.valueOf(1).equals(property.getWeight()) || Integer.valueOf(2).equals(property.getWeight())) {
            // 选a
            if (Integer.valueOf(1).equals(property.getWeight())) {
                List<SameRowIndexMapping> aMatch = matchSameField(a.getHeader(), b.getHeader());
                return match.stream()
                        .filter(m -> !m.getMatched().isEmpty())
                        .map(m ->
                                Stream.of(m)
                                        .map(row -> {
                                            // 按a、b节点对应字段顺序返回
                                            Row fieldValues = buildSingleEmptyData(b.getHeader());
                                            copyValue(row.getFrom(), fieldValues, aMatch);
                                            return mergeRow(row.getFrom(), fieldValues);
                                        })
                                        .collect(Collectors.toList()))
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
            }
            // 选b
            Map<Row, List<Row>> bMatch = new HashMap<>();
            for (MatchResult matchResult : match) {
                if (CollectionUtils.isNotEmpty(matchResult.getMatched())) {
                    for (Row rowB : matchResult.getMatched()) {
                        if (CollectionUtils.isEmpty(bMatch.get(rowB))) {
                            bMatch.put(rowB, new ArrayList<>());
                        }
                        bMatch.get(rowB).add(matchResult.getFrom());
                    }
                }
            }
            List<MatchResult> bMatchResult = bMatch.entrySet()
                    .stream()
                    .map(en -> new MatchResult(en.getKey(), en.getValue()))
                    .collect(Collectors.toList());
            List<SameRowIndexMapping> indexMappings = matchSameField(b.getHeader(), a.getHeader());
            return bMatchResult.stream()
                    .map(m ->
                            Stream.of(m)
                                    .map(row -> {
                                        // 按a、b节点对应字段顺序返回
                                        Row fieldValues = buildSingleEmptyData(a.getHeader());
                                        copyValue(row.getFrom(), fieldValues, indexMappings);
                                        return mergeRow(fieldValues, row.getFrom());
                                    })
                                    .collect(Collectors.toList()))
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        }

        // 比较后选大还是选小策略
        if (Integer.valueOf(3).equals(property.getWeight()) || Integer.valueOf(4).equals(property.getWeight())) {
            // 比较规则
            Comparator<Row> comparator = (o1, o2) -> {
                for (RelevancyNodeProperties.CompareFieldConfig config : property.getCompareField()) {
                    if (a == null || b == null) {
                        continue;
                    }
                    ValueMateBase a1 = config.getA();
                    ValueMateBase b1 = config.getB();
                    Comparator<Row> comparable = Row.getComparable(
                            new FieldInfoVO(a1.getCnName(), a1.getEnName(), a1.getTypeCode(), a1.getFromNode()),
                            new FieldInfoVO(b1.getCnName(), b1.getEnName(), b1.getTypeCode(), b1.getFromNode())
                    );
                    int compare1 = comparable.compare(o1, o2);
                    if (compare1 != 0) {
                        return compare1;
                    }
                }
                return 0;
            };
            // 执行比较 返回去重后的数据
            List<SameRowIndexMapping> amb = matchSameField(a.getHeader(), b.getHeader());
            List<SameRowIndexMapping> bma = matchSameField(b.getHeader(), a.getHeader());
            List<Row> collect = match.stream()
                    .filter(m -> !m.getMatched().isEmpty())
                    .map(m -> {
                        Optional<Row> bMatch = m.getMatched()
                                .stream()
                                .max((r1, r2) -> {
                                    int compare = comparator.compare(r1, r2);
                                    return Integer.valueOf(3).equals(property.getWeight()) ? compare : -compare;
                                });
                        // 根据方向决定返回
                        if (comparator.compare(m.getFrom(), bMatch.get()) >= 0) {
                            // 取大
                            return (Integer.valueOf(3).equals(property.getWeight()))
                                    ? mergeRow(m.getFrom(), copyValue(m.getFrom(), buildSingleEmptyData(b.getHeader()), amb))
                                    : mergeRow(copyValue(bMatch.get(), buildSingleEmptyData(a.getHeader()), bma), bMatch.get());
                        } else {
                            // 取小
                            return (Integer.valueOf(3).equals(property.getWeight())
                                    ? mergeRow(copyValue(bMatch.get(), buildSingleEmptyData(a.getHeader()), bma), bMatch.get())
                                    : mergeRow(m.getFrom(), copyValue(m.getFrom(), buildSingleEmptyData(b.getHeader()), amb)));
                        }
                    })
                    .collect(Collectors.toList());
            return collect;
        }
        return new ArrayList<>();
    }

    @Getter
    private static class SameRowIndexMapping {
        private Integer from;

        private Integer to;

        public SameRowIndexMapping(Integer from, Integer to) {
            this.from = from;
            this.to = to;
        }
    }
}
