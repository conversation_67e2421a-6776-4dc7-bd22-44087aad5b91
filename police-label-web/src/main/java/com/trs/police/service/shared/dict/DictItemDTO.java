package com.trs.police.service.shared.dict;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 码表数据传输对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DictItemDTO {

    /**
     * id
     */
    private Long id;
    /**
     * 父节点id
     */
    private Long pid;
    /**
     * code
     */
    private Long code;
    /**
     * p_code
     */
    private Long pCode;
    /**
     * 名称
     */
    private String name;
    /**
     * 顺序
     */
    private Integer showNumber;

    /**
     * 码表描述
     */
    private String dictDesc;

    /**
     * 背景色
     */
    private String color;

    /**
     * type
     */
    private String type;

    /**
     * 子节点
     */
    private List<DictItemDTO> children;

    /**
     * 其它标志
     */
    private String flag;
}
