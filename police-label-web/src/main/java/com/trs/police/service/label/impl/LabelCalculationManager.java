package com.trs.police.service.label.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.PreConditionCheck;
import com.trs.police.common.core.utils.OkHttpUtil;
import com.trs.police.dto.label.JobInput;
import com.trs.police.dto.label.LabelComputeDTO;
import com.trs.police.dto.label.node.LabelProcessDTO;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.entity.label.LabelDO;
import com.trs.police.mapper.LabelMapper;
import com.trs.police.service.node.impl.nodeDTO.LabelNodeDTOBuilderFactory;
import com.trs.police.vo.label.SparkJobState;
import com.trs.police.vo.label.SubmitInfoVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
@Component
@Slf4j
public class LabelCalculationManager {

    OkHttpUtil okHttpUtil = OkHttpUtil.getInstance();

    @Autowired
    private LabelMapper labelMapper;

    @Autowired
    private LabelNodeDTOBuilderFactory labelNodeDtoBuilderFactory;

    /**
     * 提交标签计算任务
     *
     * @param labelId 标签ID
     * @param taskId 任务ID
     * @return 提交信息
     */
    public SubmitInfoVo submitLabelCalculation(Long labelId, String taskId){
        JobInput jobInput = new JobInput();
        jobInput.setJobName("标签计算"+taskId);
        jobInput.setMainClass("com.trs.police.engine.controller.LabelEngineMain");
        LabelComputeDTO labelComputeDTO = buildLabelComputeDTO(labelId);
        String inputParams = Try.of(() -> URLEncoder.encode(JSONObject.toJSONString(labelComputeDTO), "utf-8")).getOrElseThrow(e -> new RuntimeException("标签计算参数编码失败"));
        jobInput.setInputParams(inputParams);
        jobInput.setJarFileName("police-label-engine_3.2.4_3.2.2-shaded-0.0.1-SNAPSHOT.jar");
        String url = BeanFactoryHolder.getEnv().getProperty("spark.url.job", "http://10.18.20.131:16082/spark/job");
        String jsonString = JSONObject.toJSONString(jobInput);
        log.info("提交标签计算任务{}: {}", labelId, jsonString);
        String res = okHttpUtil.postData(url, jsonString);
        JSONObject jsonObject = JSONObject.parseObject(res);
        PreConditionCheck.checkArgument(jsonObject.getInteger("code") == 200, jsonObject.getJSONArray("data").getJSONObject(0).getString("message"));
        String data = jsonObject.getJSONArray("data").getString(0);
        return JSONObject.parseObject(data, SubmitInfoVo.class);
    }

    /**
     * 构建标签计算参数
     *
     * @param labelId 标签ID
     * @return 标签计算参数
     */
    public LabelComputeDTO buildLabelComputeDTO(Long labelId) {
        LabelDO labelDO = labelMapper.selectById(labelId);
        LabelComputeDTO labelComputeDTO = new LabelComputeDTO();
        LabelProcessDTO labelProcessDTO = JSON.parseObject(labelDO.getProcessOrder(), LabelProcessDTO.class);
        List<NodeDTO> sortedNodes = labelNodeDtoBuilderFactory.sortNodes(labelProcessDTO.getNodes(), labelProcessDTO.getNodeOrders());
        NodeContext nodeContext = new NodeContext(labelProcessDTO.getControl());
        nodeContext.setNodeOrders(labelProcessDTO.getNodeOrders());
        nodeContext.setNodes(labelProcessDTO.getNodes());
        labelComputeDTO.setLabelNodeDtos(sortedNodes.stream()
                .map(nodeDTO -> labelNodeDtoBuilderFactory.buildByStrategy(nodeDTO, nodeContext))
                .flatMap(List::stream)
                .collect(Collectors.toList()));
        return labelComputeDTO;
    }

    /**
     * 获取任务状态
     *
     * @param jobId 任务ID
     * @return 任务状态
     */
    public SparkJobState getJobState(String jobId) {
        String url = BeanFactoryHolder.getEnv().getProperty("spark.url.jobInfo", "http://10.18.20.131:16082/spark/findJobInfo");
        String res = okHttpUtil.getData(url+"?jobId="+jobId);
        String data = JSONObject.parseObject(res).getJSONArray("data").getString(0);
        JSONObject jsonObject = JSONObject.parseObject(data);
        SparkJobState sparkJobState = new SparkJobState();
        sparkJobState.setJobState(jsonObject.getString("jobState"));
        sparkJobState.setErrorMessage(jsonObject.getString("errorMessage"));

        return sparkJobState;
    }
}
