package com.trs.police.service.node.impl;

import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.dto.node.properties.bean.OrderItem;
import com.trs.police.dto.node.properties.OrderNodeProperties;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.Node;
import com.trs.police.service.node.ValueComparator;

import java.util.Comparator;
import java.util.List;

/**
 * 排序节点
 *
 * <AUTHOR>
 */
public class OrderNode extends Node {

    public OrderNode(NodeMeta nodeMeta, String nodeProperties) {
        super(nodeMeta, nodeProperties);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        OrderNodeProperties propertyAs = getPropertyAs(OrderNodeProperties.class);
        Comparator<List<FieldValue>> comparable = comparable(propertyAs);
        NodeData input = inputNode.get(0);
        List<List<FieldValue>> data = input.getData();
        if (comparable != null) {
            data.sort(comparable);
        }
        NodeData outPut = new NodeData();
        outPut.setNodeMeta(nodeMeta);
        outPut.setTotalCount(input.getTotalCount());
        outPut.setData(data);
        outPut.setHeader(input.getHeader());
        return outPut;
    }

    @Override
    public Integer nodeType() {
        return NodeType.ORDER;
    }


    /**
     * 排序
     *
     * @param property 排序属性
     * @return c
     */
    public static Comparator<List<FieldValue>> comparable(OrderNodeProperties property) {
        return (v1, v2) -> {
            for (OrderItem item : property.getOrderItems()) {
                FieldValue f1 = v1.stream()
                        .filter(f -> f.getId().equals(new FieldInfoVO(item.getEnName(), null, null, item.getFromNode()).getId()))
                        .findFirst()
                        .orElse(null);
                FieldValue f2 = v2.stream()
                        .filter(f -> f.getId().equals(new FieldInfoVO(item.getEnName(), null, null, item.getFromNode()).getId()))
                        .findFirst()
                        .orElse(null);
                if (f1 == null || f2 == null) {
                    continue;
                }
                int compareValues = ValueComparator.compareValues(f1, f2);
                if (compareValues != 0) {
                    return "asc".equalsIgnoreCase(item.getOrder()) ? compareValues : -compareValues;
                }
            }
            return 0;
        };
    }

}
