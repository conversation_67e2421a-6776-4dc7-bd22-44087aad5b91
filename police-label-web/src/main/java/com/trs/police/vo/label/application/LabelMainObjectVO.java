package com.trs.police.vo.label.application;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标签主体
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelMainObjectVO {

    private String enName;

    private String fromNode;

    private Long mainObjectTypeCode;

    public LabelMainObjectVO(String enName, String fromNode, Long mainObjectTypeCode) {
        this.enName = enName;
        this.fromNode = fromNode;
        this.mainObjectTypeCode = mainObjectTypeCode;
    }
}
