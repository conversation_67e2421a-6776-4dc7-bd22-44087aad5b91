package com.trs.police.vo;

import com.trs.common.base.PreConditionCheck;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/4/19 10:13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DictListVO implements Serializable {

    private static final long serialVersionUID = 2251724313832924730L;
    /**
     * id
     */
    private Long id;
    /**
     * 父节点id
     */
    private Long pid;
    /**
     * code
     */
    private Long code;
    /**
     * p_code
     */
    private Long pCode;
    /**
     * 名称
     */
    private String name;
    /**
     * 顺序
     */
    private Integer showNumber;

    /**
     * 码表描述
     */
    private String dictDesc;

    /**
     * 背景色
     */
    private String color;

    /**
     * type
     */
    private String type;

    /**
     * 子节点
     */
    private List<DictListVO> children;

    /**
     * 其它标志
     */
    private String flag;


    private String pName;


    /**
     * 添加子分类
     *
     * @param vo 参数
     * @return 结果
     */
    public DictListVO addChildren(DictListVO vo) {
        return addChildren(Collections.singletonList(vo));
    }

    /**
     * addChildren<BR>
     *
     * @param vo 参数
     * @return 结果
     */
    public DictListVO addChildren(Collection<DictListVO> vo) {
        PreConditionCheck.checkNotNull(vo, "不能为空！");
        List<DictListVO> list = Optional.ofNullable(getChildren()).orElse(new ArrayList<>(1));
        list.addAll(vo);
        this.setChildren(list);
        return this;
    }

}
