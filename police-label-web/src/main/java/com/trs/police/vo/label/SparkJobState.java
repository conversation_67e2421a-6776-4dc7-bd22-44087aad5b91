package com.trs.police.vo.label;

import com.trs.police.enums.LabelCalculationStatus;
import lombok.Data;

/**
 * 任务运行状态
 *
 */
@Data
public class SparkJobState {

    private LabelCalculationStatus jobState;

    private String errorMessage;

    /**
     * 设置任务运行状态
     *
     * @param jobState 运行状态
     */
    public void setJobState(String jobState) {
        if("FAILED".equals(jobState)){
            this.jobState = LabelCalculationStatus.FAILED;
        }else if ("FINISHED".equals(jobState) || "LOST".equals(jobState)){
            this.jobState = LabelCalculationStatus.SUCCESS;
        }else {
            this.jobState = LabelCalculationStatus.RUNNING;
        }
    }
}
