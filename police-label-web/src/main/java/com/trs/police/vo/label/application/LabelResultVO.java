package com.trs.police.vo.label.application;

import com.trs.police.service.shared.dict.DictItemDTO;
import com.trs.police.service.shared.dict.DictReference;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标签结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelResultVO {

    private String id;

    /**
     * 主体编号
     */
    private String objectNumber;

    /**
     * 主体类型
     */
    private Integer objectType;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 词典引用
     */
    private DictReference policeKindReference;

    /**
     * 警种分类
     */
    private DictItemDTO policeKind;

    /**
     * 频次
     */
    private Integer frequency;

    /**
     * 关联对象编号
     */
    private List<String> relatedObjectNumber;

    /**
     * 关联数据
     */
    private List<LabelResultRelatedVO> relatedData;
}
