package com.trs.police.vo;

import com.trs.police.vo.data.TableDataSourceVO;
import lombok.Data;

/**
 * 数据表VO类
 *
 * <AUTHOR>
 */
@Data
public class DataTableVO {

    /**
     * 数据表ID
     */
    private Long id;

    /**
     * 数据表英文名
     */
    private String tableNameEn;

    /**
     * 数据表别名
     */
    private String aliaseName;

    /**
     * 数据表中文名
     */
    private String tableNameCn;

    /**
     * 关联特征数量
     *
     * @deprecated 已废弃 该字段名定义不合理，舍弃该字段，后续请使用featureCount
     */
    @Deprecated
    private Long relationLabelCount = 0L;

    /**
     * 选中状态
     */
    private Integer selectedStatus;

    /**
     * 关联特征数量
     */
    private Integer featureCount = 0;

    /**
     * 数据源信息
     */
    private TableDataSourceVO dataSource;

    /**
     * 数据表主键字段
     */
    private String idField;
}
