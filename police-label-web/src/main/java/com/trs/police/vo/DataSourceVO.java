package com.trs.police.vo;

import com.trs.police.entity.datasource.DataSource.ConnectionStatus;
import com.trs.police.entity.datasource.DataSourceType;
import lombok.Data;


/**
 * 数据源VO类
 *
 * <AUTHOR>
 */
@Data
public class DataSourceVO {

    /**
     * 数据源ID
     */
    private Long id;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源类型
     */
    private DataSourceType type;

    /**
     * 连接状态
     */
    private ConnectionStatus connectionStatus;

    /**
     * 创建状态
     */
    private Integer createStatus;

    /**
     * 最后连接检查时间
     */
    private String lastCheckTime;

    /**
     * 关联特征数量
     */
    private Integer featureCount;

    /**
     * 关联标签数量
     */
    private Integer labelCount;

    /**
     * 关联模型数量
     */
    private Integer modelCount;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 数据源详细信息
     */
    private Object sourceInfo;

    private Integer sourceFrom;
} 