package com.trs.police.vo.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 公式
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FormulaVO implements Serializable {

    private String id;

    /**
     * 公式名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 语法
     */
    private String syntax;

    /**
     * 示例
     */
    private String example;

    /**
     * 参数
     */
    private List<Parameters> parameters;

    /**
     * 返回类型
     */
    private String returnType;

    /**
     * 分组类型
     */
    private String category;

    /**
     * 参数
     */
    @Data
    @NoArgsConstructor
    public static class Parameters {
        private String name;

        private String type;

        private String description;
    }

}
