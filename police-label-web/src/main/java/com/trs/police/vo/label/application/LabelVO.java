package com.trs.police.vo.label.application;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.enums.LabelCalculationStatus;
import com.trs.police.service.shared.dict.DictItemDTO;
import com.trs.police.service.shared.feature.Feature;
import com.trs.police.service.shared.user.CurrentUserDTO;
import lombok.Data;

import java.util.List;

/**
 * 特征视图对象
 *
 * <AUTHOR>
 */
@Data
public class LabelVO {

    /**
     * 标签ID
     */
    private Long labelId;

    /**
     * 特征名称
     */
    private String labelName;

    /**
     * 英文名称
     */
    private String labelEnName;

    /**
     * 描述
     */
    private String description;

    /**
     * 所属分类
     */
    private Long categoryCode;

    private DictItemDTO categoryDict;

    /**
     * 所属警种code，t_dict，type = 'feature_police_kind'
     */
    private Long policeKind;

    private DictItemDTO policeKindDict;

    /**
     * 业务规则描述
     */
    private String businessRule;

    /**
     * 颜色
     */
    private String color;

    /**
     * 是否自定义颜色
     */
    private Integer isCustomColor;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    private Long createDeptId;

    /**
     * 创建用户名称
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 编排的流程
     */
    private JSONObject processOrder;

    /**
     * 状态信息 状态：0-停用，1-启用
     */
    private Integer status;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 主体编码
     */
    private Long mainObjectCode;

    /**
     * 主体
     */
    private DictItemDTO mainObjectDict;

    /**
     * 命中数
     */
    private Long hitRate;

    /**
     * 更新状态 {@link LabelCalculationStatus#getDescription()}
     */
    private String updateStatusName = "等待计算";

    /**
     * 更新状态：{@link LabelCalculationStatus#getCode()}
     */
    private String updateStatus;

    /**
     * 更新方式：0-手动，1-例行
     */
    private Integer updateMethod = 1;

    private String updateMethodName = "例行";

    /**
     * 最后完成时间
     */
    private String lastFinishTime;

    /**
     * 最后运行时间
     */
    private String lastRunTime;

    private Integer labelType;
    private Integer cycleTimeType;
    private Integer cycleTime;

    /**
     * 有效时间方式 0 永久 1 期限
     */
    private Integer effectiveTimeType;

    /**
     * 有效期天数
     */
    private Integer effectiveTime;

    /**
     * 有效期
     */
    private String validityPeriod;

    /**
     * 用户信息
     */
    private CurrentUserDTO createUser;



    /* 兼容久的列表vo 过期，不推荐使用 */


    private Integer generatedData = 0;  // 是否产生数据：0-未产生，1-已产生

    /**
     * 使用的特征列表
     */
    private List<Long> featureIds;

    private List<Feature> features;

    /**
     * 最后运行错误信息
     */
    private String runErrorMessage;

    /**
     * 编排的流程
     */
    private JSONObject processOrderSnapshot;
}
