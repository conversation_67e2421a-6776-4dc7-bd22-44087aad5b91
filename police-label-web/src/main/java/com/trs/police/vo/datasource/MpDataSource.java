package com.trs.police.vo.datasource;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 中台数据源
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class MpDataSource implements Serializable {


    /**
     * id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 数据类型
     */
    private Integer dbType;

    /**
     * 数据类型名称
     */
    private String typeName;

    private String host;

    private String port;

    private String userName;

    private String password;;

    private String dbName;

    private String protocol;
}
