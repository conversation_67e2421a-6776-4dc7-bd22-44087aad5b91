package com.trs.police.vo.label.application;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.service.shared.field.DataTableFieldDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标签关联对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LabelResultRelatedVO {

    /**
     * 相关的id
     */
    private List<String> ids;

    /**
     * 表格id
     */
    private Long tableId;

    /**
     * 字段信息
     */
    private List<DataTableFieldDTO> filedInfo;

    /**
     * 数据
     */
    private List<JSONObject> data;

    /**
     * 主键字段名称
     */
    private String primaryKey;
}
