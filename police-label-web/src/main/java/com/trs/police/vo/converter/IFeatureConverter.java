package com.trs.police.vo.converter;

import com.trs.police.entity.feature.FeatureDO;
import com.trs.police.service.feature.application.DTO.vo.FeatureVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


/**
 * 特征转换器接口 (IFeatureConverter)
 * <p>
 * 该接口使用 MapStruct 框架定义了实体类 Feature 与视图对象 FeatureVO 之间的转换规则。
 * <p>
 * 注解说明：
 * - @Mapper: 指定该接口为 MapStruct 的映射器，并将其注册为 Spring 的组件。
 * - @Mapping: 定义字段映射规则，将 Feature 实体的 `id` 字段映射到 FeatureVO 的 `featureId` 字段。
 * <p>
 * 方法说明：
 * - convert: 将 Feature 实体对象转换为 FeatureVO 视图对象。
 */
@Mapper(componentModel = "spring")
public interface IFeatureConverter {

    /**
     * 将 Feature 实体对象转换为 FeatureVO 视图对象。
     *
     * @param feature Feature 实体对象
     * @return FeatureVO 视图对象
     */
    @Mapping(target = "featureId", source = "id")
    @Mapping(target = "mainObjectField", source = "mainObjectField", ignore = true)
    FeatureVO convert2VO(FeatureDO feature);
}
