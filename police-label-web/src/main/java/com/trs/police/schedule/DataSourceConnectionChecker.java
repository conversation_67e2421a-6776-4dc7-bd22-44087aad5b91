package com.trs.police.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.police.mapper.DataSourceMapper;
import com.trs.police.service.datasource.DataSourceService;
import com.trs.police.utils.SourceInfoConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据源连接状态定时检查
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DataSourceConnectionChecker {

    @Autowired
    private DataSourceService dataSourceService;

    @Autowired
    private DataSourceMapper dataSourceMapper;

    /**
     * 每小时执行一次数据源连接检查
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    public void checkAllDataSourceConnections() {
        log.info("开始执行数据源连接状态定时检查");

        try {
            // 查询所有未删除的数据源
            QueryWrapper<DataSource> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("deleted", 0);
            List<DataSource> allDataSources = dataSourceMapper.selectList(queryWrapper);

            log.info("共检测到 {} 个数据源需要检查", allDataSources.size());

            for (DataSource dataSource : allDataSources) {
                try {
                    DataSourceDTO dto = new DataSourceDTO();
                    SourceInfo sourceData = SourceInfoConverter.fromJson(dataSource.getSourceInfo(), dataSource.getType());
                    dto.setSourceInfo(sourceData);
                    dto.setType(dataSource.getType());
                    boolean connected = dataSourceService.checkConnection(dto);
                    log.info("数据源 [{}] 连接状态: {}", dataSource.getName(),
                            connected ? "正常" : "异常");
                } catch (Exception e) {
                    log.error("检查数据源 [{}] 连接状态时发生异常: {}",
                            dataSource.getName(), e.getMessage(), e);
                }
            }

            log.info("数据源连接状态定时检查完成");
        } catch (Exception e) {
            log.error("执行数据源连接状态定时检查时发生异常", e);
        }
    }
} 