package com.trs.police.connection.impl;

import com.trs.common.utils.StringUtils;
import com.trs.db.sdk.alive.DbAliveConnectTestSupport;
import com.trs.db.sdk.config.DBInfoConfig;
import com.trs.db.sdk.repository.paramProperties.CommonConnectParam;
import com.trs.police.connection.ConnectionTester;
import com.trs.police.entity.datasource.AbstractDbSourceInfo;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.Tuple2;
import io.vavr.control.Either;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.HashMap;
import java.util.Map;

import static com.trs.db.sdk.constant.ConfigureConstant.PRINCIPAL;
import static com.trs.db.sdk.constant.ConfigureConstant.USER_KEYTAB_PATH;


/**
 * 默认的数据源连接测试实现类。
 * 该类实现了 {@link ConnectionTester} 接口，提供了默认的连接测试逻辑。
 * 如果没有找到特定数据源类型的连接测试实现，则会使用此默认实现。
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefaultConnectionTester implements ConnectionTester {

    // 设置连接测试超时时间（秒）
    private static final int CONNECTION_TEST_TIMEOUT = 5;

    @Override
    public RestfulResults<Boolean> testConnection(SourceInfo sourceInfo) {
        Tuple2<DBInfoConfig, CommonConnectParam> tuple = getResult(sourceInfo);
        log.warn("使用默认连接测试实现处理数据源类型: {}", sourceInfo.getType());

        // 创建一个线程来执行连接测试，并设置超时
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Future<RestfulResults<Boolean>> future = executor.submit(() -> {
            try {
                log.info("开始连接测试: {}", tuple._1.getDbType());
                log.info("连接测试参数: {}", tuple._1);
                Either<Throwable, Boolean> either = DbAliveConnectTestSupport.getConnectTest(tuple._1.getDbType())
                        .connectTest(tuple._1, tuple._2);
                if (either.isRight()) {
                    return RestfulResults.ok(either.get());
                }
                return RestfulResults.error(either.getLeft().getMessage());
            } catch (Exception e) {
                log.error("连接测试执行异常: {}", e.getMessage(), e);
                return RestfulResults.error(e.getMessage());
            }
        });
        try {
            // 设置超时时间
            return future.get(CONNECTION_TEST_TIMEOUT, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            future.cancel(true);
            return RestfulResults.error("连接测试超时，请检查数据源配置或网络连接");
        } catch (InterruptedException | ExecutionException e) {
            log.error("连接测试执行异常: {}", e.getMessage(), e);
            return RestfulResults.error(e.getMessage());
        } finally {
            executor.shutdownNow();
        }
    }

    private static Tuple2<DBInfoConfig, CommonConnectParam> getResult(SourceInfo sourceInfo) {
        DBInfoConfig dbInfoConfig = new DBInfoConfig();
        CommonConnectParam connectParam = new CommonConnectParam();
        if (sourceInfo instanceof AbstractDbSourceInfo) {
            AbstractDbSourceInfo dbSourceInfo = (AbstractDbSourceInfo) sourceInfo;
            dbInfoConfig.setHost(dbSourceInfo.getHost());
            dbInfoConfig.setPort(dbSourceInfo.getPort());
            dbInfoConfig.setDbType(dbSourceInfo.getDbType());
            dbInfoConfig.setUserName(dbSourceInfo.getUserName());
            dbInfoConfig.setPassword(dbSourceInfo.getPassword());
            dbInfoConfig.setDbName(dbSourceInfo.getDbName());
            dbInfoConfig.setKerberos(dbSourceInfo.isKerberos());
            dbInfoConfig.setCloudType(dbInfoConfig.getCloudType());
            dbInfoConfig.setCustomHeaders(dbSourceInfo.getCustomHeaders());
            dbInfoConfig.setZookeeperPrincipal(dbSourceInfo.getZookeeperPrincipal());
            dbInfoConfig.setServiceName(dbSourceInfo.getServiceName());
            dbInfoConfig.setCloudType(dbSourceInfo.getCloudType());
            Map<String, String> customHeaders =new HashMap<>();
            customHeaders.put(USER_KEYTAB_PATH,dbSourceInfo.getUserKerTabPath());
            customHeaders.put(PRINCIPAL,dbSourceInfo.getPrincipal());
            customHeaders.put("KRB5CONF_PATH",dbSourceInfo.getKrb5ConfPath());
            dbInfoConfig.setCustomHeaders(customHeaders);
            connectParam.addProperty("trs.db.default.connection.protocol", StringUtils.isNullOrEmpty(dbSourceInfo.getProtocol()) ? "http" : dbSourceInfo.getProtocol());

            // 添加连接超时参数
            connectParam.addProperty("trs.db.default.connection.timeout", String.valueOf(CONNECTION_TEST_TIMEOUT));
            connectParam.addProperty("trs.db.default.socket.timeout", String.valueOf(CONNECTION_TEST_TIMEOUT));
        }
        return new Tuple2<>(dbInfoConfig, connectParam);
    }

}
