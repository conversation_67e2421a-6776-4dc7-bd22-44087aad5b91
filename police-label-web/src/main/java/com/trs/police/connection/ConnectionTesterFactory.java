package com.trs.police.connection;

import com.trs.police.connection.impl.DefaultConnectionTester;
import com.trs.police.entity.datasource.DataSourceType;
import com.trs.police.entity.datasource.SourceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源连接测试工厂
 * 用于获取不同类型数据源的连接测试实现
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ConnectionTesterFactory {

    private final Map<DataSourceType, ConnectionTester> testerMap = new ConcurrentHashMap<>();

    /**
     * 注入所有实现了ConnectionTester接口的Bean
     *
     * @param testers 所有ConnectionTester实现
     */
    @Autowired
    public ConnectionTesterFactory(List<ConnectionTester> testers) {
        for (ConnectionTester tester : testers) {
            if (tester instanceof TypedConnectionTester) {
                TypedConnectionTester typedTester = (TypedConnectionTester) tester;
                testerMap.put(typedTester.getType(), tester);
                log.info("注册数据源连接测试实现: {} -> {}", typedTester.getType(), tester.getClass().getSimpleName());
            }
        }
    }

    /**
     * 根据数据源信息获取对应的连接测试实现
     *
     * @param sourceInfo 数据源信息
     * @return 连接测试实现
     */
    public ConnectionTester getTester(SourceInfo sourceInfo) {
        ConnectionTester tester = testerMap.get(sourceInfo.getType());

        if (tester == null) {
            log.warn("未找到数据源类型 [{}] 的连接测试实现，将使用默认实现", sourceInfo.getType());
            tester = new DefaultConnectionTester();
        }

        return tester;
    }

    /**
     * 测试数据源连接
     *
     * @param sourceInfo 数据源信息
     * @return 连接是否成功
     */
    public boolean testConnection(SourceInfo sourceInfo) {
        try {
            return getTester(sourceInfo).testConnection(sourceInfo).getDatas();
        } catch (Exception e) {
            log.error("测试数据源连接时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

} 