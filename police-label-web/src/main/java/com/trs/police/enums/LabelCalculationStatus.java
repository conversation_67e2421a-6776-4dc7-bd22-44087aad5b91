package com.trs.police.enums;

/**
 * 标签计算状态枚举
 *
 * <AUTHOR>
 */
public enum LabelCalculationStatus {
    
    /**
     * 等待执行
     */
    PENDING("PENDING", "等待执行"),
    
    /**
     * 执行中
     */
    RUNNING("RUNNING", "执行中"),
    
    /**
     * 执行成功
     */
    SUCCESS("SUCCESS", "执行成功"),
    
    /**
     * 执行失败
     */
    FAILED("FAILED", "执行失败"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),
    
    /**
     * 超时
     */
    TIMEOUT("TIMEOUT", "超时");
    
    private final String code;
    private final String description;
    
    LabelCalculationStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取代码
     *
     * @return 代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 枚举值
     */
    public static LabelCalculationStatus fromCode(String code) {
        for (LabelCalculationStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的标签计算状态: " + code);
    }
    
    /**
     * 是否为终态
     *
     * @return 是否为终态
     */
    public boolean isTerminal() {
        return this == SUCCESS || this == FAILED || this == CANCELLED || this == TIMEOUT;
    }
    
    /**
     * 是否为运行态
     *
     * @return 是否为运行态
     */
    public boolean isRunning() {
        return this == RUNNING;
    }
}
