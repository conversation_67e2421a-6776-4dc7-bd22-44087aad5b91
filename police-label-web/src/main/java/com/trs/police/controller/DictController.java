package com.trs.police.controller;

import com.trs.police.dto.dict.DictDto;
import com.trs.police.service.dict.DictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 字典控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/global")
@Slf4j
public class DictController {


    @Autowired
    private DictService dictService;

    /**
     * 字典树
     *
     * @param type 类型
     * @return {@link DictDto}
     */
    @GetMapping(value = {"/dict", "/public/dict"})
    public List<DictDto> getDictTree(@RequestParam("type") String type) {
        return dictService.getDictListByType(type);
    }
}
