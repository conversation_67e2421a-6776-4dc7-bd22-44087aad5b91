package com.trs.police.controller;

import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.service.feature.application.DTO.command.FeatureAddDTO;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureCategoryStatisticDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureProcessPreviewDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureSearchDTO;
import com.trs.police.service.feature.application.DTO.vo.FeatureCategoryStatisticVO;
import com.trs.police.service.feature.application.DTO.vo.FeatureDetailVO;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.feature.application.service.FeatureService;
import com.trs.police.service.feature.application.DTO.vo.FeatureVO;
import com.trs.police.service.feature.application.service.FeatureStatisticService;
import com.trs.police.service.node.NodeService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 特征控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/feature")
@Slf4j
public class FeatureController {

    @Autowired
    private FeatureService featureService;

    @Autowired
    private NodeService nodeService;

    @Autowired
    private FeatureStatisticService featureStatisticService;

    /**
     * 新增特征
     *
     * @param dto 特征新增请求
     * @return 新增结果
     */
    @PostMapping("add")
    public RestfulResultsV2<FeatureVO> add(FeatureAddDTO dto) {
        return featureService.add(dto);
    }


    /**
     * 分页查询特征
     *
     * @param searchDTO 当前页
     * @return 特征分页列表
     */
    @PostMapping("/page")
    public RestfulResultsV2<FeatureVO> listFeatures(@RequestBody FeatureSearchDTO searchDTO) {
        return featureService.findFeatures(searchDTO);
    }

    /**
     * 删除特征
     *
     * @param featureId 特征ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Boolean deleteFeature(@PathVariable("id") Long featureId) {
        return featureService.deleteFeature(featureId);
    }

    /**
     * 启用/停用特征
     *
     * @param id     特征ID
     * @param status 状态 0=停用 1=启用
     * @return 操作结果
     */
    @PutMapping("/{id}/toggle/{status}")
    public Boolean toggleFeatureStatus(
            @PathVariable Long id,
            @PathVariable Integer status) {
        return featureService.toggleFeatureStatus(id, status);
    }

    /**
     * 保存节点信息
     *
     * @param processDTO 参数
     */
    @PostMapping("/saveNode")
    public void saveNode(@RequestBody FeatureProcessDTO processDTO) {
        featureService.saveNode(processDTO);
    }

    /**
     * 详情
     *
     * @param featureId 特征id
     * @return 特征详情
     */
    @GetMapping("/detail")
    private RestfulResultsV2<FeatureDetailVO> detail(Long featureId) {
        return RestfulResultsV2.ok(featureService.detail(featureId));
    }

    /**
     * 预览数据
     *
     * @param dto 参数
     * @return 结果
     */
    @PostMapping("/previewNode")
    public RestfulResultsV2<NodeData> previewNode(@RequestBody FeatureProcessPreviewDTO dto) {
        ProcessDTO p = new ProcessDTO();
        p.setCurrentNode(dto.getPreviewNode());
        p.setNodes(dto.getNodes());
        p.setControl(dto.getControl());
        p.setNodeOrders(dto.getNodeOrders());
        NodeData nodeData = nodeService.previewNode(p);
        return RestfulResultsV2.ok(nodeData);
    }

    /**
     * 分类统计
     *
     * @param dto 参数
     * @return 分类统计
     */
    @RequestMapping("/categoryStatistic")
    public RestfulResultsV2<FeatureCategoryStatisticVO> labelCategoryStatistic(FeatureCategoryStatisticDTO dto) {
        return RestfulResultsV2.ok(featureStatisticService.featureCategoryStatistic(dto));
    }

    /**
     * 复制
     *
     * @param id 特征id
     * @return 新特征id
     */
    @RequestMapping("/{id}/copy")
    public RestfulResultsV2<Long> copy(@PathVariable Long id) {
        return RestfulResultsV2.ok(featureService.copy(id));
    }
}
