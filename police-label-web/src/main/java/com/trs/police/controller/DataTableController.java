package com.trs.police.controller;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.dto.DataTableDTO;
import com.trs.police.dto.DataTableOverviewDto;
import com.trs.police.dto.TableSelectionDTO;
import com.trs.police.service.datatable.DataTableService;
import com.trs.police.vo.DataTableVO;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数据表控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/datatable")
@Slf4j
public class DataTableController {

    @Autowired
    private DataTableService dataTableService;

    /**
     * 根据数据源获取该数据源下的全部数据表
     *
     * @param dto  数据表DTO
     * @return 数据表列表
     */
    @GetMapping("getAllDataTable")
    public RestfulResultsV2<DataTableVO> getAllDataTable(TableSelectionDTO dto) {
        return dataTableService.getAllDataTable(dto);
    }

    /**
     * 修改数据表
     *
     * @param id  数据表ID
     * @param dto 数据表dto
     */
    @GetMapping("/{id}/updateTable")
    public void updateDataSource(@PathVariable Long id, DataTableDTO dto) {
        dataTableService.updateDataSource(id, dto);
    }

    /**
     * 选中数据表
     *
     * @param dto  dto
     */
    @PostMapping("/selectTables")
    public void selectTables(@RequestBody TableSelectionDTO dto) {
        dataTableService.selectTables(dto);
    }

    /**
     * 删除数据表
     *
     * @param id  数据表ID
     */
    @GetMapping("/{id}")
    public void delete(@PathVariable Long id) {
        dataTableService.delete(id);
    }

    /**
     * 数据表详情页
     *
     * @param id id
     * @return 数据表详情页
     */
    @GetMapping("/datatableDetail")
    public RestfulResultsV2<DataTableVO> datatableDetail(@RequestParam("id") Integer id) {
        return dataTableService.datatableDetail(id);
    }

    /**
     * 数据详情预览页
     *
     * @param dto dto
     * @return 数据表预览页
     */
    @PostMapping("/getDataOverview")
    public RestfulResultsV2<JSONObject> getDataOverview(@RequestBody DataTableOverviewDto dto) {
        return dataTableService.getDataOverview(dto);
    }

    /**
     * 刷新数据源
     *
     * @param tableId 数据源ID
     */
    @GetMapping("/refreshTable")
    public void refreshTable(Long tableId) {
        dataTableService.refreshTableById(tableId);
    }
}
