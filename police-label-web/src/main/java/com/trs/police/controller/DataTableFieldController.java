package com.trs.police.controller;

import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.vo.DataTableFieldVO;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 字段controller
 */
@RestController
@RequestMapping("/api/datatableField")
public class DataTableFieldController {

    @Autowired
    private FieldsService fieldsService;

    /**
     * 数据表字段查询
     *
     * @param tableId 数据表id
     * @param selectedStatus 选中状态 0:未选中 1:选中
     * @return 结果
     */
    @GetMapping("/getFieldInfo")
    public RestfulResultsV2<DataTableFieldVO> getFieldInfo(@RequestParam("tableId") Integer tableId,
                                                           @RequestParam(value = "selectedStatus",required = false) Integer selectedStatus){
        return RestfulResultsV2.ok(fieldsService.getFieldInfo(tableId,selectedStatus));
    }
}
