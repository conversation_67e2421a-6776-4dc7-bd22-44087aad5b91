package com.trs.police.controller;


import com.trs.police.dto.label.*;
import com.trs.police.dto.label.node.LabelProcessDTO;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.label.LabelService;
import com.trs.police.service.node.NodeService;
import com.trs.police.service.shared.dict.DictItemDTO;
import com.trs.police.vo.label.LabelCategoryStatisticVO;
import com.trs.police.vo.label.LabelListVO;
import com.trs.police.vo.label.application.LabelResultVO;
import com.trs.police.vo.label.application.LabelVO;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 标签控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping({"/api/label","/public/api/label"})
@Slf4j
public class LabelController {

    @Autowired
    private LabelService labelService;

    @Autowired
    private NodeService nodeService;

    /**
     * 添加标签
     *
     * @param add 标签
     * @return 新增的标签
     */
    @PostMapping("/add")
    public RestfulResultsV2<LabelVO> addLabel(@RequestBody LabelAddDTO add) {
        return RestfulResultsV2.ok(labelService.addLabel(add));
    }

    /**
     * 详情
     *
     * @param labelId li
     * @return 标签详情
     */
    @GetMapping("/detail")
    public RestfulResultsV2<LabelVO> detail(Long labelId) {
        return RestfulResultsV2.ok(labelService.detail(labelId));
    }

    /**
     * 分类统计
     *
     * @param dto dto
     * @return 分类统计
     */
    @RequestMapping("/categoryStatistic")
    public RestfulResultsV2<LabelCategoryStatisticVO> labelCategoryStatistic(LabelCategoryStatisticDTO dto) {
        return RestfulResultsV2.ok(labelService.labelCategoryStatistic(dto));
    }

    /**
     * 列表页面
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/page")
    public RestfulResultsV2<LabelListVO> page(@RequestBody LabelListDto dto){
        return labelService.page(dto);
    }

    /**
     * 启用/停用标签
     *
     * @param labelId 标签ID
     * @param status 状态 0=停用 1=启用
     * @return 操作结果
     */
    @GetMapping("/status")
    public RestfulResultsV2<String> toggleLabelStatus(Long labelId, Integer status) {
        return RestfulResultsV2.ok(labelService.toggleLabelStatus(labelId, status));
    }

    /**
     * 删除标签
     *
     * @param labelId 标签ID
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    public RestfulResultsV2<String> deleteLabel(Long labelId) {
        return RestfulResultsV2.ok(labelService.deleteLabel(labelId));
    }


    /**
     * 添加码表
     *
     * @param add 码表
     * @return 新增的码表
     */
    @PostMapping("/dict/add")
    RestfulResultsV2<DictItemDTO> add(@RequestBody LabelDictAddDTO add) {
        return RestfulResultsV2.ok(labelService.addCategory(add));
    }

    /**
     * 添加码表
     *
     * @param code 码表code
     * @return 新增的码表
     */
    @DeleteMapping("/dict/delete")
    RestfulResultsV2<Boolean> delete(Long code) {
        labelService.deleteCategory(code);
        return RestfulResultsV2.ok(true);
    }

    /**
     * 保存节点信息
     *
     * @param processDTO 参数
     */
    @PostMapping("/saveNode")
    public void saveNode(@RequestBody LabelProcessDTO processDTO) {
        labelService.saveNode(processDTO);
    }


    /**
     * 预览数据
     *
     * @param dto 参数
     * @return 结果
     */
    @PostMapping("/previewNode")
    public RestfulResultsV2<NodeData> previewNode(@RequestBody LabelProcessPreviewDTO dto) {
        ProcessDTO p = new ProcessDTO();
        p.setCurrentNode(dto.getPreviewNode());
        p.setNodes(dto.getNodes());
        p.setControl(dto.getControl());
        p.setNodeOrders(dto.getNodeOrders());
        p.setLabelId(dto.getInfo().getLabelId());
        NodeData nodeData = nodeService.previewNode(p);
        return RestfulResultsV2.ok(nodeData);
    }

    /**
     * 复制
     *
     * @param id 特征id
     * @return 新特征id
     */
    @RequestMapping("/{id}/copy")
    public RestfulResultsV2<Long> copy(@PathVariable Long id) {
        return RestfulResultsV2.ok(labelService.copy(id));
    }

    /**
     * 查询标签结果
     *
     * @param query 参数
     * @return 标签结果
     */
    @PostMapping("/queryLabelResult")
    public RestfulResultsV2<LabelResultVO> queryLabelResult(@RequestBody LabelResultQuery query) {
        return labelService.queryLabelResult(query.getParams(), query.getPageParams(), query.getSearchParams());
    }

}
