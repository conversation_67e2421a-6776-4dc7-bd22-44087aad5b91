package com.trs.police.controller;

import com.trs.police.service.baseService.CommonService;
import com.trs.police.vo.common.FormulaVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 通用控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/common")
@Slf4j
public class CommonController {

    @Autowired
    private CommonService commonService;

    /**
     * 上传Kerberos文件
     *
     * @param userName userName
     * @param type type
     * @param file file
     */
    @ApiOperation(value = "上传Kerberos文件",notes = "上传Kerberos文件")
    @PostMapping("/uploadKerberosFile")
    public void uploadKerberosFile(String userName,String type,MultipartFile file){
        log.info("file = {}",file);
        commonService.uploadKerberosFile(userName,type,file);
    }

    /**
     * 获取Kerberos文件列表
     *
     * @param userName userName
     * @param type type
     * @return kerberos路径
     */
    @GetMapping("/getKerberosFileList")
    public List<String> getKerberosFileList(String userName, String type){
        return commonService.getKerberosFileList(userName,type);
    }

    /**
     * 获取公式列表
     *
     * @return 公式列表
     */
    @GetMapping("/getFormulaList")
    List<FormulaVO> getFormulaList() {
        return commonService.getFormulaList();
    }

}
