package com.trs.police.controller;

import com.trs.police.converter.DataSourceConverter;
import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.service.datasource.DataSourceService;
import com.trs.police.vo.DataSourceVO;
import com.trs.police.vo.datasource.DataSourceGroupVO;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 数据源控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/datasource")
@Slf4j
public class DataSourceController {

    @Autowired
    private DataSourceService dataSourceService;

    /**
     * 创建数据源
     *
     * @param dto 数据源信息
     * @return 创建的数据源
     */
    @PostMapping("/createDataSource")
    public RestfulResults createDataSource(@RequestBody @Validated DataSourceDTO dto) {
        return dataSourceService.saveDataSource(dto);
    }

    /**
     * 更新数据源
     *
     * @param dto 数据源信息
     * @return 更新后的数据源
     */
    @PostMapping("/updateDataSource")
    public DataSourceVO updateDataSource(@RequestBody @Validated DataSourceDTO dto) {
        return dataSourceService.updateDataSource(dto);
    }

    /**
     * 获取数据源详情
     *
     * @param id 数据源ID
     * @return 数据源详情
     */
    @GetMapping("/{id}")
    public DataSourceVO getDataSource(@PathVariable Long id) {
        DataSource dataSource = dataSourceService.getDataSourceById(id);
        if (dataSource == null) {
            throw new IllegalArgumentException("数据源不存在");
        }

        return DataSourceConverter.toVO(dataSource);
    }

    /**
     * 分页查询数据源(中台)
     *
     * @param dto  dto
     * @return 数据源分页结果
     */
    @GetMapping("/mpPage")
    public RestfulResultsV2<DataSourceGroupVO> listMpDataSources(DataSourceDTO dto) {
        return dataSourceService.listMpDataSources(dto);
    }

    /**
     * 分页查询数据源
     *
     * @param dto  dto
     * @return 数据源分页结果
     */
    @GetMapping("/page")
    public RestfulResultsV2<DataSourceVO> listDataSources(DataSourceDTO dto) {
        return dataSourceService.findDataSources(dto);
    }

    /**
     * 改变数据源创建状态
     *
     * @param id 数据源类型
     * @param status 创建状态
     */
    @GetMapping("changeDataSourceStatus")
    public void changeDataSourceStatus(Long id, Integer status) {
        dataSourceService.changeDataSourceStatus(id,status);
    }

    /**
     * 删除数据源
     *
     * @param id 数据源ID
     */
    @DeleteMapping("/{id}")
    public void deleteDataSource(@PathVariable Long id) {
        dataSourceService.deleteDataSource(id);
    }

    /**
     * 检查数据源连接
     *
     * @param dto 数据源信息
     * @return 连接检查结果
     */
    @PostMapping("/check-connection")
    public boolean checkConnection(@RequestBody @Validated DataSourceDTO dto) {
        return dataSourceService.checkConnection(dto);
    }

    /**
     * 刷新数据源
     *
     * @param dataSourceId 数据源ID
     */
    @GetMapping("/refreshDataSource")
    public void refreshDataSource(Long dataSourceId) {
        dataSourceService.refreshDataSource(dataSourceId);
    }
} 