package com.trs.police.controller;

import com.trs.police.entity.label.LabelCalculationTaskDO;
import com.trs.police.service.label.LabelCalculationService;
import com.trs.police.service.label.LabelScheduleService;
import com.trs.police.vo.LabelComputeStatusVO;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标签计算控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/label/calculation")
@Api(tags = "标签计算管理")
public class LabelCalculationController {

    @Autowired
    private LabelCalculationService calculationService;

    @Autowired
    private LabelScheduleService scheduleService;

    /**
     * 手动触发标签计算
     *
     * @param labelId 标签ID
     * @return 任务ID
     */
    @PostMapping("/trigger/{labelId}")
    @ApiOperation("手动触发标签计算")
    public RestfulResultsV2<String> triggerCalculation(
            @ApiParam("标签ID") @PathVariable Long labelId) {
        
        try {
            String taskId = calculationService.triggerManualCalculation(labelId);
            log.info("手动触发标签计算成功: labelId={}, taskId={}", labelId, taskId);
            return RestfulResultsV2.ok(taskId+"：标签计算任务已提交");
        } catch (Exception e) {
            log.error("手动触发标签计算失败: labelId={}", labelId, e);
            return RestfulResultsV2.error("触发标签计算失败: " + e.getMessage());
        }
    }

    /**
     * 获取标签运行状态
     *
     * @param labelId 标签id
     * @return 任务状态
     */
    @GetMapping("/status/{labelId}")
    @ApiOperation("获取计算任务状态")
    public RestfulResultsV2<LabelComputeStatusVO> getTaskStatus(
            @ApiParam("任务ID") @PathVariable Long labelId) {
        
        try {
            LabelComputeStatusVO vo = calculationService.getTaskStatus(labelId);
            return RestfulResultsV2.ok(vo);
        } catch (Exception e) {
            log.error("获取任务状态失败: labelId={}", labelId, e);
            return RestfulResultsV2.error("获取任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消计算任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    @PostMapping("/cancel/{taskId}")
    @ApiOperation("取消计算任务")
    public RestfulResultsV2<Boolean> cancelTask(
            @ApiParam("任务ID") @PathVariable String taskId) {
        
        try {
            boolean result = calculationService.cancelTask(taskId);
            if (result) {
                log.info("取消计算任务成功: taskId={}", taskId);
                return RestfulResultsV2.ok("任务已取消");
            } else {
                return RestfulResultsV2.error("取消任务失败，任务可能不存在或状态不允许取消");
            }
        } catch (Exception e) {
            log.error("取消计算任务失败: taskId={}", taskId, e);
            return RestfulResultsV2.error("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取标签计算历史
     *
     * @param labelId 标签ID
     * @param limit 限制数量
     * @return 任务历史列表
     */
    @GetMapping("/history/{labelId}")
    @ApiOperation("获取标签计算历史")
    public RestfulResultsV2<LabelCalculationTaskDO> getCalculationHistory(
            @ApiParam("标签ID") @PathVariable Long labelId,
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            List<LabelCalculationTaskDO> history = calculationService.getCalculationHistory(labelId, limit);
            return RestfulResultsV2.ok(history);
        } catch (Exception e) {
            log.error("获取标签计算历史失败: labelId={}", labelId, e);
            return RestfulResultsV2.error("获取计算历史失败: " + e.getMessage());
        }
    }

    /**
     * 检查标签是否需要计算
     *
     * @param labelId 标签ID
     * @return 是否需要计算
     */
    @GetMapping("/should-calculate/{labelId}")
    @ApiOperation("检查标签是否需要计算")
    public RestfulResultsV2<Boolean> shouldCalculate(
            @ApiParam("标签ID") @PathVariable Long labelId) {
        
        try {
            boolean shouldCalculate = calculationService.shouldCalculate(labelId);
            return RestfulResultsV2.ok(shouldCalculate);
        } catch (Exception e) {
            log.error("检查标签是否需要计算失败: labelId={}", labelId, e);
            return RestfulResultsV2.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有定时标签
     *
     * @return 定时标签ID列表
     */
    @GetMapping("/scheduled-labels")
    @ApiOperation("获取所有定时标签")
    public RestfulResultsV2<Long> getScheduledLabels() {
        
        try {
            List<Long> scheduledLabels = calculationService.getScheduledLabels();
            return RestfulResultsV2.ok(scheduledLabels);
        } catch (Exception e) {
            log.error("获取定时标签失败", e);
            return RestfulResultsV2.error("获取定时标签失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发所有需要计算的标签
     *
     * @return 是否触发成功
     */
    @PostMapping("/trigger-all")
    @ApiOperation("手动触发所有需要计算的标签")
    public RestfulResultsV2<String> triggerAllCalculations() {
        
        try {
            scheduleService.triggerAllScheduledCalculations();
            return RestfulResultsV2.ok("所有需要计算的标签已触发");
        } catch (Exception e) {
            log.error("手动触发所有标签计算失败", e);
            return RestfulResultsV2.error("触发失败: " + e.getMessage());
        }
    }

    /**
     * 获取计算统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取计算统计信息")
    public RestfulResultsV2<Map<String, Object>> getStatistics() {
        
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 获取定时标签数量
            List<Long> scheduledLabels = calculationService.getScheduledLabels();
            statistics.put("scheduledLabelCount", scheduledLabels.size());
            
            // 获取运行中的任务数量
            long runningTaskCount = scheduleService.getRunningTaskCount();
            statistics.put("runningTaskCount", runningTaskCount);
            
            // 计算需要执行的标签数量
            long needCalculateCount = scheduledLabels.stream()
                    .mapToLong(labelId -> calculationService.shouldCalculate(labelId) ? 1 : 0)
                    .sum();
            statistics.put("needCalculateCount", needCalculateCount);
            
            return RestfulResultsV2.ok(statistics);
        } catch (Exception e) {
            log.error("获取计算统计信息失败", e);
            return RestfulResultsV2.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量触发标签计算
     *
     * @param labelIds 标签ID列表
     * @return 触发结果
     */
    @PostMapping("/batch-trigger")
    @ApiOperation("批量触发标签计算")
    public RestfulResultsV2<Map<String, String>> batchTriggerCalculation(
            @ApiParam("标签ID列表") @RequestBody List<Long> labelIds) {
        
        try {
            Map<String, String> results = new HashMap<>();
            
            for (Long labelId : labelIds) {
                try {
                    String taskId = calculationService.triggerManualCalculation(labelId);
                    results.put(labelId.toString(), taskId);
                    log.info("批量触发标签计算成功: labelId={}, taskId={}", labelId, taskId);
                } catch (Exception e) {
                    results.put(labelId.toString(), "ERROR: " + e.getMessage());
                    log.error("批量触发标签计算失败: labelId={}", labelId, e);
                }
            }
            
            return RestfulResultsV2.ok(results+"：批量触发完成");
        } catch (Exception e) {
            log.error("批量触发标签计算失败", e);
            return RestfulResultsV2.error("批量触发失败: " + e.getMessage());
        }
    }
}
