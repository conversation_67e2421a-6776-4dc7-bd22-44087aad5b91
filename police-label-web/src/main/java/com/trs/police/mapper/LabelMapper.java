package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.dto.label.LabelListDto;
import com.trs.police.entity.label.LabelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * lm
 */
@Mapper
public interface LabelMapper extends BaseMapper<LabelDO> {

    /**
     * 分页查询标签列表
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return 标签分页列表
     */
    Page<LabelDO> findLabelPage(IPage<LabelDO> page, @Param("dto") LabelListDto dto);
}
