package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.entity.datatable.DataTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 数据表Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface DataTableMapper extends BaseMapper<DataTable> {
    /**
     * 批量插入数据表
     *
     * @param dataTables 数据表列表
     * @return 影响行数
     */
    int insertBatch(List<DataTable> dataTables);

    /**
     * 更新数据表ID字段
     *
     * @param id      数据表ID
     * @param idField ID字段
     */
    @Update("update tb_data_table set id_field = #{idField} where id = #{id}")
    void updateIdField(@Param("id") Long id, @Param("idField") String idField);
}
