package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.entity.baseEntiry.Dict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 字典
 *
 * <AUTHOR>
 * @since 2025/4/8
 **/
@Mapper
public interface DictMapper extends BaseMapper<Dict> {


    /**
     * 字典
     *
     * @param code 码值
     * @param type 类型
     * @return {@link Dict}
     */
    Dict getByCodeAndType(@Param("code") Long code, @Param("type") String type);

}
