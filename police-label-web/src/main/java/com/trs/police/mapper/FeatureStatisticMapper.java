package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.vo.CountVO;
import com.trs.police.entity.feature.FeatureDO;
import com.trs.police.service.feature.domain.value.FeatureCategoryCount;
import com.trs.police.service.feature.domain.value.FeatureCategoryStatisticDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 特征Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FeatureStatisticMapper extends BaseMapper<FeatureDO> {

    /**
     * 分类统计
     *
     * @param dto 警种
     * @return 分类统计
     */
    List<FeatureCategoryCount> categoryCount(@Param("dto") FeatureCategoryStatisticDTO dto);

    /**
     * 表格使用统计
     *
     * @param tbs 表格ID
     * @return 表格使用统计
     */
    List<CountVO> tableUseStatistic(@Param("tbs") List<Long> tbs);
}
