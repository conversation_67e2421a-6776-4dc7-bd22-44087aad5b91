package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.DataSourceType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据源Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface DataSourceMapper extends BaseMapper<DataSource> {

    /**
     * 根据名称查找数据源
     *
     * @param name 数据源名称
     * @return 数据源对象
     */
    @Select("SELECT * FROM tb_data_source WHERE name = #{name} AND deleted = 0 AND create_status = 1")
    DataSource findByName(@Param("name") String name);

    /**
     * 根据数据库名称查找数据源
     *
     * @param dbName 数据库名称
     * @return 数据源对象
     */
    @Select("SELECT * FROM tb_data_source WHERE JSON_EXTRACT(source_info, '$.dbName') = #{dbName} AND deleted = 0 AND create_status = 1")
    DataSource findByDbName(@Param("dbName") String dbName);

    /**
     * 根据类型查找数据源列表
     *
     * @param type 数据源类型
     * @return 数据源列表
     */
    @Select("SELECT * FROM tb_data_source WHERE type = #{type} AND deleted = 0")
    List<DataSource> findByType(@Param("type") DataSourceType type);

    /**
     * 查询有关联的数据源（关联特征、标签或模型的数据源）
     *
     * @return 有关联的数据源列表
     */
    @Select("SELECT * FROM tb_data_source WHERE (feature_count > 0 OR label_count > 0 OR model_count > 0) AND deleted = 0")
    List<DataSource> findDataSourcesWithRelations();

    /**
     * 查找特定类型且信息中包含指定信息的数据源
     * 用于检查数据源是否重复
     *
     * @param type        数据源类型
     * @param infoPattern 信息特征（如某些字段的组合值）
     * @return 匹配的数据源列表
     */
    @Select("SELECT * FROM tb_data_source WHERE type = #{type} AND generate_unique_id = #{infoPattern} AND deleted = 0 AND create_status = 1")
    List<DataSource> findByTypeAndSourceInfoContaining(
            @Param("type") DataSourceType type,
            @Param("infoPattern") String infoPattern);

    /**
     * 查找所有数据源数据
     *
     * @return 数据源类型列表
     */
    List<DataSource> findAllDataType();

}