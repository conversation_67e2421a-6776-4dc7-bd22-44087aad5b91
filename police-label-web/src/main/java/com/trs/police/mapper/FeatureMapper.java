package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.service.feature.domain.value.FeatureSearch;
import com.trs.police.entity.feature.FeatureDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 特征Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FeatureMapper extends BaseMapper<FeatureDO> {

    /**
     * 分页查询特征列表
     *
     * @param page 分页参数
     * @param dto  表ID
     * @return 特征分页列表
     */
    Page<FeatureDO> findFeaturePage(IPage<FeatureDO> page, @Param("dto") FeatureSearch dto);
}
