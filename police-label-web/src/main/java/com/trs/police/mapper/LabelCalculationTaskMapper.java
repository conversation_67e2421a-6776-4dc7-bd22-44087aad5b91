package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.entity.label.LabelCalculationTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 标签计算任务Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LabelCalculationTaskMapper extends BaseMapper<LabelCalculationTaskDO> {

    /**
     * 根据标签ID查询计算任务历史
     *
     * @param labelId 标签ID
     * @param limit 限制数量
     * @return 任务历史列表
     */
    List<LabelCalculationTaskDO> findByLabelIdOrderByCreateTimeDesc(@Param("labelId") Long labelId, @Param("limit") Integer limit);

    /**
     * 根据任务ID查询任务
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    LabelCalculationTaskDO findByTaskId(@Param("taskId") String taskId);

    /**
     * 根据标签ID查询任务
     *
     * @param labelId 标签ID
     * @return 任务信息
     */
    LabelCalculationTaskDO findByLabelId(@Param("labelId") Long labelId);

    /**
     * 查询指定时间范围内的任务
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 状态（可选）
     * @return 任务列表
     */
    List<LabelCalculationTaskDO> findByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime,
                                                 @Param("status") String status);

    /**
     * 查询正在运行的任务
     *
     * @return 正在运行的任务列表
     */
    List<LabelCalculationTaskDO> findRunningTasks();

    /**
     * 查询超时的任务
     *
     * @param timeoutMinutes 超时分钟数
     * @param nowTime 当前时间
     * @return 超时任务列表
     */
    List<LabelCalculationTaskDO> findTimeoutTasks(@Param("timeoutMinutes") Integer timeoutMinutes, @Param("nowTime") LocalDateTime nowTime);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @param endTime 结束时间
     * @param errorMessage 错误信息
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int updateTaskStatus(@Param("taskId") String taskId,
                         @Param("status") String status,
                         @Param("endTime") LocalDateTime endTime,
                         @Param("errorMessage") String errorMessage,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新任务执行结果
     *
     * @param taskId 任务ID
     * @param processedCount 处理记录数
     * @param successCount 成功记录数
     * @param failedCount 失败记录数
     * @param executionLog 执行日志
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int updateTaskResult(@Param("taskId") String taskId,
                        @Param("processedCount") Long processedCount,
                        @Param("successCount") Long successCount,
                        @Param("failedCount") Long failedCount,
                        @Param("executionLog") String executionLog,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 获取标签最近一次成功执行的时间
     *
     * @param labelId 标签ID
     * @return 最近成功执行时间
     */
    LocalDateTime getLastSuccessTime(@Param("labelId") Long labelId);

    /**
     * 获取标签最近一次执行的时间
     *
     * @param labelId 标签ID
     * @return 最近执行时间
     */
    LocalDateTime getLastRunTime(@Param("labelId") Long labelId);

    /**
     * 统计标签的执行次数
     *
     * @param labelId 标签ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行次数
     */
    Long countExecutions(@Param("labelId") Long labelId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);
}
