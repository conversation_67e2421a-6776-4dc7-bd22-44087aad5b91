package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.vo.CountVO;
import com.trs.police.dto.label.LabelCategoryStatisticDTO;
import com.trs.police.entity.label.LabelDO;
import com.trs.police.vo.label.domain.CategoryCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * lsm
 */
public interface LabelStatisticMapper extends BaseMapper<LabelDO> {

    /**
     * 分类统计
     *
     * @param dto 警种
     * @return 分类统计
     */
    List<CategoryCount> categoryCount(@Param("dto") LabelCategoryStatisticDTO dto);

    /**
     * 统计特征使用次数
     *
     * @param featureIds 特征id
     * @return 统计结果
     */
    List<CountVO> featureUseStatistic(@Param("featureIds") List<Long> featureIds);
}
