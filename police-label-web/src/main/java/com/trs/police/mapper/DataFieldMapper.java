package com.trs.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.entity.dataField.DataField;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 数据字段Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface DataFieldMapper extends BaseMapper<DataField> {
    /**
     * 批量插入数据字段
     *
     * @param dataFields 数据字段列表
     * @return 影响行数
     */
    int insertBatch(List<DataField> dataFields);
} 