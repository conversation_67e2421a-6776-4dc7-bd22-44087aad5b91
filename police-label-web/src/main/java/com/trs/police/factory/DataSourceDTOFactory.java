package com.trs.police.factory;

import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.*;

/**
 * 数据源DTO工厂
 * 用于根据类型创建对应的数据源DTO实例
 *
 * <AUTHOR>
 */
public class DataSourceDTOFactory {

    /**
     * 判断DTO类型是否与指定类型匹配
     *
     * @param dto  数据源DTO
     * @param type 数据源类型
     * @return 是否匹配
     */
    public static boolean matchType(DataSourceDTO dto, DataSourceType type) {
        if (dto == null || type == null || dto.getSourceInfo() == null) {
            return false;
        }
        SourceInfo sourceInfo = dto.sourceInfo;
        switch (type) {
            case ES:
                return sourceInfo instanceof EsSourceInfo;
            case MYSQL:
                return sourceInfo instanceof MysqlSourceInfo;
            case ORACLE:
                return sourceInfo instanceof OracleSourceInfo;
            case HYBASE:
                return sourceInfo instanceof HybaseSourceInfo;
            case POSTGRESQL:
                return sourceInfo instanceof PostgresqlSourceInfo;
            case CLICKHOUSE:
                return sourceInfo instanceof ClickHouseSourceInfo;
            case HIVE:
                return sourceInfo instanceof HiveSourceInfo;
            case API:
                return sourceInfo instanceof ApiSourceInfo;
            case KAFKA:
                return sourceInfo instanceof KafkaSourceInfo;
            default:
                return false;
        }
    }
} 