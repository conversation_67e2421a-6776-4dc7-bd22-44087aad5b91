package com.trs.db.sdk.mysql.builder.condition;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.db.sdk.builder.condition.AbstractConditionBuilder;
import com.trs.db.sdk.mysql.builder.condition.operator.StartWithOperator;

import java.util.Arrays;
import java.util.List;

/**
 * start
 *
 * <AUTHOR>
 */
public class StartConditionBuilder extends AbstractConditionBuilder {

    @Override
    protected void validateValues(List<Object> list) throws RuntimeException {
        PreConditionCheck.checkNotNull(list);
        PreConditionCheck.checkArgument(!list.isEmpty());
    }

    @Override
    protected Value trslBuilderWithValues(List<Object> list) throws RuntimeException {
        Value value = new Value();
        value.setSymbolValue("?");
        String v = "'" + String.valueOf(list.get(0)) + "%'";
        value.setTrueValues(Arrays.asList(list.get(0) + "%"));
        value.setValues(v);
        return value;
    }

    @Override
    protected String operatorSymbol() {
        return " LIKE ";
    }

    @Override
    public IOperator interestedOperator() {
        return StartWithOperator.INSTANCE;
    }
}
