package com.trs.db.sdk.mysql.builder.condition.operator;

import com.trs.common.base.Option;
import com.trs.common.utils.expression.operator.IOperator;

/**
 * sparlk start
 *
 * <AUTHOR>
 */
public class StartWithOperator implements IOperator {

    public static final StartWithOperator INSTANCE = new StartWithOperator();

    private StartWithOperator() {
    }


    @Override
    public Option<String> getOptionValue() {
        return Option.of("start");
    }
}
