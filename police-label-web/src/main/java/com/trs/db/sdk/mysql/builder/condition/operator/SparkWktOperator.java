package com.trs.db.sdk.mysql.builder.condition.operator;

import com.trs.common.base.Option;
import com.trs.common.utils.expression.operator.IOperator;

/**
 * sparlk wkt操作符
 *
 * <AUTHOR>
 */
public class SparkWktOperator implements IOperator {

    public static final SparkWktOperator INSTANCE = new SparkWktOperator();

    private SparkWktOperator() {
    }


    @Override
    public Option<String> getOptionValue() {
        return Option.of("spark_wkt");
    }
}
