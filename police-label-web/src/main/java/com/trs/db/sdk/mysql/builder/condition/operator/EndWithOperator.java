package com.trs.db.sdk.mysql.builder.condition.operator;

import com.trs.common.base.Option;
import com.trs.common.utils.expression.operator.IOperator;

/**
 * sparlk end
 *
 * <AUTHOR>
 */
public class EndWithOperator implements IOperator {

    public static final EndWithOperator INSTANCE = new EndWithOperator();

    private EndWithOperator() {
    }


    @Override
    public Option<String> getOptionValue() {
        return Option.of("end");
    }
}
