package com.trs.db.sdk.mysql.builder.condition;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.db.sdk.builder.condition.AbstractConditionBuilder;
import com.trs.db.sdk.mysql.builder.condition.operator.SparkWktOperator;
import com.trs.police.common.core.utils.GeoUtils;
import com.trs.police.constant.GeoEnum;
import com.trs.police.common.core.vo.Geometries;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * WKT条件构造器
 */
public class SparkWktConditionBuilder extends AbstractConditionBuilder {

    @Override
    protected void validateValues(List<Object> values) throws RuntimeException {
        if (CollectionUtils.isEmpty(values)) {
            throw buildErrorMessage("values值个数必须大于0");
        }
    }

    @Override
    protected Value trslBuilderWithValues(List<Object> values) throws RuntimeException {
        throw new RuntimeException("WKT暂不支持");
    }

    @Override
    protected String operatorSymbol() {
        return "";
    }

    @Override
    public IOperator interestedOperator() {
        return SparkWktOperator.INSTANCE;
    }

    @Override
    protected StringBuilder trslBuilderWithFieldAndValues(String field, List<Object> values, List<Object> templateColumns) throws RuntimeException {
        List<String> sqlList = values.stream()
                .map(v -> buildSql(field, String.valueOf(v), Objects.nonNull(templateColumns)))
                .collect(Collectors.toList());
        if (sqlList.size() == 1) {
            return new StringBuilder().append(sqlList.get(0));
        }
        return new StringBuilder()
                .append(
                        String.format("(%s)", sqlList.stream().collect(Collectors.joining(" OR ")))
                );
    }

    private String buildSql(String field, String value, Boolean isTemplate) {
        if (value.trim().startsWith("POLYGON")) {
            String where = getString(field, value, isTemplate);
            return where;
        } else if (value.trim().startsWith("{")) {
            Geometries geometries = JSON.parseObject(value, Geometries.class);
            GeoEnum geoEnum = GeoEnum.fromCodeOrThrow(geometries.getType());
            String sqlValue = null;
            switch (geoEnum) {
                case POLYGON:
                    sqlValue = isTemplate ? "?" : String.format("'%s'", geometries.getGeometry());
                    break;
                case CIRCLE:
                    sqlValue = isTemplate ? "?" : String.format("'%s'", GeoUtils.convertCircleToPolygon(geometries));
                    break;
                default:
                    throw new RuntimeException("暂不支持的geo类型");
            }
            return getString(field, sqlValue, isTemplate);
        }
        throw new RuntimeException("暂不支持的geo类型");
    }

    @NotNull
    private static String getString(String field, String value, Boolean isTemplate) {
        String sqlValue = isTemplate ? "?" : String.format("'%s'", value);
        String where = String.format("%s IS NOT NULL AND %s != '' AND " +
                        "ST_Contains(ST_GeomFromText(%s), " +
                        "ST_Point(" +
                        "CAST(CASE " +
                        "    WHEN %s LIKE 'POINT%%' THEN " +
                        "        regexp_extract(%s, 'POINT\\\\s*\\\\(([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s RLIKE '^[-+]?[0-9]*\\\\.?[0-9]+[ ,][-+]?[0-9]*\\\\.?[0-9]+$' THEN " +
                        "        split(%s, '[ ,]')[0] " +
                        "    WHEN %s LIKE '{%%\"lon\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"lon\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s LIKE '{%%\"longitude\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"longitude\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s LIKE '{%%\"x\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"x\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    ELSE " +
                        "        NULL " +
                        "END AS DOUBLE), " +
                        "CAST(CASE " +
                        "    WHEN %s LIKE 'POINT%%' THEN " +
                        "        regexp_extract(%s, 'POINT\\\\s*\\\\([-+]?[0-9]*\\\\.?[0-9]+[ ,]([-+]?[0-9]*\\\\.?[0-9]+)\\\\)', 1) " +
                        "    WHEN %s RLIKE '^[-+]?[0-9]*\\\\.?[0-9]+[ ,][-+]?[0-9]*\\\\.?[0-9]+$' THEN " +
                        "        split(%s, '[ ,]')[size(split(%s, '[ ,]'))-1] " +
                        "    WHEN %s LIKE '{%%\"lon\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"lat\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s LIKE '{%%\"longitude\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"latitude\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    WHEN %s LIKE '{%%\"x\"%%:%%}' THEN " +
                        "        regexp_extract(%s, '\"y\"\\\\s*:\\\\s*([-+]?[0-9]*\\\\.?[0-9]+)', 1) " +
                        "    ELSE " +
                        "        NULL " +
                        "END AS DOUBLE)" +
                        "))",
                field, field, sqlValue,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field,
                field, field);
        return where;
    }
}
