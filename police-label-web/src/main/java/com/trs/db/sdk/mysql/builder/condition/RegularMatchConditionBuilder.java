package com.trs.db.sdk.mysql.builder.condition;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.db.sdk.builder.condition.AbstractConditionBuilder;
import com.trs.db.sdk.mysql.builder.condition.operator.RegularOperator;
import com.trs.db.sdk.util.MysqlUtil;

import java.util.Arrays;
import java.util.List;

/**
 * regular match
 *
 * <AUTHOR>
 */
public class RegularMatchConditionBuilder extends AbstractConditionBuilder {

    @Override
    protected void validateValues(List<Object> list) throws RuntimeException {
        PreConditionCheck.checkNotNull(list);
        PreConditionCheck.checkArgument(!list.isEmpty());
    }

    @Override
    protected Value trslBuilderWithValues(List<Object> list) throws RuntimeException {
        Value value = new Value();
        value.setSymbolValue("?");
        value.setTrueValues(Arrays.asList(list.get(0)));
        value.setValues(MysqlUtil.javaObjectToFetch(list.get(0)));
        return value;
    }

    @Override
    protected String operatorSymbol() {
        return " RLIKE ";
    }

    @Override
    public IOperator interestedOperator() {
        return RegularOperator.INSTANCE;
    }
}
