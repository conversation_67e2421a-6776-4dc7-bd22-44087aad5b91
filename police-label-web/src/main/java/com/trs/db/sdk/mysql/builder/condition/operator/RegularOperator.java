package com.trs.db.sdk.mysql.builder.condition.operator;

import com.trs.common.base.Option;
import com.trs.common.utils.expression.operator.IOperator;

/**
 * sparlk regularMatch操作符
 *
 * <AUTHOR>
 */
public class RegularOperator implements IOperator {

    public static final RegularOperator INSTANCE = new RegularOperator();

    private RegularOperator() {
    }


    @Override
    public Option<String> getOptionValue() {
        return Option.of("regular");
    }
}
