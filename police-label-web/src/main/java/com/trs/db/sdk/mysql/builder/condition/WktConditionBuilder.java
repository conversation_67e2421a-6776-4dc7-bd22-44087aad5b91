package com.trs.db.sdk.mysql.builder.condition;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.expression.Operator;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.db.sdk.builder.condition.AbstractConditionBuilder;
import com.trs.police.constant.GeoEnum;
import com.trs.police.vo.Geometries;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * wkt支持
 *
 * <AUTHOR>
 */
public class WktConditionBuilder extends AbstractConditionBuilder {

    @Override
    protected void validateValues(List<Object> values) throws RuntimeException {
        if (CollectionUtils.isEmpty(values)) {
            throw buildErrorMessage("values值个数必须大于0");
        }
    }

    @Override
    protected Value trslBuilderWithValues(List<Object> values) throws RuntimeException {
        throw new RuntimeException("WKT暂不支持");
    }

    @Override
    protected String operatorSymbol() {
        return "";
    }

    @Override
    public IOperator interestedOperator() {
        return Operator.WKT;
    }

    @Override
    protected StringBuilder trslBuilderWithFieldAndValues(String field, List<Object> values, List<Object> templateColumns) throws RuntimeException {
        List<String> sqlList = values.stream()
                .map(v -> buildSql(field, String.valueOf(v), Objects.isNull(templateColumns)))
                .collect(Collectors.toList());
        if (sqlList.size() == 1) {
            return new StringBuilder().append(sqlList.get(0));
        }
        return new StringBuilder()
                .append(
                        String.format("(%s)", sqlList.stream().collect(Collectors.joining(" OR ")))
                );
    }

    private String buildSql(String field, String value, Boolean isTemplate) {
        if (value.trim().startsWith("POLYGON")) {
            String sqlValue = isTemplate ? "?" : String.format("'%s'", value);
            return String.format("ST_Contains(ST_SRID(ST_PolygonFromText(%s), 4326),ST_SRID(%s, 4326))", sqlValue, field);
        }
        if (value.trim().startsWith("{")) {
            Geometries geometries = JSON.parseObject(value, Geometries.class);
            GeoEnum geoEnum = GeoEnum.fromCodeOrThrow(geometries.getType());
            switch (geoEnum) {
                case POLYGON:
                    String sqlValue = isTemplate ? "?" : String.format("'%s'", geometries.getGeometry());
                    return String.format("ST_Contains(ST_SRID(ST_PolygonFromText(%s), 4326),ST_SRID(%s, 4326))", sqlValue, field);
                case CIRCLE:
                    String lon = isTemplate ? "?" : geometries.getLon();
                    String lat = isTemplate ? "?" : geometries.getLat();
                    String sqlCircleDis = isTemplate ? "?" : geometries.getProperties().getRadius();
                    return String.format("ST_Distance_Sphere(ST_SRID(POINT(%s, %s), 4326), ST_SRID(%s, 4326)) <= %s", lon, lat, field, sqlCircleDis);
                default:
                    throw new RuntimeException("暂不支持的geo类型");
            }
        }
        throw new RuntimeException("暂不支持的geo类型");
    }
}
