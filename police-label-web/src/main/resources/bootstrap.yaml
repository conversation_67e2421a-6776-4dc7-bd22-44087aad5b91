spring:
  application:
    name: police-label
  cloud:
    nacos:
      server-addr: @spring.cloud.nacos.server-addr@
      username: @spring.cloud.nacos.username@
      password: @spring.cloud.nacos.password@
      config:
        file-extension: yaml
        namespace: @spring.cloud.nacos.namespace@
        extension-configs:
          - data-id: police-label.properties
            group: DEFAULT_GROUP
            refresh: true
          - data-id: commons-oauth2-client.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}

