CREATE TABLE IF NOT EXISTS `auth_certificate_kerberos` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `krb5_pth` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'krb5配置文件路径',
  `principal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户名称',
  `keytab_pth` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '密钥文件地址',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `t_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `p_id` bigint DEFAULT NULL COMMENT '上级主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '码值类型',
  `code` bigint DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '码值名称',
  `p_code` bigint DEFAULT NULL,
  `dict_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述信息',
  `show_number` int DEFAULT NULL COMMENT '排序字段',
  `standard` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '编码标准',
  `flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '其他标记',
  `color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '颜色',
  `status` int DEFAULT '1' COMMENT '状态 0=停用 1=启用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `TYPE_CODE_INDEX` (`type`,`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=170 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `tb_data_field` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '字段英文名',
  `field_name_cn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '字段中文名',
  `field_original_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '字段原始类型',
  `field_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '字段类型',
  `status` tinyint DEFAULT '0' COMMENT '可用状态: 0-可用, 1-不可用',
  `table_id` bigint DEFAULT NULL COMMENT '所属数据表ID',
  `feature_count` int DEFAULT '0' COMMENT '关联特征数量',
  `label_count` int DEFAULT '0' COMMENT '关联标签数量',
  `model_count` int DEFAULT '0' COMMENT '关联模型数量',
  `field_description` text COMMENT '字段描述信息',
  `selected_status` int DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数据字段表';

CREATE TABLE IF NOT EXISTS `tb_data_source` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据源名称',
  `type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据源类型 (ES/Mysql/接口等)',
  `connection_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '连接状态',
  `source_info` json DEFAULT NULL COMMENT '数据源详细信息 (JSON格式，根据不同类型存储不同结构)',
  `last_check_time` timestamp NULL DEFAULT NULL COMMENT '最后连接检查时间',
  `feature_count` int DEFAULT '0' COMMENT '关联特征数量',
  `label_count` int DEFAULT '0' COMMENT '关联标签数量',
  `model_count` int DEFAULT '0' COMMENT '关联模型数量',
  `deleted` bigint DEFAULT '0' COMMENT '是否删除',
  `create_status` int DEFAULT '0' COMMENT '数据源创建状态',
  `generate_unique_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '唯一key（生成唯一标识用于判断数据源是否重复）',
  `source_from` int DEFAULT NULL COMMENT '来源 0 默认 1 中台',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据源表';

CREATE TABLE IF NOT EXISTS `tb_data_table` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `data_source_id` bigint DEFAULT NULL COMMENT '所属数据源ID',
  `table_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表类型 (Table/View)',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表英文名',
  `table_name_cn` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表中文名',
  `business_description` text COLLATE utf8mb4_bin COMMENT '业务描述',
  `status` int DEFAULT '0' COMMENT '可用状态',
  `feature_label_status` int DEFAULT NULL COMMENT '关联特征标签状态',
  `feature_count` int DEFAULT '0' COMMENT '关联特征数量',
  `label_count` int DEFAULT '0' COMMENT '关联标签数量',
  `model_count` int DEFAULT '0' COMMENT '关联模型数量',
  `selected_status` int DEFAULT '0' COMMENT '是否被选中',
  `aliase_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表别名(目前是es和海贝有)',
  `id_field` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'id所在列名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据表数据';

CREATE TABLE IF NOT EXISTS `tb_feature` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户ID',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '创建用户ID',
  `update_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `feature_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '特征名称',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '特征描述',
  `category_code` bigint DEFAULT NULL COMMENT '所属分类ID',
  `police_kind` bigint DEFAULT NULL COMMENT '所属警种ID',
  `business_rule` text COLLATE utf8mb4_unicode_ci COMMENT '业务规则描述',
  `table_id` bigint DEFAULT NULL COMMENT '选用的表格ID',
  `output_fields` text COLLATE utf8mb4_unicode_ci COMMENT '输出字段（JSON结构，包括特征主体、对象类型等）',
  `color` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '颜色',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 0=停用 1=启用',
  `label_count` int NOT NULL DEFAULT '0' COMMENT '关联标签数量',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否已删除 0=未删除 1=已删除',
  `main_object_code` bigint DEFAULT NULL COMMENT '特征主体code',
  `process_order` text COLLATE utf8mb4_unicode_ci COMMENT '流程定义',
  `main_object_field` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '特征主体关联字段',
  `process_order_snapshot` text COLLATE utf8mb4_unicode_ci COMMENT '流程快照',
  PRIMARY KEY (`id`),
  KEY `idx_feature_name` (`feature_name`),
  KEY `idx_category` (`category_code`),
  KEY `idx_police_kind` (`police_kind`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`,`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='特征表';

CREATE TABLE IF NOT EXISTS `tb_formula` (
  `id` varchar(100) NOT NULL,
  `name` varchar(100) DEFAULT NULL COMMENT '公式名称',
  `description` varchar(1024) DEFAULT NULL COMMENT '描述',
  `syntax` varchar(1024) DEFAULT NULL COMMENT '语法',
  `example` varchar(1024) DEFAULT NULL COMMENT '示例',
  `parameters` json DEFAULT NULL COMMENT '参数',
  `return_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '返回类型',
  `category` varchar(100) DEFAULT NULL COMMENT '分组类型',
  `order_value` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `tb_label` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `label_name` varchar(255) DEFAULT NULL COMMENT '名称',
  `description` text COMMENT '描述',
  `category_code` bigint DEFAULT NULL COMMENT '所属分类ID',
  `police_kind` bigint DEFAULT NULL COMMENT '码表接口：type = ''feature_police_kind''',
  `business_rule` text COMMENT '业务规则描述',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色',
  `is_custom_color` tinyint DEFAULT NULL COMMENT '是否自定义颜色 0=否 1=是',
  `status` tinyint DEFAULT '0' COMMENT '状态 0=停用 1=启用',
  `label_count` int DEFAULT '0' COMMENT '关联标签数量',
  `deleted` tinyint DEFAULT '0' COMMENT '是否已删除 0=未删除 1=已删除',
  `process_order` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '流程定义',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `label_base_info` json DEFAULT NULL COMMENT '标签基础信息',
  `main_object` text COMMENT '标签主体',
  `related_object` text COMMENT '客体',
  `label_type` int DEFAULT NULL COMMENT '打标方式 0 手动 1 自动',
  `cycle_time_type` int DEFAULT NULL COMMENT '周期时间类型',
  `cycle_time` int DEFAULT NULL COMMENT '周期时间',
  `effective_time_type` int DEFAULT NULL COMMENT '有效时间方式',
  `effective_time` int DEFAULT NULL COMMENT '有效时间天数',
  `en_name` varchar(100) DEFAULT NULL COMMENT '标签英文名',
  `feature_id` json DEFAULT NULL COMMENT '使用的特征列表',
  `update_status` int DEFAULT NULL COMMENT '0-成功 1-等待计算 2-延迟计算',
  `last_finish_time` datetime DEFAULT NULL COMMENT '最后完成时间',
  `last_run_time` datetime DEFAULT NULL COMMENT '最后运行时间',
  `hits` bigint DEFAULT NULL COMMENT '命中数',
  `process_order_snapshot` text COMMENT '编排信息快照',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='标签信息表';

CREATE TABLE IF NOT EXISTS `tb_label_calculation_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `task_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '任务id',
  `label_id` bigint DEFAULT NULL COMMENT '标签ID',
  `label_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '标签名称',
  `police_kind` int DEFAULT NULL COMMENT '警种',
  `trigger_type` tinyint DEFAULT NULL COMMENT '触发类型 0=手动 1=定时',
  `job_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'spark任务id',
  `status` varchar(10) DEFAULT NULL COMMENT '任务运行状态',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `duration` int DEFAULT NULL COMMENT '执行耗时',
  `error_message` text COMMENT '错误信息',
  `execution_log` text COMMENT '执行日志',
  `deleted` tinyint DEFAULT '0' COMMENT '是否已删除 0=未删除 1=已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='标签计算任务表';

DROP PROCEDURE IF EXISTS InsertWhen;
DELIMITER //
CREATE PROCEDURE InsertWhen()
BEGIN
    DECLARE count INT DEFAULT 0;
    SELECT COUNT(*) INTO count FROM t_dict WHERE type = id <= 177;
    IF count = 0 THEN
        INSERT INTO `police-label`.t_dict (id,p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        	 (1,1,'tablesource_type_group',0,'数据源类型',0,'数据源类型',0,NULL,NULL,NULL,1),
        	 (2,1,'tablesource_type',1,'消息队列',1,'消息队列',1,NULL,NULL,NULL,1),
        	 (3,1,'tablesource_type',2,'数据库',1,'数据库',2,NULL,NULL,NULL,1),
        	 (4,1,'tablesource_type',3,'文件',1,'文件',3,NULL,NULL,NULL,1),
        	 (5,1,'tablesource_type',4,'API',1,'API',4,NULL,NULL,NULL,1),
        	 (6,2,'tablesource_type',5,'Kafka',2,'KAFKA',5,NULL,NULL,NULL,1),
        	 (7,2,'tablesource_type',6,'Rocketmq',2,'ROCKETMQ',6,NULL,NULL,NULL,1),
        	 (8,3,'tablesource_type',7,'Mysql',3,'MYSQL',7,NULL,NULL,NULL,1),
        	 (9,3,'tablesource_type',8,'Oracle',3,'ORACLE',8,NULL,NULL,NULL,1),
        	 (10,3,'tablesource_type',9,'Postgresql',3,'POSTGRESQL',9,NULL,NULL,NULL,1),
        	 (11,3,'tablesource_type',10,'Hive',3,'Hive',10,NULL,NULL,NULL,1),
        	 (12,3,'tablesource_type',11,'ClickHouse',3,'CLICKHOUSE',11,NULL,NULL,NULL,1),
        	 (13,3,'tablesource_type',12,'ES',3,'ES',12,NULL,NULL,NULL,1),
        	 (14,3,'tablesource_type',13,'海贝',3,'HYBASE',13,NULL,NULL,NULL,1),
        	 (15,4,'tablesource_type',14,'上传文件',4,'上传文件',14,NULL,NULL,NULL,1),
        	 (16,5,'tablesource_type',15,'Http',5,'Http',15,NULL,NULL,NULL,1),
        	 (17,17,'feature_police_kind_group',0,'特征警种分类',0,'特征警种分类',0,NULL,NULL,NULL,1),
        	 (18,17,'feature_police_kind',-1,'公共',0,'公共',1,NULL,NULL,NULL,1),
        	 (19,17,'feature_police_kind',1,'刑侦',0,'刑侦',2,NULL,NULL,NULL,1),
        	 (20,17,'feature_police_kind',2,'经侦',0,'经侦',3,NULL,NULL,NULL,1),
        	 (21,17,'feature_police_kind',3,'网安',0,'网安',4,NULL,NULL,NULL,1),
        	 (22,17,'feature_police_kind',4,'反恐',0,'反恐',5,NULL,NULL,NULL,1),
        	 (23,23,'feature_category_group',0,'特征类别',0,'特征类别',0,NULL,NULL,NULL,1),
        	 (24,23,'feature_category',1,'关系类',0,'关系类',1,NULL,NULL,NULL,1),
        	 (25,25,'feature_main_object_group',0,'特征主体',0,'特征主体',0,NULL,NULL,NULL,1),
        	 (26,25,'feature_main_object',1,'人',0,'特征主体',1,NULL,NULL,NULL,1),
        	 (27,25,'feature_main_object',2,'车',0,'特征主体',2,NULL,NULL,NULL,1),
        	 (28,28,'label_category_group',0,'标签类别',0,'标签类别',0,NULL,NULL,NULL,1),
        	 (29,28,'label_category',1,'关系类',0,'关系类',1,NULL,NULL,NULL,1),
        	 (30,28,'label_category',2,'背景类',0,'背景类',2,NULL,NULL,NULL,1),
        	 (31,28,'label_category',3,'行为类',0,'行为类',3,NULL,NULL,NULL,1),
        	 (32,28,'label_category',4,'违法犯罪',0,'违法犯罪',4,NULL,NULL,NULL,1),
        	 (48,28,'label_category',5,'案件类型',0,'案件类型',5,NULL,NULL,NULL,1),
        	 (49,28,'label_category',6,'作案手段',0,'作案手段',6,NULL,NULL,NULL,1),
        	 (57,30,'label_category',201,'证照',2,'证件相关标签',1,NULL,NULL,NULL,1),
        	 (58,30,'label_category',202,'年龄',2,'年龄相关标签',2,NULL,NULL,NULL,1),
        	 (59,30,'label_category',203,'外貌',2,'外貌特征标签',3,NULL,NULL,NULL,1),
        	 (60,30,'label_category',204,'家庭',2,'家庭情况标签',4,NULL,NULL,NULL,1),
        	 (61,30,'label_category',205,'教育',2,'教育背景标签',5,NULL,NULL,NULL,1),
        	 (62,30,'label_category',206,'职业',2,'职业信息标签',6,NULL,NULL,NULL,1),
        	 (63,30,'label_category',207,'身份',2,'身份信息标签',7,NULL,NULL,NULL,1),
        	 (64,30,'label_category',208,'财产',2,'财产状况标签',8,NULL,NULL,NULL,1),
        	 (65,30,'label_category',209,'婚姻状况',2,'婚姻情况标签',9,NULL,NULL,NULL,1),
        	 (66,30,'label_category',210,'省外发证地',2,'省外证件发放地标签',10,NULL,NULL,NULL,1),
        	 (67,30,'label_category',211,'省内发证地',2,'省内证件发放地标签',11,NULL,NULL,NULL,1),
        	 (68,30,'label_category',212,'民族',2,'民族信息标签',12,NULL,NULL,NULL,1),
        	 (69,30,'label_category',213,'星座',2,'星座信息标签',13,NULL,NULL,NULL,1),
        	 (70,30,'label_category',214,'宗教信仰',2,'宗教信仰标签',14,NULL,NULL,NULL,1),
        	 (71,30,'label_category',215,'社保',2,'社保情况标签',15,NULL,NULL,NULL,1),
        	 (72,30,'label_category',216,'违法犯罪',2,'违法犯罪记录标签',16,NULL,NULL,NULL,1),
        	 (73,30,'label_category',217,'涉嫌',2,'涉嫌违法标签',17,NULL,NULL,NULL,1),
        	 (74,30,'label_category',218,'车辆类型',2,'车辆类型标签',18,NULL,NULL,NULL,1),
        	 (75,30,'label_category',219,'车牌归属地',2,'车牌归属地标签',19,NULL,NULL,NULL,1),
        	 (76,30,'label_category',220,'车辆颜色',2,'车辆颜色标签',20,NULL,NULL,NULL,1),
        	 (77,30,'label_category',221,'档次',2,'车辆档次标签',21,NULL,NULL,NULL,1),
        	 (78,30,'label_category',222,'驾驶车龄',2,'驾驶年限标签',22,NULL,NULL,NULL,1),
        	 (79,30,'label_category',223,'车辆使用性质',2,'车辆使用性质标签',23,NULL,NULL,NULL,1),
        	 (80,30,'label_category',224,'车辆状况',2,'车辆状况标签',24,NULL,NULL,NULL,1),
        	 (81,30,'label_category',225,'车辆品牌',2,'车辆品牌标签',25,NULL,NULL,NULL,1),
        	 (82,30,'label_category',226,'归属地',2,'归属地标签',26,NULL,NULL,NULL,1),
        	 (83,30,'label_category',227,'运营商',2,'运营商标签',27,NULL,NULL,NULL,1),
        	 (84,29,'label_category',101,'亲属',1,'直系亲属关系标签',1,NULL,NULL,NULL,1),
        	 (85,29,'label_category',102,'人员属性',1,'人员属性',2,NULL,NULL,NULL,1),
        	 (86,31,'label_category',301,'数量偏多',3,'数量偏多',1,NULL,NULL,NULL,1),
        	 (87,31,'label_category',302,'行为异常',3,'行为异常',2,NULL,NULL,NULL,1),
        	 (88,31,'label_category',303,'手机使用异常',3,'手机使用异常',3,NULL,NULL,NULL,1),
        	 (89,31,'label_category',304,'风险行为',3,'风险行为',4,NULL,NULL,NULL,1),
        	 (90,31,'label_category',305,'违法行为',3,'违法行为',5,NULL,NULL,NULL,1),
        	 (91,32,'label_category',401,'涉案类型',4,'涉案类型',1,NULL,NULL,NULL,1),
        	 (92,32,'label_category',402,'作案手段',4,'作案手段',2,NULL,NULL,NULL,1),
        	 (93,32,'label_category',403,'机主违法',4,'机主违法',3,NULL,NULL,NULL,1),
        	 (94,49,'label_category',601,'冒充冒用',6,'冒充冒用',1,NULL,NULL,NULL,1),
        	 (95,49,'label_category',602,'窃取手段',6,'窃取手段',2,NULL,NULL,NULL,1),
        	 (96,49,'label_category',603,'胁迫手段',6,'胁迫手段',3,NULL,NULL,NULL,1),
        	 (97,49,'label_category',604,'解锁手段',6,'解锁手段',4,NULL,NULL,NULL,1),
        	 (98,49,'label_category',605,'进入方式',6,'进入方式',5,NULL,NULL,NULL,1),
        	 (99,49,'label_category',606,'诈骗手段',6,'诈骗手段',6,NULL,NULL,NULL,1),
        	 (100,49,'label_category',607,'箱体突破',6,'箱体突破',7,NULL,NULL,NULL,1),
        	 (101,49,'label_category',608,'干扰侦察',6,'干扰侦察',8,NULL,NULL,NULL,1),
        	 (102,48,'label_category',501,'危害公共安全',5,'危害公共安全',1,NULL,NULL,NULL,1),
        	 (103,48,'label_category',502,'妨害社会管理',5,'妨害社会管理',2,NULL,NULL,NULL,1),
        	 (104,48,'label_category',503,'侵犯公民权利',5,'侵犯公民权利',3,NULL,NULL,NULL,1),
        	 (105,48,'label_category',504,'盗窃案',5,'盗窃案',4,NULL,NULL,NULL,1),
        	 (106,48,'label_category',505,'抢劫案',5,'抢劫案',5,NULL,NULL,NULL,1),
        	 (107,48,'label_category',506,'诈骗案',5,'诈骗案',6,NULL,NULL,NULL,1),
        	 (108,48,'label_category',507,'飞车抢夺',5,'飞车抢夺',7,NULL,NULL,NULL,1),
        	 (109,48,'label_category',508,'损毁财物',5,'损毁财物',8,NULL,NULL,NULL,1),
        	 (112,23,'feature_category',2,'背景类',0,'背景类',2,NULL,NULL,NULL,1),
        	 (113,23,'feature_category',3,'行为类',0,'行为类',3,NULL,NULL,NULL,1),
        	 (114,23,'feature_category',4,'违法犯罪',0,'违法犯罪',4,NULL,NULL,NULL,1),
        	 (115,23,'feature_category',5,'案件类型',0,'案件类型',5,NULL,NULL,NULL,1),
        	 (116,23,'feature_category',6,'作案手段',0,'作案手段',6,NULL,NULL,NULL,1),
        	 (117,24,'feature_category',101,'亲属',1,'直系亲属关系标签',1,NULL,NULL,NULL,1),
        	 (118,24,'feature_category',102,'人员属性',1,'人员属性',2,NULL,NULL,NULL,1),
        	 (119,112,'feature_category',201,'证照',2,'证件相关标签',1,NULL,NULL,NULL,1),
        	 (120,112,'feature_category',202,'年龄',2,'年龄相关标签',2,NULL,NULL,NULL,1),
        	 (121,112,'feature_category',203,'外貌',2,'外貌特征标签',3,NULL,NULL,NULL,1),
        	 (122,112,'feature_category',204,'家庭',2,'家庭情况标签',4,NULL,NULL,NULL,1),
        	 (123,112,'feature_category',205,'教育',2,'教育背景标签',5,NULL,NULL,NULL,1),
        	 (124,112,'feature_category',206,'职业',2,'职业信息标签',6,NULL,NULL,NULL,1),
        	 (125,112,'feature_category',207,'身份',2,'身份信息标签',7,NULL,NULL,NULL,1),
        	 (126,112,'feature_category',208,'财产',2,'财产状况标签',8,NULL,NULL,NULL,1),
        	 (127,112,'feature_category',209,'婚姻状况',2,'婚姻情况标签',9,NULL,NULL,NULL,1),
        	 (128,112,'feature_category',210,'省外发证地',2,'省外证件发放地标签',10,NULL,NULL,NULL,1),
        	 (129,112,'feature_category',211,'省内发证地',2,'省内证件发放地标签',11,NULL,NULL,NULL,1),
        	 (130,112,'feature_category',212,'民族',2,'民族信息标签',12,NULL,NULL,NULL,1),
        	 (131,112,'feature_category',213,'星座',2,'星座信息标签',13,NULL,NULL,NULL,1),
        	 (132,112,'feature_category',214,'宗教信仰',2,'宗教信仰标签',14,NULL,NULL,NULL,1),
        	 (133,112,'feature_category',215,'社保',2,'社保情况标签',15,NULL,NULL,NULL,1),
        	 (134,112,'feature_category',216,'违法犯罪',2,'违法犯罪记录标签',16,NULL,NULL,NULL,1),
        	 (135,112,'feature_category',217,'涉嫌',2,'涉嫌违法标签',17,NULL,NULL,NULL,1),
        	 (136,112,'feature_category',218,'车辆类型',2,'车辆类型标签',18,NULL,NULL,NULL,1),
        	 (137,112,'feature_category',219,'车牌归属地',2,'车牌归属地标签',19,NULL,NULL,NULL,1),
        	 (138,112,'feature_category',220,'车辆颜色',2,'车辆颜色标签',20,NULL,NULL,NULL,1),
        	 (139,112,'feature_category',221,'档次',2,'车辆档次标签',21,NULL,NULL,NULL,1),
        	 (140,112,'feature_category',222,'驾驶车龄',2,'驾驶年限标签',22,NULL,NULL,NULL,1),
        	 (141,112,'feature_category',223,'车辆使用性质',2,'车辆使用性质标签',23,NULL,NULL,NULL,1),
        	 (142,112,'feature_category',224,'车辆状况',2,'车辆状况标签',24,NULL,NULL,NULL,1),
        	 (143,112,'feature_category',225,'车辆品牌',2,'车辆品牌标签',25,NULL,NULL,NULL,1),
        	 (144,112,'feature_category',226,'归属地',2,'归属地标签',26,NULL,NULL,NULL,1),
        	 (145,112,'feature_category',227,'运营商',2,'运营商标签',27,NULL,NULL,NULL,1),
        	 (146,113,'feature_category',301,'数量偏多',3,'数量偏多',1,NULL,NULL,NULL,1),
        	 (147,113,'feature_category',302,'行为异常',3,'行为异常',2,NULL,NULL,NULL,1),
        	 (148,113,'feature_category',303,'手机使用异常',3,'手机使用异常',3,NULL,NULL,NULL,1),
        	 (149,113,'feature_category',304,'风险行为',3,'风险行为',4,NULL,NULL,NULL,1),
        	 (150,113,'feature_category',305,'违法行为',3,'违法行为',5,NULL,NULL,NULL,1),
        	 (151,114,'feature_category',401,'涉案类型',4,'涉案类型',1,NULL,NULL,NULL,1),
        	 (152,114,'feature_category',402,'作案手段',4,'作案手段',2,NULL,NULL,NULL,1),
        	 (153,114,'feature_category',403,'机主违法',4,'机主违法',3,NULL,NULL,NULL,1),
        	 (154,115,'feature_category',501,'危害公共安全',5,'危害公共安全',1,NULL,NULL,NULL,1),
        	 (155,115,'feature_category',502,'妨害社会管理',5,'妨害社会管理',2,NULL,NULL,NULL,1),
        	 (156,115,'feature_category',503,'侵犯公民权利',5,'侵犯公民权利',3,NULL,NULL,NULL,1),
        	 (157,115,'feature_category',504,'盗窃案',5,'盗窃案',4,NULL,NULL,NULL,1),
        	 (158,115,'feature_category',505,'抢劫案',5,'抢劫案',5,NULL,NULL,NULL,1),
        	 (159,115,'feature_category',506,'诈骗案',5,'诈骗案',6,NULL,NULL,NULL,1),
        	 (160,115,'feature_category',507,'飞车抢夺',5,'飞车抢夺',7,NULL,NULL,NULL,1),
        	 (161,115,'feature_category',508,'损毁财物',5,'损毁财物',8,NULL,NULL,NULL,1),
        	 (162,116,'feature_category',601,'冒充冒用',6,'冒充冒用',1,NULL,NULL,NULL,1),
        	 (163,116,'feature_category',602,'窃取手段',6,'窃取手段',2,NULL,NULL,NULL,1),
        	 (164,116,'feature_category',603,'胁迫手段',6,'胁迫手段',3,NULL,NULL,NULL,1),
        	 (165,116,'feature_category',604,'解锁手段',6,'解锁手段',4,NULL,NULL,NULL,1),
        	 (166,116,'feature_category',605,'进入方式',6,'进入方式',5,NULL,NULL,NULL,1),
        	 (167,116,'feature_category',606,'诈骗手段',6,'诈骗手段',6,NULL,NULL,NULL,1),
        	 (168,116,'feature_category',607,'箱体突破',6,'箱体突破',7,NULL,NULL,NULL,1),
        	 (169,116,'feature_category',608,'干扰侦察',6,'干扰侦察',8,NULL,NULL,NULL,1),
        	 (170,NULL,'label_status_group',0,'标签执行状态',0,'标签执行状态',0,NULL,NULL,NULL,1),
        	 (171,170,'label_status',1,'PENDING',0,'等待执行',1,NULL,NULL,NULL,1),
        	 (173,170,'label_status',3,'RUNNING',0,'执行中',3,NULL,NULL,NULL,1),
        	 (174,170,'label_status',4,'SUCCESS',0,'执行成功',4,NULL,NULL,NULL,1),
        	 (175,170,'label_status',5,'FAILED',0,'执行失败',5,NULL,NULL,NULL,1),
        	 (176,170,'label_status',6,'CANCELLED',0,'已取消',6,NULL,NULL,NULL,1),
        	 (177,170,'label_status',7,'TIMEOUT',0,'超时',7,NULL,NULL,NULL,1);
    END IF;
END//
DELIMITER ;
CALL InsertWhen();
DROP PROCEDURE IF EXISTS InsertWhen;

DROP PROCEDURE IF EXISTS InsertWhen;
DELIMITER //
CREATE PROCEDURE InsertWhen()
BEGIN
    DECLARE count INT DEFAULT 0;
    SELECT COUNT(*) INTO count FROM tb_formula;
    IF count = 0 THEN
        INSERT INTO tb_formula (id,name,description,syntax,example,parameters,return_type,category,order_value) VALUES
        	 ('abs','绝对值','计算数值的绝对值','绝对值(数字)','绝对值(-5) => 5','[{"name": "数字", "type": "number", "description": "要计算绝对值的数值"}]','number','number',2),
        	 ('copy','复制字段','复制字段','复制字段(输入字段) =','输入字段(输入字段)  => 输入字段（复制）','[{"name": "输入字段", "type": "string", "description": "输入字段"}]','string','other',6),
        	 ('curdate','当前日期','提取当前日期','当前日期()','当前日期() => "2025-01-01"','[]','datetime','date',4),
        	 ('date_diff','日期相减','计算两个日期之间的时间差（毫秒）','日期相减(第一个日期, 第二个日期)','日期相减("2023-01-02", "2023-01-01") => 86400000','[{"name": "第一个日期", "type": "datetime", "description": "第一个日期"}, {"name": "第二个日期", "type": "datetime", "description": "第二个日期"}]','number','date',1),
        	 ('get_date_part','提取时间','提取到年月日，参数2: 日 月 年','提取时间(时间, 提取类型)','提取时间("2025-01-01 00:00:00", "日") => 1','[{"name": "时间", "type": "datetime", "description": "提取时间"}, {"name": "提取类型", "type": "string", "description": "提取类型 日 月 年"}]','number','date',3),
        	 ('merge_lat_lon','合并经纬度','合并经纬度','合并经纬度(经度, 纬度)','提取时间(100, 30) => "POINT (100 30)"','[{"name": "经度", "type": "number", "description": "经度"}, {"name": "纬度", "type": "number", "description": "纬度"}]','geo','geo',5),
        	 ('string_to_number','字符串转数字','字符串转数字','字符串转数字(字符串)','字符串转数字("-5") => 5','[{"name": "字符串", "type": "string", "description": "字符串"}]','number','type_convert',5);
    END IF;
END//
DELIMITER ;
CALL InsertWhen();
DROP PROCEDURE IF EXISTS InsertWhen;