<?xml version="1.0" encoding="UTF-8"?>
<!--
debug 级别
scan  是否自动扫描配置加载 使用jar包启动时无法自动扫描jar内部的文件
scanPeriod 扫描配置周期
-->
<configuration debug="false" scan="true" scanPeriod="10 seconds">
    <include resource="logback-included.xml"/>

    <logger name="com.trs.police.control.kafka.KafkaGroupWarningConsumer" level="info" additivity="false">
        <appender-ref ref="kafka"/>
    </logger>
    <logger name="com.trs.police.control.kafka.KafkaPersonWarningConsumer" level="info" additivity="false">
        <appender-ref ref="kafka"/>
    </logger>
    <logger name="com.trs.police.control.kafka.KafkaStxfWarningConsumer" level="info" additivity="false">
        <appender-ref ref="kafka"/>
    </logger>
    <logger name="com.trs.police.control.kafka.KafkaXlWarningConsumer" level="info" additivity="false">
        <appender-ref ref="kafka"/>
    </logger>
    <logger name="com.trs.police.control.kafka.KafkaFkrxyjWarningConsumer" level="info" additivity="false">
        <appender-ref ref="kafka"/>
    </logger>
</configuration>
