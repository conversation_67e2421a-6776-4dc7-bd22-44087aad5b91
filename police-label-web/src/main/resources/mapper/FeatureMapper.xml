<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.mapper.FeatureMapper">

    <!-- 分页查询特征列表 -->
    <select id="findFeaturePage" resultType="com.trs.police.entity.feature.FeatureDO">
        SELECT
         *
        FROM tb_feature f
        <where>
            f.deleted = 0
            <if test="dto.tableId != null">
                AND f.table_id = #{dto.tableId}
            </if>
            <if test="dto.tableName != null and dto.tableName != ''">
                AND EXISTS (SELECT 1 FROM tb_data_table WHERE id = f.table_id AND table_name_cn LIKE CONCAT('%',
                #{dto.tableName}, '%'))
            </if>
            <if test="dto.featureSubject != null and dto.featureSubject != ''">
                and JSON_CONTAINS(output_fields, #{dto.featureSubject, jdbcType=VARCHAR}, '$.content')
            </if>
            <if test="dto.categoryCode != null and dto.categoryCode != ''">
                AND f.category_Code = #{dto.categoryCode}
            </if>
            <if test="dto.policeKind != null">
                AND f.police_kind = #{dto.policeKind}
            </if>
            <if test="dto.mainObjectCode != null">
                AND f.main_object_code = #{dto.mainObjectCode}
            </if>
            <if test="null != dto.tableInputFieldIds and dto.tableInputFieldIds.size() > 0">
                <![CDATA[
                    AND JSON_CONTAINS(f.table_input_fields, JSON_ARRAY(#{dto.tableInputFieldIds}), '$')
                ]]>
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND f.create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND f.create_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                AND (
                f.feature_name LIKE CONCAT('%', #{dto.searchValue}, '%')
                OR f.description LIKE CONCAT('%', #{dto.searchValue}, '%')
                )
            </if>
            <if test="null != dto.dataSourceTableId">
                AND table_id in (select id from tb_data_table where data_source_id = #{dto.dataSourceTableId})
            </if>
        </where>
        <if test="dto.orderList != null and dto.orderList.size() > 0">
            ORDER BY
            <foreach collection="dto.orderList" item="order" separator=",">
                ${order.fieldName} ${order.orderType}
            </foreach>
        </if>
        <if test="dto.orderList == null or dto.orderList.size() == 0">
            ORDER BY f.create_time DESC
        </if>
    </select>

</mapper>
