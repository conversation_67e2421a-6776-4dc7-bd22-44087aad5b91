<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.mapper.DataFieldMapper">
    
    <!-- 批量插入数据字段 -->
    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_data_field (
            table_id, field_name, field_name_cn, field_type,
            field_original_type, field_description,
            create_user_id, create_dept_id, create_time,
            update_user_id, update_dept_id, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tableId}, #{item.fieldName}, #{item.fieldNameCn}, #{item.fieldType},
                #{item.fieldOriginalType}, #{item.fieldDescription},
                #{item.createUserId}, #{item.createDeptId}, #{item.createTime},
                #{item.updateUserId}, #{item.updateDeptId}, #{item.updateTime}
            )
        </foreach>
    </insert>
    
</mapper> 