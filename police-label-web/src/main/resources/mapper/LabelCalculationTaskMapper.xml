<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.mapper.LabelCalculationTaskMapper">

    <!-- 根据标签ID查询计算任务历史 -->
    <select id="findByLabelIdOrderByCreateTimeDesc" resultType="com.trs.police.entity.label.LabelCalculationTaskDO">
        SELECT *
        FROM tb_label_calculation_task
        WHERE label_id = #{labelId} AND deleted = 0
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据任务ID查询任务 -->
    <select id="findByTaskId" resultType="com.trs.police.entity.label.LabelCalculationTaskDO">
        SELECT *
        FROM tb_label_calculation_task
        WHERE task_id = #{taskId} AND deleted = 0
    </select>

    <!-- 根据任务ID查询任务 -->
    <select id="findByLabelId" resultType="com.trs.police.entity.label.LabelCalculationTaskDO">
        SELECT *
        FROM tb_label_calculation_task
        WHERE label_id = #{labelId} AND deleted = 0
        order by create_time DESC
        limit 1
    </select>

    <!-- 查询指定时间范围内的任务 -->
    <select id="findByTimeRange" resultType="com.trs.police.entity.label.LabelCalculationTaskDO">
        SELECT *
        FROM tb_label_calculation_task
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询正在运行的任务 -->
    <select id="findRunningTasks" resultType="com.trs.police.entity.label.LabelCalculationTaskDO">
        SELECT *
        FROM tb_label_calculation_task
        WHERE status = 'RUNNING' AND deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 查询超时的任务 -->
    <select id="findTimeoutTasks" resultType="com.trs.police.entity.label.LabelCalculationTaskDO">
        SELECT *
        FROM tb_label_calculation_task
        WHERE status = 'RUNNING'
        AND deleted = 0
        AND start_time &lt; DATE_SUB(#{nowTime}, INTERVAL #{timeoutMinutes} MINUTE)
        ORDER BY start_time ASC
    </select>

    <!-- 更新任务状态 -->
    <update id="updateTaskStatus">
        UPDATE tb_label_calculation_task
        SET status = #{status},
            end_time = #{endTime},
            error_message = #{errorMessage},
            update_time = #{updateTime}
        WHERE task_id = #{taskId}
    </update>

    <!-- 更新任务执行结果 -->
    <update id="updateTaskResult">
        UPDATE tb_label_calculation_task
        SET processed_count = #{processedCount},
            success_count = #{successCount},
            failed_count = #{failedCount},
            execution_log = #{executionLog},
            update_time = #{updateTime}
        WHERE task_id = #{taskId}
    </update>

    <!-- 获取标签最近一次成功执行的时间 -->
    <select id="getLastSuccessTime" resultType="java.time.LocalDateTime">
        SELECT MAX(end_time)
        FROM tb_label_calculation_task
        WHERE label_id = #{labelId} 
        AND status = 'SUCCESS' 
        AND deleted = 0
    </select>

    <!-- 获取标签最近一次执行的时间 -->
    <select id="getLastRunTime" resultType="java.time.LocalDateTime">
        SELECT (case when status = 'RUNNING' then "2100-01-01 00:00:00" else create_time end) as last_run_time
        FROM tb_label_calculation_task
        WHERE label_id = #{labelId}
        AND deleted = 0
        order by create_time desc
        limit 1
    </select>

    <!-- 统计标签的执行次数 -->
    <select id="countExecutions" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM tb_label_calculation_task
        WHERE label_id = #{labelId} AND deleted = 0
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

</mapper>
