<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd"  >
<mapper namespace="com.trs.police.mapper.DictMapper">
    <resultMap id="BaseTreeMap" type="com.trs.police.entity.baseEntiry.Dict">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="p_id" property="pId"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="p_code" property="pCode"/>
        <result column="dict_desc" property="dictDesc"/>
        <result column="show_number" property="showNumber"/>
        <result column="standard" property="standard"/>
        <result column="flag" property="flag"/>
        <collection column="id" property="children" ofType="com.trs.police.entity.baseEntiry.Dict" select="getByParentId"/>
    </resultMap>
    <select id="getByParentId" resultMap="BaseTreeMap">
        select *
        from t_dict
        where p_id = #{id,jdbcType=BIGINT}
        and p_id != id
        order by show_number
    </select>

    <select id="getByCodeAndType" resultMap="BaseTreeMap">
        select *
        from t_dict
        where type = #{type,jdbcType=VARCHAR}
        and code = #{code,jdbcType=BIGINT}
        and status = 1
        order by show_number
    </select>

</mapper>