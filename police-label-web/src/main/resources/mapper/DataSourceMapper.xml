<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd"  >
<mapper namespace="com.trs.police.mapper.DataSourceMapper">

    <select id="findAllDataType" resultType="com.trs.police.entity.datasource.DataSource">
        SELECT ds.*
        FROM tb_data_source ds
        LEFT JOIN tb_data_table dt ON ds.id = dt.data_source_id
        <where>
            dt.data_source_id IS NULL
            AND ds.deleted = 0
            AND ds.create_status = 1;
        </where>
    </select>
</mapper>