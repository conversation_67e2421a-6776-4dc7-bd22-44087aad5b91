<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.mapper.FeatureStatisticMapper">
    <select id="categoryCount" resultType="com.trs.police.service.feature.domain.value.FeatureCategoryCount">
        select category_code as categoryCode, count(1) as count
        from tb_feature tf
        <where>
            AND deleted = 0
            <if test="null != dto.policeKind">
                AND police_kind = #{dto.policeKind}
            </if>
            <if test="null != dto.categoryCode">
                AND category_code = #{dto.categoryCode}
            </if>
            <if test="null != dto.mainObjectCode">
                AND main_object_code = #{dto.mainObjectCode}
            </if>
            <if test="null != dto.searchValue">
                AND (
                feature_name LIKE CONCAT('%', #{dto.searchValue}, '%')
                )
            </if>
            <if test="null != dto.dataSourceTableId">
                AND table_id in (select id from tb_data_table where data_source_id = #{dto.dataSourceTableId})
            </if>
        </where>
        group by category_code
    </select>

    <select id="tableUseStatistic" resultType="com.trs.police.common.core.vo.CountVO">
        select table_id as id, count(1) as count
        from tb_feature
        <where>
            AND deleted = 0
            <if test="null != tbs">
                AND table_id in
                <foreach collection="tbs" item="it" open="(" close=")" separator=",">
                    #{it}
                </foreach>
            </if>
        </where>
        group by table_id
    </select>
</mapper>