<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.mapper.LabelMapper">

    <!-- 分页查询标签列表 -->
    <select id="findLabelPage" resultType="com.trs.police.entity.label.LabelDO">
        SELECT
            *
        FROM tb_label
        <where>
            deleted = 0
            <if test="dto.categoryCode != null and dto.categoryCode != ''">
                AND category_code = #{dto.categoryCode}
            </if>
            <if test="dto.categoryCodeList != null and dto.categoryCodeList.size()> 0">
                AND category_code IN
                <foreach collection="dto.categoryCodeList" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="dto.policeKind != null and dto.policeKind != ''">
                AND police_kind = #{dto.policeKind}
            </if>
            <if test="dto.updateStatus != null">
                AND update_status = #{dto.updateStatus}
            </if>
            <if test="dto.updateMethod != null">
                AND JSON_EXTRACT(process_order , '$.info.labelType') = #{dto.updateMethod}
            </if>
            <if test="dto.status != null">
                AND status = #{dto.status}
            </if>
            <if test="dto.createStartTime != null and dto.createStartTime != ''">
                AND create_time &gt;= #{dto.createStartTime}
            </if>
            <if test="dto.createEndTime != null and dto.createEndTime != ''">
                AND create_time &lt;= #{dto.createEndTime}
            </if>
            <if test="dto.mainObjectCode != null">
                AND JSON_CONTAINS(main_object , '{"mainObjectTypeCode": ${dto.mainObjectCode}}', '$')
            </if>
            <choose>
                <when test="dto.searchKey == 'labelName'">
                    and label_name like CONCAT('%', #{dto.searchValue}, '%')
                </when>
            </choose>
        </where>
        ORDER BY create_time DESC
    </select>
    
</mapper>
