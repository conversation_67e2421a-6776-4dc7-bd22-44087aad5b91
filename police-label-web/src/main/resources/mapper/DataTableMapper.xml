<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.mapper.DataTableMapper">
    
    <!-- 批量插入数据表 -->
    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_data_table (
            data_source_id, table_type, table_name, table_name_cn,
            create_user_id, create_dept_id, create_time,
            update_user_id, update_dept_id, update_time,aliase_name
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.dataSourceId}, #{item.tableType}, #{item.tableName}, #{item.tableNameCn},
                #{item.createUserId}, #{item.createDeptId}, #{item.createTime},
                #{item.updateUserId}, #{item.updateDeptId}, #{item.updateTime},#{item.aliaseName}
            )
        </foreach>
    </insert>
    
</mapper> 