<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.mapper.LabelStatisticMapper">

    <select id="categoryCount" resultType="com.trs.police.vo.label.domain.CategoryCount">
        SELECT
            category_code as categoryCode,
            count(1) as count
        FROM tb_label
        WHERE deleted = 0
        <if test="null != dto.policeKind">
            AND police_kind = #{dto.policeKind}
        </if>
        <if test="null != dto.categoryCode">
            AND category_code = #{dto.categoryCode}
        </if>
        <if test="null != dto.searchValue">
            AND (
                label_name LIKE CONCAT('%', #{dto.searchValue}, '%')
            )
        </if>
        <if test="null != dto.mainObjectCode">
            AND JSON_EXTRACT(main_object, '$[0].mainObjectTypeCode') = #{dto.mainObjectCode}
        </if>
        GROUP BY category_code
    </select>

    <select id="featureUseStatistic" resultType="com.trs.police.common.core.vo.CountVO">
        SELECT
            features.feature_id AS id,
            COUNT(*) AS count
        FROM (
            SELECT jt.feature_id
            FROM tb_label
            CROSS JOIN JSON_TABLE(
                feature_id,
                "$[*]" COLUMNS (feature_id VARCHAR(255) PATH "$")
            ) AS jt
        WHERE deleted = 0
        and jt.feature_id in <foreach collection="featureIds" open="(" separator="," item="it" close=")">#{it}</foreach>
        ) AS features
        GROUP BY feature_id
    </select>
</mapper>