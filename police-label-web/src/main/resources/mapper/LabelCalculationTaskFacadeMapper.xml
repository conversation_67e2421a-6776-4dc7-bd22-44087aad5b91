<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.service.shared.task.mapper.LabelCalculationTaskFacadeMapper">

    <select id="allLabelCalculationTasks" resultType="com.trs.police.entity.label.LabelCalculationTaskDO">
        SELECT t.*
        FROM tb_label_calculation_task t
        INNER JOIN (
            SELECT label_id, MAX(id) AS max_id
            FROM tb_label_calculation_task
             GROUP BY label_id
        ) latest ON t.label_id = latest.label_id AND t.id = latest.max_id
        WHERE t.deleted = 0;
    </select>

</mapper>