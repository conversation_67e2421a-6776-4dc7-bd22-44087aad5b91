{"等于文本函数": {"tableId": 2306, "tokens": [{"id": "condition-b1e11107_7ec8_4ec9_a516_ff6258fbe84f", "key": "date", "fromNode": "05835120_2d08_4ed8_9849_e7ffa0204207", "operator": "eq", "value": {"type": "string", "value": ["11111111"], "fromNode": ""}}]}, "等于正则表达式": {"tableId": 2306, "tokens": [{"id": "condition-b1e11107_7ec8_4ec9_a516_ff6258fbe84f", "key": "date", "fromNode": "05835120_2d08_4ed8_9849_e7ffa0204207", "operator": "eq", "value": {"type": "regular", "value": ["111111111"], "fromNode": ""}}]}, "等于自定义数值": {"tableId": 2306, "tokens": [{"id": "condition-a5053924_0e1c_43d5_86ec_95a8e1407c4f", "key": "date", "fromNode": "05835120_2d08_4ed8_9849_e7ffa0204207", "operator": "eq", "value": {"type": "number", "value": [1], "fromNode": ""}}]}, "等于一个月前": {"tableId": 2306, "tokens": [{"id": "condition-a5053924_0e1c_43d5_86ec_95a8e1407c4f", "key": "date", "fromNode": "05835120_2d08_4ed8_9849_e7ffa0204207", "operator": "eq", "value": {"type": "aMonthAgo", "value": [], "fromNode": ""}}]}, "自定义30天前": {"tableId": 2306, "tokens": [{"id": "condition-a5053924_0e1c_43d5_86ec_95a8e1407c4f", "key": "date", "fromNode": "05835120_2d08_4ed8_9849_e7ffa0204207", "operator": "eq", "value": {"type": "aFewDaysAgo", "value": [30], "fromNode": ""}}]}, "正则匹配": {"tableId": 1, "tokens": [{"id": "2", "key": "a_string", "fromNode": "1", "operator": "regularMatch", "value": {"type": "string", "value": ["北京"], "fromNode": ""}}]}, "正则不匹配": {"tableId": 1, "tokens": [{"id": "2", "key": "a_string", "fromNode": "1", "operator": "regularNotMatch", "value": {"type": "string", "value": ["北京"], "fromNode": ""}}]}, "开始是": {"tableId": 1, "tokens": [{"id": "2", "key": "a_string", "fromNode": "1", "operator": "startWith", "value": {"type": "string", "value": ["北京"], "fromNode": ""}}]}, "开始不是": {"tableId": 2306, "tokens": [{"id": "2", "key": "a_string", "fromNode": "1", "operator": "notStartWith", "value": {"type": "string", "value": ["北京"], "fromNode": ""}}]}, "结尾是": {"tableId": 1, "tokens": [{"id": "2", "key": "a_string", "fromNode": "1", "operator": "endWith", "value": {"type": "string", "value": ["北京"], "fromNode": ""}}]}, "结尾不是": {"tableId": 1, "tokens": [{"id": "2", "key": "a_string", "fromNode": "1", "operator": "notEndWith", "value": {"type": "string", "value": ["北京"], "fromNode": ""}}]}}