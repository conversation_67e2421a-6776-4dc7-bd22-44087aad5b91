package com.trs.police.sql;

import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.Operator;
import com.trs.db.sdk.core.dbtype.DBTypeEnum;
import com.trs.db.sdk.mysql.builder.condition.SparkWktOperator;
import com.trs.db.sdk.node.ExpressionNode;
import com.trs.db.sdk.node.ExpressionNodes;
import org.junit.Test;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import static com.trs.common.utils.expression.ExpressionBuilder.And;
import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * *@author:wen.wen
 * *@create 2021-09-29 16:54
 **/
public class SqlTests {


    @Test
    public void testWkt() {
        Expression expression =And(
                Condition("point", Operator.WKT, "POLYGON((116.3 39.9, 116.3 40.0, 116.4 40.0, 116.4 39.9, 116.3 39.9))"),
                Condition("a", Operator.Equal, "1"),
                Condition("point", SparkWktOperator.INSTANCE, "POLYGON((116.3 39.9, 116.3 40.0, 116.4 40.0, 116.4 39.9, 116.3 39.9))")
        );
        ExpressionNode expressionNode = ExpressionNodes.parseExpression(expression, DBTypeEnum.MYSQL);
        LinkedList linkedList = new LinkedList<>();
        String sql = expressionNode.getFinalTemplateSql(linkedList);
        String finalSql = expressionNode.getFinalSql();
        System.out.println(sql);
    }

    @Test
    public void testWkt1() {
        List<String> sqlList = Arrays.asList(
                "{\n" +
                        "      \"type\": \"circle\",\n" +
                        "      \"geometry\": \"POINT(104.068379 30.605988)\",\n" +
                        "      \"properties\": {\n" +
                        "        \"radius\": \"764.434155\"\n" +
                        "      }\n" +
                        "    }",
                "{\n" +
                        "      \"type\": \"polygon\",\n" +
                        "      \"geometry\": \"POLYGON((104.066227 30.589817,104.077614 30.58473,104.077614 30.58473,104.075186 30.594268,104.070582 30.5938,104.066227 30.589817))\"\n" +
                        "    }"
        );
        Expression expression = Condition("point", Operator.WKT, sqlList);
        ExpressionNode expressionNode = ExpressionNodes.parseExpression(expression, DBTypeEnum.MYSQL);
        LinkedList linkedList = new LinkedList<>();
        String sql = expressionNode.getFinalTemplateSql(linkedList);
        String finalSql = expressionNode.getFinalSql();
        System.out.println(sql);
    }
}
