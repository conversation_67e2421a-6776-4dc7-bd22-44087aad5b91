package com.trs.police.spark;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.dto.label.LabelComputeDTO;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.label.LabelCalculationService;
import com.trs.police.service.label.LabelScheduleService;
import com.trs.police.service.label.LabelService;
import com.trs.police.service.label.impl.LabelCalculationManager;
import com.trs.police.vo.LabelComputeStatusVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class LabelCalculationServiceTest {
    @Autowired
    private LabelCalculationService labelCalculationService;
    @Autowired
    private LabelCalculationManager labelCalculationManager;
    @Autowired
    private LabelService labelService;
    @Autowired
    private LabelScheduleService labelScheduleService;

    @Test
    public void testTriggerManualCalculation() {
        String result = labelCalculationService.triggerManualCalculation(119L);
        System.out.println(result);
    }

    @Test
    public void labelComputeDtoTest() {
        LabelComputeDTO labelComputeDTO = labelCalculationManager.buildLabelComputeDTO(119L);
        System.out.println(JSONObject.toJSONString(labelComputeDTO));
    }

    @Test
    public void labelComputeResultTest() {
        NodeData nodeData = labelService.previewNode(78L);
        System.out.println();
    }

    @Test
    public void testUpdateTaskStatus() {
        labelScheduleService.updateTaskStatus();
        System.out.println();
    }

    @Test
    public void testGetTaskStatus() {
        LabelComputeStatusVO vo = labelCalculationService.getTaskStatus(69L);
        System.out.println(vo);
    }
}