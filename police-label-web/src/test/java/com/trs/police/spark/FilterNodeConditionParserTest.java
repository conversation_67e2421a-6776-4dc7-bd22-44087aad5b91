package com.trs.police.spark;

import com.trs.common.utils.expression.Expression;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.FilterNodeConditionParser;
import org.junit.Test;

public class FilterNodeConditionParserTest {



    @Test
    public void test() {
        String condition = "[{\"id\":\"condition-029a366a-6b0a-4b35-86d4-8ba1523247a0\",\"key\":\"hour\",\"operator\":\"eq\",\"value\":{\"type\":\"string\",\"value\":[\"15\"]}},\"(\",{\"id\":\"condition-4219401f-860a-41c1-8941-5e20be63a7ae\",\"key\":\"year\",\"operator\":\"gt\",\"value\":{\"type\":\"string\",\"value\":[\"0\"]}},{\"id\":\"condition-99a669d7-5777-496e-8687-3785392e41b8\",\"key\":\"year\",\"operator\":\"lt\",\"value\":{\"type\":\"string\",\"value\":[\"99\"]}},\")\",\"(\",{\"id\":\"condition-e033c738-102c-46e0-af07-ca4e41ac62ba\",\"key\":\"year\",\"operator\":\"eq\",\"value\":{\"type\":\"string\",\"value\":[\"1\"]}},\"或\",{\"id\":\"condition-c19354a4-e0c8-482d-a6a5-ddf9c0a2f4fb\",\"key\":\"year\",\"operator\":\"eq\",\"value\":{\"type\":\"string\",\"value\":[\"15\"]}},\"或\",{\"id\":\"condition-1f0b25f4-a7d4-4126-9049-4786118e9cc3\",\"key\":\"year\",\"operator\":\"in\",\"value\":{\"type\":\"string\",\"value\":[\"1\",\"2\",\"3\"]}},\"或\",{\"id\":\"condition-82ac8121-8131-4fb5-9e33-0b495963c094\",\"key\":\"year\",\"operator\":\"regularMatch\",\"value\":{\"type\":\"string\",\"value\":[\"aaa\"]}},\")\"]";
        FilterNodeConditionParser parser = new FilterNodeConditionParser(new NodeContext(null));
        Expression expression = parser.parseCondition(condition);

        System.out.println();
    }
}
