package com.trs.police.node;

import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.impl.FilterNode;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * 过滤节点测试
 *
 * <AUTHOR>
 */
public class FilterNodeTest extends BaseNodeTest {

    @Test
    public void testPolygonIn() throws Exception {
        String properties = getTestNodeProperties("filter_polygon_in");
        NodeData nodeData = getNodeData();
        FilterNode filterNode = new FilterNode(nodeData.getNodeMeta(), properties);
        NodeData process = filterNode.process(Arrays.asList(nodeData), new NodeContext(new ArrayList<>()));
        Assert.assertTrue("过滤节点测试失败", !process.getRowList().isEmpty());
    }

    @Test
    public void testPolygonOut() throws Exception {
        String properties = getTestNodeProperties("filter_polygon_out");
        NodeData nodeData = getNodeData();
        FilterNode filterNode = new FilterNode(nodeData.getNodeMeta(), properties);
        NodeData process = filterNode.process(Arrays.asList(nodeData), new NodeContext(new ArrayList<>()));
        Assert.assertTrue("过滤节点测试失败", process.getRowList().isEmpty());
    }

    @Test
    public void testCircleIn() throws Exception {
        String properties = getTestNodeProperties("filter_polygon_in");
        NodeData nodeData = getNodeData();
        FilterNode filterNode = new FilterNode(nodeData.getNodeMeta(), properties);
        NodeData process = filterNode.process(Arrays.asList(nodeData), new NodeContext(new ArrayList<>()));
        Assert.assertTrue("过滤节点测试失败", !process.getRowList().isEmpty());
    }

    @Test
    public void testCircleOut() throws Exception {
        String properties = getTestNodeProperties("filter_circle_out");
        NodeData nodeData = getNodeData();
        FilterNode filterNode = new FilterNode(nodeData.getNodeMeta(), properties);
        NodeData process = filterNode.process(Arrays.asList(nodeData), new NodeContext(new ArrayList<>()));
        Assert.assertTrue("过滤节点测试失败", process.getRowList().isEmpty());
    }
}
