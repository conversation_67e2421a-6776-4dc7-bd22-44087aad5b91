package com.trs.police.node;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.EmtpyExpression;
import com.trs.common.utils.expression.Expression;
import com.trs.db.sdk.core.dbtype.DBTypeEnum;
import com.trs.db.sdk.node.ExpressionNode;
import com.trs.db.sdk.node.ExpressionNodes;
import com.trs.es.esbean.builder.EsSearchBuilderFactory;
import com.trs.es.esbean.builder.EsSearchBuilderImpl;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.properties.TableNodeProperties;
import com.trs.police.service.node.FilterNodeConditionParser;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * 表输入节点sql测试
 *
 * <AUTHOR>
 */
public class TableInputNodeSqlTest extends BaseNodeTest {



    // 开头匹配
    @Test
    public void startWithTest() throws Exception {
        Expression expression = getExpression("开始是");

        String esSql = getEsSql(expression);
        Assert.assertTrue(esSql.contains("\"北京*\""));

        List<String> mysqlSql = getMysqlSql(expression);
        Assert.assertTrue("a_string LIKE '北京%'".equals(mysqlSql.get(1)));
    }

    // 开头不匹配
    @Test
    public void notStartWithTest() throws Exception {
        Expression expression = getExpression("开始不是");

        String esSql = getEsSql(expression);
        Assert.assertTrue(esSql.contains("\"北京*\""));


        List<String> mysqlSql = getMysqlSql(expression);
        Assert.assertTrue("( NOT a_string LIKE '北京%')".equals(mysqlSql.get(1)));
    }

    // 结尾匹配
    @Test
    public void endWithTest() throws Exception {
        Expression expression = getExpression("结尾是");

        String esSql = getEsSql(expression);
        Assert.assertTrue(esSql.contains("\"*北京\""));

        List<String> mysqlSql = getMysqlSql(expression);
        Assert.assertTrue("a_string LIKE '%北京'".equals(mysqlSql.get(1)));
    }

    // 结尾不匹配
    @Test
    public void notEndWithTest() throws Exception {
        Expression expression = getExpression("结尾不是");

        String esSql = getEsSql(expression);
        Assert.assertTrue(esSql.contains("\"*北京\""));


        List<String> mysqlSql = getMysqlSql(expression);
        Assert.assertTrue("( NOT a_string LIKE '%北京')".equals(mysqlSql.get(1)));
    }

    // 正则匹配
    @Test
    public void regularMatchTest() throws Exception {
        Expression expression = getExpression("正则匹配");

        String esSql = getEsSql(expression);
        Assert.assertTrue(esSql.contains("regexp"));

        List<String> mysqlSql = getMysqlSql(expression);
        Assert.assertTrue("a_string RLIKE '北京'".equals(mysqlSql.get(1)));
    }

    // 正则不匹配
    @Test
    public void regularNotMatchTest() throws Exception {
        Expression expression = getExpression("正则不匹配");

        String esSql = getEsSql(expression);
        Assert.assertTrue(esSql.contains("regexp") && esSql.contains("not"));

        List<String> mysqlSql = getMysqlSql(expression);
        Assert.assertTrue("( NOT a_string RLIKE '北京')".equals(mysqlSql.get(1)));
    }

    private Expression getExpression(String name) throws Exception {
        String properties = getTestNodeProperties("table");
        JSONObject jsonObject = JSON.parseObject(properties);
        TableNodeProperties property = jsonObject.getObject(name, TableNodeProperties.class);
        FilterNodeConditionParser parser = new FilterNodeConditionParser(new NodeContext(new ArrayList<>()));
        Expression expression = StringUtils.isNotEmpty(property.getTokens())
                ? parser.parseCondition(property.getTokens())
                : new EmtpyExpression();
        return expression;
    }

    private String getEsSql(Expression expression) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        EsSearchBuilderImpl esSearchBuilder = (EsSearchBuilderImpl) EsSearchBuilderFactory.createNewBuilder(0, 40)
                .where(expression);
        QueryBuilder queryBuilder = esSearchBuilder.getBoolQueryBuilder();
        searchSourceBuilder.query(queryBuilder);
        return searchSourceBuilder.toString();
    }

    private List<String> getMysqlSql(Expression expression) {
        ExpressionNode expressionNode = ExpressionNodes.parseExpression(expression, DBTypeEnum.MYSQL);
        LinkedList linkedList = new LinkedList<>();
        String sql = expressionNode.getFinalTemplateSql(linkedList);
        String finalSql = expressionNode.getFinalSql();
        return Arrays.asList(sql, finalSql);
    }
}
