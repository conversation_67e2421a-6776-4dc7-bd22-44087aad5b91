package com.trs.police.node;

import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.common.core.vo.node.Row;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.RowMeta;
import com.trs.police.dto.node.ValueMateBase;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.formula.FormulaParser;
import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public abstract class BaseNodeTest {

    protected FormulaParser formulaParser = new FormulaParser();

    protected String getTestJson(String name) throws Exception {
        try (InputStream inputStream = BaseNodeTest.class.getClassLoader().getResourceAsStream("function/" + name + ".txt")) {
            return IOUtils.toString(inputStream);
        }
    }

    protected NodeData getNodeData() {
        NodeData nodeData = new NodeData();

        // 输出信息
        nodeData.setNodeMeta(new NodeMeta());
        nodeData.getNodeMeta().setOutputRowMeta(new RowMeta());
        nodeData.getNodeMeta().getOutputRowMeta().setValueMetaList(Arrays.asList(
                new ValueMateBase("1", "a", "a", "string"),
                new ValueMateBase("2", "b", "b", "number"),
                new ValueMateBase("3", "c", "c", "datetime")
        ));

        // 表头
        List<FieldInfoVO> heder = nodeData.getNodeMeta()
                .getOutputRowMeta()
                .getValueMetaList()
                .stream()
                .map(meta -> new FieldInfoVO(meta.getCnName(), meta.getEnName(), meta.getTypeCode(), meta.getFromNode()))
                .collect(Collectors.toList());
        nodeData.setHeader(heder);

        // 数据
        List<Row> rowData = new ArrayList<>();
        for (int i = 0; i < nodeData.getData().size(); i++) {
            Row row = new Row();
            row.setRowData(Arrays.asList(
                    new FieldValue("" + i + "s", nodeData.getHeader().get(0)),
                    new FieldValue("" + i, nodeData.getHeader().get(1)),
                    new FieldValue("2025-01-01 00:00:00", nodeData.getHeader().get(2))
            ));
        }
        nodeData.setRowList(rowData);
        return nodeData;
    }
}
