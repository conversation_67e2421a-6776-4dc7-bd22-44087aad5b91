package com.trs.police.node;

import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.service.node.formula.Expression;
import com.trs.police.service.node.formula.FormulaContext;
import com.trs.police.service.node.formula.FormulaParser;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

/**
 * 函数测试
 */
public class FunctionTest extends BaseNodeTest {


    @Test
    public void absTest() throws Exception {
        String abs = getTestJson("abs");
        Expression parse = FormulaParser.parse(abs);
        FieldValue evaluate = parse.evaluate(new FormulaContext());
        Assert.assertEquals("1.0", evaluate.getValue());
    }

    @Test
    public void dateDiffTest() throws Exception {
        String abs = getTestJson("date_diff");
        Expression parse = FormulaParser.parse(abs);
        FieldValue evaluate = parse.evaluate(new FormulaContext());
        Assert.assertEquals("1000", evaluate.getValue());
    }
}
