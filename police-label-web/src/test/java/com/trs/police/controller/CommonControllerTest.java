package com.trs.police.controller;

import com.trs.police.service.baseService.CommonService;
import com.trs.police.vo.common.FormulaVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Test class for CommonController
 */
public class CommonControllerTest {

    @Mock
    private CommonService commonService;

    @InjectMocks
    private CommonController commonController;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(commonController).build();
    }

    @Test
    public void testUploadKerberosFile() throws Exception {
        // Prepare test data
        String userName = "testUser";
        String type = "testType";
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.keytab",
                MediaType.APPLICATION_OCTET_STREAM_VALUE,
                "test content".getBytes()
        );

        // Mock service behavior
        doNothing().when(commonService).uploadKerberosFile(eq(userName), eq(type), any());

        // Perform request and verify
        mockMvc.perform(multipart("/api/common/uploadKerberosFile")
                .file(file)
                .param("userName", userName)
                .param("type", type))
                .andExpect(status().isOk());

        // Verify service was called
        verify(commonService, times(1)).uploadKerberosFile(eq(userName), eq(type), any());
    }

    @Test
    public void testGetKerberosFileList() throws Exception {
        // Prepare test data
        String userName = "testUser";
        String type = "testType";
        List<String> expectedFiles = Arrays.asList("file1.keytab", "file2.keytab");

        // Mock service behavior
        when(commonService.getKerberosFileList(userName, type)).thenReturn(expectedFiles);

        // Perform request and verify
        mockMvc.perform(get("/api/common/getKerberosFileList")
                .param("userName", userName)
                .param("type", type))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0]").value("file1.keytab"))
                .andExpect(jsonPath("$[1]").value("file2.keytab"));

        // Verify service was called
        verify(commonService, times(1)).getKerberosFileList(userName, type);
    }

    @Test
    public void testGetFormulaList() throws Exception {
        // Prepare test data
        FormulaVO formula1 = new FormulaVO();
        formula1.setName("Formula1");
        formula1.setDescription("Description1");

        FormulaVO formula2 = new FormulaVO();
        formula2.setName("Formula2");
        formula2.setDescription("Description2");

        List<FormulaVO> expectedFormulas = Arrays.asList(formula1, formula2);

        // Mock service behavior
        when(commonService.getFormulaList()).thenReturn(expectedFormulas);

        // Perform request and verify
        mockMvc.perform(get("/api/common/getFormulaList"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].name").value("Formula1"))
                .andExpect(jsonPath("$[0].description").value("Description1"))
                .andExpect(jsonPath("$[1].name").value("Formula2"))
                .andExpect(jsonPath("$[1].description").value("Description2"));

        // Verify service was called
        verify(commonService, times(1)).getFormulaList();
    }
}