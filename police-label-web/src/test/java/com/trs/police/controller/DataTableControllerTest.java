package com.trs.police.controller;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.dto.DataTableDTO;
import com.trs.police.dto.DataTableOverviewDto;
import com.trs.police.dto.TableSelectionDTO;
import com.trs.police.service.datatable.DataTableService;
import com.trs.police.vo.DataTableVO;
import com.trs.web.builder.base.RestfulResultsV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Test class for DataTableController
 */
public class DataTableControllerTest {

    @Mock
    private DataTableService dataTableService;

    @InjectMocks
    private DataTableController dataTableController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(dataTableController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testGetAllDataTable() throws Exception {
        // Prepare test data
        TableSelectionDTO dto = new TableSelectionDTO();
        dto.setDataSourceId(1L);
        
        List<DataTableVO> tableList = new ArrayList<>();
        DataTableVO tableVO = new DataTableVO();
        tableVO.setId(1L);
        tableVO.setName("Test Table");
        tableList.add(tableVO);
        
        RestfulResultsV2<DataTableVO> expectedResult = new RestfulResultsV2<>();
        expectedResult.setCode(200);
        expectedResult.setMessage("Success");
        expectedResult.setData(tableList);
        
        // Mock service behavior
        when(dataTableService.getAllDataTable(any(TableSelectionDTO.class))).thenReturn(expectedResult);
        
        // Perform request and verify
        mockMvc.perform(get("/api/datatable/getAllDataTable")
                .param("dataSourceId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("Test Table"));
        
        // Verify service was called
        verify(dataTableService, times(1)).getAllDataTable(any(TableSelectionDTO.class));
    }
    
    @Test
    public void testUpdateDataSource() throws Exception {
        // Prepare test data
        Long tableId = 1L;
        DataTableDTO dto = new DataTableDTO();
        dto.setName("Updated Table");
        
        // Perform request and verify
        mockMvc.perform(get("/api/datatable/{id}/updateTable", tableId)
                .param("name", "Updated Table"))
                .andExpect(status().isOk());
        
        // Verify service was called
        verify(dataTableService, times(1)).updateDataSource(eq(tableId), any(DataTableDTO.class));
    }
    
    @Test
    public void testSelectTables() throws Exception {
        // Prepare test data
        TableSelectionDTO dto = new TableSelectionDTO();
        dto.setDataSourceId(1L);
        List<Long> selectedIds = new ArrayList<>();
        selectedIds.add(1L);
        selectedIds.add(2L);
        dto.setSelectedIds(selectedIds);
        
        // Perform request and verify
        mockMvc.perform(post("/api/datatable/selectTables")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk());
        
        // Verify service was called
        verify(dataTableService, times(1)).selectTables(any(TableSelectionDTO.class));
    }
    
    @Test
    public void testDelete() throws Exception {
        // Prepare test data
        Long tableId = 1L;
        
        // Perform request and verify
        mockMvc.perform(get("/api/datatable/{id}", tableId))
                .andExpect(status().isOk());
        
        // Verify service was called
        verify(dataTableService, times(1)).delete(eq(tableId));
    }
    
    @Test
    public void testDatatableDetail() throws Exception {
        // Prepare test data
        Integer tableId = 1;
        
        DataTableVO tableVO = new DataTableVO();
        tableVO.setId(1L);
        tableVO.setName("Test Table");
        
        RestfulResultsV2<DataTableVO> expectedResult = new RestfulResultsV2<>();
        expectedResult.setCode(200);
        expectedResult.setMessage("Success");
        expectedResult.setData(tableVO);
        
        // Mock service behavior
        when(dataTableService.datatableDetail(eq(tableId))).thenReturn(expectedResult);
        
        // Perform request and verify
        mockMvc.perform(get("/api/datatable/datatableDetail")
                .param("id", tableId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("Test Table"));
        
        // Verify service was called
        verify(dataTableService, times(1)).datatableDetail(eq(tableId));
    }
    
    @Test
    public void testGetDataOverview() throws Exception {
        // Prepare test data
        DataTableOverviewDto dto = new DataTableOverviewDto();
        dto.setTableId(1L);
        
        JSONObject dataOverview = new JSONObject();
        dataOverview.put("totalCount", 100);
        dataOverview.put("columns", new ArrayList<>());
        dataOverview.put("rows", new ArrayList<>());
        
        RestfulResultsV2<JSONObject> expectedResult = new RestfulResultsV2<>();
        expectedResult.setCode(200);
        expectedResult.setMessage("Success");
        expectedResult.setData(dataOverview);
        
        // Mock service behavior
        when(dataTableService.getDataOverview(any(DataTableOverviewDto.class))).thenReturn(expectedResult);
        
        // Perform request and verify
        mockMvc.perform(post("/api/datatable/getDataOverview")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.totalCount").value(100));
        
        // Verify service was called
        verify(dataTableService, times(1)).getDataOverview(any(DataTableOverviewDto.class));
    }
    
    @Test
    public void testRefreshTable() throws Exception {
        // Prepare test data
        Long tableId = 1L;
        
        // Perform request and verify
        mockMvc.perform(get("/api/datatable/refreshTable")
                .param("tableId", tableId.toString()))
                .andExpect(status().isOk());
        
        // Verify service was called
        verify(dataTableService, times(1)).refreshTableById(eq(tableId));
    }
}