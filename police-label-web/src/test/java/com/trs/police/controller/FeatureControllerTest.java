package com.trs.police.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.service.feature.application.DTO.command.FeatureAddDTO;
import com.trs.police.service.feature.application.DTO.command.FeatureProcessDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureCategoryStatisticDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureProcessPreviewDTO;
import com.trs.police.service.feature.application.DTO.query.FeatureSearchDTO;
import com.trs.police.service.feature.application.DTO.vo.FeatureCategoryStatisticVO;
import com.trs.police.service.feature.application.DTO.vo.FeatureDetailVO;
import com.trs.police.service.feature.application.DTO.vo.FeatureVO;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.feature.application.service.FeatureService;
import com.trs.police.service.feature.application.service.FeatureStatisticService;
import com.trs.police.service.node.NodeService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Test class for FeatureController
 */
public class FeatureControllerTest {

    @Mock
    private FeatureService featureService;

    @Mock
    private NodeService nodeService;

    @Mock
    private FeatureStatisticService featureStatisticService;

    @InjectMocks
    private FeatureController featureController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(featureController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testListFeatures() throws Exception {
        // Prepare test data
        FeatureSearchDTO searchDTO = new FeatureSearchDTO();
        searchDTO.setPageNum(1);
        searchDTO.setPageSize(10);
        
        List<FeatureVO> featureList = new ArrayList<>();
        FeatureVO featureVO = new FeatureVO();
        featureVO.setFeatureId(1L);
        featureVO.setFeatureName("Test Feature");
        featureList.add(featureVO);
        
        RestfulResultsV2<FeatureVO> expectedResult = RestfulResultsV2.ok(featureList);
        
        // Mock service behavior
        when(featureService.findFeatures(any(FeatureSearchDTO.class))).thenReturn(expectedResult);
        
        // Perform request and verify
        mockMvc.perform(post("/api/feature/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(searchDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("Test Feature"));
        
        // Verify service was called
        verify(featureService, times(1)).findFeatures(any(FeatureSearchDTO.class));
    }
    
    @Test
    public void testDeleteFeature() throws Exception {
        // Prepare test data
        Long featureId = 1L;
        
        // Mock service behavior
        when(featureService.deleteFeature(eq(featureId))).thenReturn(true);
        
        // Perform request and verify
        mockMvc.perform(delete("/api/feature/{id}", featureId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(true));
        
        // Verify service was called
        verify(featureService, times(1)).deleteFeature(eq(featureId));
    }
    
    @Test
    public void testToggleFeatureStatus() throws Exception {
        // Prepare test data
        Long featureId = 1L;
        Integer status = 1;
        
        // Mock service behavior
        when(featureService.toggleFeatureStatus(eq(featureId), eq(status))).thenReturn(true);
        
        // Perform request and verify
        mockMvc.perform(put("/api/feature/{id}/toggle/{status}", featureId, status))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(true));
        
        // Verify service was called
        verify(featureService, times(1)).toggleFeatureStatus(eq(featureId), eq(status));
    }
    
    @Test
    public void testDetail() throws Exception {
        // Prepare test data
        Long featureId = 1L;
        
        FeatureDetailVO detailVO = new FeatureDetailVO();
        detailVO.setFeatureId(featureId);
        detailVO.setFeatureName("Test Feature");
        
        // Mock service behavior
        when(featureService.detail(eq(featureId))).thenReturn(detailVO);
        
        // Perform request and verify
        mockMvc.perform(get("/api/feature/detail")
                .param("featureId", featureId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("Test Feature"));
        
        // Verify service was called
        verify(featureService, times(1)).detail(eq(featureId));
    }
    
    @Test
    public void testCopy() throws Exception {
        // Prepare test data
        Long featureId = 1L;
        Long newFeatureId = 2L;
        
        // Mock service behavior
        when(featureService.copy(eq(featureId))).thenReturn(newFeatureId);
        
        // Perform request and verify
        mockMvc.perform(post("/api/feature/{id}/copy", featureId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(2));
        
        // Verify service was called
        verify(featureService, times(1)).copy(eq(featureId));
    }
}