package com.trs.police.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.converter.DataSourceConverter;
import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.DataSourceType;
import com.trs.police.service.datasource.DataSourceService;
import com.trs.police.vo.DataSourceVO;
import com.trs.police.vo.datasource.DataSourceGroupVO;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.builder.base.RestfulResultsV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Test class for DataSourceController
 */
public class DataSourceControllerTest {

    @Mock
    private DataSourceService dataSourceService;

    @InjectMocks
    private DataSourceController dataSourceController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(dataSourceController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testCreateDataSource() throws Exception {
        // Prepare test data
        DataSourceDTO dto = new DataSourceDTO();
        dto.setName("Test Data Source");
        dto.setType(DataSourceType.MYSQL);
        
        DataSourceVO dataSourceVO = new DataSourceVO();
        dataSourceVO.setId(1L);
        dataSourceVO.setName("Test Data Source");
        dataSourceVO.setType(DataSourceType.MYSQL);
        
        RestfulResults expectedResult = RestfulResults.ok(dataSourceVO);

        // Mock service behavior
        when(dataSourceService.saveDataSource(any(DataSourceDTO.class))).thenReturn(expectedResult);

        // Perform request and verify
        mockMvc.perform(post("/api/datasource/createDataSource")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"));

        // Verify service was called
        verify(dataSourceService, times(1)).saveDataSource(any(DataSourceDTO.class));
    }

    @Test
    public void testUpdateDataSource() throws Exception {
        // Prepare test data
        DataSourceDTO dto = new DataSourceDTO();
        dto.setId(1L);
        dto.setName("Updated Data Source");
        dto.setType(1);

        DataSourceVO expectedVO = new DataSourceVO();
        expectedVO.setId(1L);
        expectedVO.setName("Updated Data Source");
        expectedVO.setType(1);

        // Mock service behavior
        when(dataSourceService.updateDataSource(any(DataSourceDTO.class))).thenReturn(expectedVO);

        // Perform request and verify
        mockMvc.perform(post("/api/datasource/updateDataSource")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("Updated Data Source"))
                .andExpect(jsonPath("$.type").value(1));

        // Verify service was called
        verify(dataSourceService, times(1)).updateDataSource(any(DataSourceDTO.class));
    }

    @Test
    public void testGetDataSource() throws Exception {
        // Prepare test data
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setName("Test Data Source");
        dataSource.setType(1);

        DataSourceVO expectedVO = new DataSourceVO();
        expectedVO.setId(dataSourceId);
        expectedVO.setName("Test Data Source");
        expectedVO.setType(1);

        // Mock service and converter behavior
        when(dataSourceService.getDataSourceById(dataSourceId)).thenReturn(dataSource);
        mockStatic(DataSourceConverter.class);
        when(DataSourceConverter.toVO(dataSource)).thenReturn(expectedVO);

        // Perform request and verify
        mockMvc.perform(get("/api/datasource/{id}", dataSourceId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("Test Data Source"))
                .andExpect(jsonPath("$.type").value(1));

        // Verify service was called
        verify(dataSourceService, times(1)).getDataSourceById(dataSourceId);
    }

    @Test
    public void testGetDataSourceNotFound() throws Exception {
        // Prepare test data
        Long dataSourceId = 999L;

        // Mock service behavior
        when(dataSourceService.getDataSourceById(dataSourceId)).thenReturn(null);

        // Perform request and verify
        mockMvc.perform(get("/api/datasource/{id}", dataSourceId))
                .andExpect(status().isBadRequest());

        // Verify service was called
        verify(dataSourceService, times(1)).getDataSourceById(dataSourceId);
    }

    @Test
    public void testListMpDataSources() throws Exception {
        // Prepare test data
        DataSourceDTO dto = new DataSourceDTO();
        dto.setName("Test");

        List<DataSourceGroupVO> dataSourceGroups = new ArrayList<>();
        DataSourceGroupVO group = new DataSourceGroupVO();
        group.setType(1);
        group.setTypeName("MySQL");
        dataSourceGroups.add(group);

        RestfulResultsV2<DataSourceGroupVO> expectedResult = new RestfulResultsV2<>();
        expectedResult.setCode(200);
        expectedResult.setMessage("Success");
        expectedResult.setData(dataSourceGroups);

        // Mock service behavior
        when(dataSourceService.listMpDataSources(any(DataSourceDTO.class))).thenReturn(expectedResult);

        // Perform request and verify
        mockMvc.perform(get("/api/datasource/mpPage")
                .param("name", "Test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data[0].type").value(1))
                .andExpect(jsonPath("$.data[0].typeName").value("MySQL"));

        // Verify service was called
        verify(dataSourceService, times(1)).listMpDataSources(any(DataSourceDTO.class));
    }

    @Test
    public void testListDataSources() throws Exception {
        // Prepare test data
        DataSourceDTO dto = new DataSourceDTO();
        dto.setName("Test");

        List<DataSourceVO> dataSources = new ArrayList<>();
        DataSourceVO dataSource = new DataSourceVO();
        dataSource.setId(1L);
        dataSource.setName("Test Data Source");
        dataSource.setType(1);
        dataSources.add(dataSource);

        RestfulResultsV2<DataSourceVO> expectedResult = new RestfulResultsV2<>();
        expectedResult.setCode(200);
        expectedResult.setMessage("Success");
        expectedResult.setData(dataSources);

        // Mock service behavior
        when(dataSourceService.findDataSources(any(DataSourceDTO.class))).thenReturn(expectedResult);

        // Perform request and verify
        mockMvc.perform(get("/api/datasource/page")
                .param("name", "Test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("Test Data Source"))
                .andExpect(jsonPath("$.data[0].type").value(1));

        // Verify service was called
        verify(dataSourceService, times(1)).findDataSources(any(DataSourceDTO.class));
    }

    @Test
    public void testChangeDataSourceStatus() throws Exception {
        // Prepare test data
        Long dataSourceId = 1L;
        Integer status = 1;

        // Mock service behavior
        doNothing().when(dataSourceService).changeDataSourceStatus(dataSourceId, status);

        // Perform request and verify
        mockMvc.perform(get("/api/datasource/changeDataSourceStatus")
                .param("id", dataSourceId.toString())
                .param("status", status.toString()))
                .andExpect(status().isOk());

        // Verify service was called
        verify(dataSourceService, times(1)).changeDataSourceStatus(dataSourceId, status);
    }

    @Test
    public void testDeleteDataSource() throws Exception {
        // Prepare test data
        Long dataSourceId = 1L;

        // Mock service behavior
        doNothing().when(dataSourceService).deleteDataSource(dataSourceId);

        // Perform request and verify
        mockMvc.perform(delete("/api/datasource/{id}", dataSourceId))
                .andExpect(status().isOk());

        // Verify service was called
        verify(dataSourceService, times(1)).deleteDataSource(dataSourceId);
    }

    @Test
    public void testCheckConnection() throws Exception {
        // Prepare test data
        DataSourceDTO dto = new DataSourceDTO();
        dto.setName("Test Data Source");
        dto.setType(1);

        // Mock service behavior
        when(dataSourceService.checkConnection(any(DataSourceDTO.class))).thenReturn(true);

        // Perform request and verify
        mockMvc.perform(post("/api/datasource/check-connection")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));

        // Verify service was called
        verify(dataSourceService, times(1)).checkConnection(any(DataSourceDTO.class));
    }

    @Test
    public void testRefreshDataSource() throws Exception {
        // Prepare test data
        Long dataSourceId = 1L;

        // Mock service behavior
        doNothing().when(dataSourceService).refreshDataSource(dataSourceId);

        // Perform request and verify
        mockMvc.perform(get("/api/datasource/refreshDataSource")
                .param("dataSourceId", dataSourceId.toString()))
                .andExpect(status().isOk());

        // Verify service was called
        verify(dataSourceService, times(1)).refreshDataSource(dataSourceId);
    }
}