package com.trs.police.controller;

import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.vo.DataTableFieldVO;
import com.trs.web.builder.base.RestfulResultsV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Test class for DataTableFieldController
 */
public class DataTableFieldControllerTest {

    @Mock
    private FieldsService fieldsService;

    @InjectMocks
    private DataTableFieldController dataTableFieldController;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(dataTableFieldController).build();
    }

    @Test
    public void testGetFieldInfo() throws Exception {
        // Prepare test data
        Integer tableId = 1;
        Integer selectedStatus = 1;
        
        List<DataTableFieldVO> fieldList = new ArrayList<>();
        DataTableFieldVO fieldVO = new DataTableFieldVO();
        fieldVO.setId(1L);
        fieldVO.setFieldName("test_field");
        fieldVO.setFieldNameCn("测试字段");
        fieldVO.setFieldType("string");
        fieldVO.setFieldTypeName("字符串");
        fieldList.add(fieldVO);
        
        // Mock service behavior
        when(fieldsService.getFieldInfo(eq(tableId), eq(selectedStatus))).thenReturn(fieldList);
        
        // Perform request and verify
        mockMvc.perform(get("/api/datatableField/getFieldInfo")
                .param("tableId", tableId.toString())
                .param("selectedStatus", selectedStatus.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].fieldName").value("test_field"))
                .andExpect(jsonPath("$.data[0].fieldNameCn").value("测试字段"))
                .andExpect(jsonPath("$.data[0].fieldType").value("string"))
                .andExpect(jsonPath("$.data[0].fieldTypeName").value("字符串"));
        
        // Verify service was called
        verify(fieldsService, times(1)).getFieldInfo(eq(tableId), eq(selectedStatus));
    }
    
    @Test
    public void testGetFieldInfoWithoutSelectedStatus() throws Exception {
        // Prepare test data
        Integer tableId = 1;
        
        List<DataTableFieldVO> fieldList = new ArrayList<>();
        DataTableFieldVO fieldVO = new DataTableFieldVO();
        fieldVO.setId(1L);
        fieldVO.setFieldName("test_field");
        fieldVO.setFieldNameCn("测试字段");
        fieldVO.setFieldType("string");
        fieldVO.setFieldTypeName("字符串");
        fieldList.add(fieldVO);
        
        // Mock service behavior
        when(fieldsService.getFieldInfo(eq(tableId), eq(null))).thenReturn(fieldList);
        
        // Perform request and verify
        mockMvc.perform(get("/api/datatableField/getFieldInfo")
                .param("tableId", tableId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].fieldName").value("test_field"))
                .andExpect(jsonPath("$.data[0].fieldNameCn").value("测试字段"))
                .andExpect(jsonPath("$.data[0].fieldType").value("string"))
                .andExpect(jsonPath("$.data[0].fieldTypeName").value("字符串"));
        
        // Verify service was called
        verify(fieldsService, times(1)).getFieldInfo(eq(tableId), eq(null));
    }
}