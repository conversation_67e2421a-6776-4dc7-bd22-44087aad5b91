package com.trs.police.controller;

import com.trs.police.dto.dict.DictDto;
import com.trs.police.service.dict.DictService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Test class for DictController
 */
public class DictControllerTest {

    @Mock
    private DictService dictService;

    @InjectMocks
    private DictController dictController;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(dictController).build();
    }

    @Test
    public void testGetDictTree() throws Exception {
        // Prepare test data
        String type = "test_type";
        
        List<DictDto> dictList = new ArrayList<>();
        DictDto dictDto = new DictDto();
        dictDto.setId(1L);
        dictDto.setCode(1001L);
        dictDto.setName("测试字典");
        dictDto.setType(type);
        dictList.add(dictDto);
        
        // Mock service behavior
        when(dictService.getDictListByType(eq(type))).thenReturn(dictList);
        
        // Perform request and verify
        mockMvc.perform(get("/api/global/dict")
                .param("type", type))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].code").value(1001))
                .andExpect(jsonPath("$[0].name").value("测试字典"))
                .andExpect(jsonPath("$[0].type").value(type));
        
        // Verify service was called
        verify(dictService, times(1)).getDictListByType(eq(type));
    }
    
    @Test
    public void testGetDictTreePublicEndpoint() throws Exception {
        // Prepare test data
        String type = "test_type";
        
        List<DictDto> dictList = new ArrayList<>();
        DictDto dictDto = new DictDto();
        dictDto.setId(1L);
        dictDto.setCode(1001L);
        dictDto.setName("测试字典");
        dictDto.setType(type);
        dictList.add(dictDto);
        
        // Mock service behavior
        when(dictService.getDictListByType(eq(type))).thenReturn(dictList);
        
        // Perform request and verify
        mockMvc.perform(get("/api/global/public/dict")
                .param("type", type))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].code").value(1001))
                .andExpect(jsonPath("$[0].name").value("测试字典"))
                .andExpect(jsonPath("$[0].type").value(type));
        
        // Verify service was called
        verify(dictService, times(1)).getDictListByType(eq(type));
    }
}