package com.trs.police.service.fieldsService.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.constant.DbConstant;
import com.trs.police.entity.dataField.DataField;
import com.trs.police.mapper.DataFieldMapper;
import com.trs.police.vo.DataTableFieldVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test class for FieldsServiceImpl
 */
public class FieldsServiceImplTest {

    @Mock
    private DataFieldMapper dataFieldMapper;

    @InjectMocks
    private FieldsServiceImpl fieldsService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetFieldInfoWithSelectedStatus() {
        // Prepare test data
        Integer tableId = 1;
        Integer selectedStatus = 1;
        
        List<DataField> dataFields = new ArrayList<>();
        DataField dataField = new DataField();
        dataField.setId(1L);
        dataField.setFieldName("test_field");
        dataField.setFieldNameCn("测试字段");
        dataField.setFieldType("varchar");
        dataFields.add(dataField);
        
        // Mock behavior
        when(dataFieldMapper.selectList(any(QueryWrapper.class))).thenReturn(dataFields);
        
        // Execute
        List<DataTableFieldVO> result = fieldsService.getFieldInfo(tableId, selectedStatus);
        
        // Verify
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals("test_field", result.get(0).getFieldName());
        assertEquals("测试字段", result.get(0).getFieldNameCn());
        assertEquals("varchar", result.get(0).getFieldType());
        
        // Verify the query was built correctly
        verify(dataFieldMapper, times(1)).selectList(any(QueryWrapper.class));
    }
    
    @Test
    public void testGetFieldInfoWithoutSelectedStatus() {
        // Prepare test data
        Integer tableId = 1;
        
        List<DataField> dataFields = new ArrayList<>();
        DataField dataField = new DataField();
        dataField.setId(1L);
        dataField.setFieldName("test_field");
        dataField.setFieldNameCn("测试字段");
        dataField.setFieldType("varchar");
        dataFields.add(dataField);
        
        // Mock behavior
        when(dataFieldMapper.selectList(any(QueryWrapper.class))).thenReturn(dataFields);
        
        // Execute
        List<DataTableFieldVO> result = fieldsService.getFieldInfo(tableId);
        
        // Verify
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test_field", result.get(0).getFieldName());
        assertEquals("测试字段", result.get(0).getFieldNameCn());
        assertEquals("varchar", result.get(0).getFieldType());
        
        // Verify the query was built correctly
        verify(dataFieldMapper, times(1)).selectList(any(QueryWrapper.class));
    }
    
    @Test
    public void testGetFieldInfoWithEmptyResult() {
        // Prepare test data
        Integer tableId = 1;
        Integer selectedStatus = 1;
        
        // Mock behavior
        when(dataFieldMapper.selectList(any(QueryWrapper.class))).thenReturn(new ArrayList<>());
        
        // Execute
        List<DataTableFieldVO> result = fieldsService.getFieldInfo(tableId, selectedStatus);
        
        // Verify
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // Verify the query was built correctly
        verify(dataFieldMapper, times(1)).selectList(any(QueryWrapper.class));
    }
    
    @Test
    public void testGetFieldInfoWithNullFieldNameCn() {
        // Prepare test data
        Integer tableId = 1;
        
        List<DataField> dataFields = new ArrayList<>();
        DataField dataField = new DataField();
        dataField.setId(1L);
        dataField.setFieldName("test_field");
        dataField.setFieldNameCn(null);
        dataField.setFieldType("varchar");
        dataFields.add(dataField);
        
        // Mock behavior
        when(dataFieldMapper.selectList(any(QueryWrapper.class))).thenReturn(dataFields);
        
        // Execute
        List<DataTableFieldVO> result = fieldsService.getFieldInfo(tableId);
        
        // Verify
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test_field", result.get(0).getFieldName());
        assertEquals("test_field", result.get(0).getFieldNameCn()); // Should use field name when field name CN is null
        assertEquals("varchar", result.get(0).getFieldType());
        
        // Verify the query was built correctly
        verify(dataFieldMapper, times(1)).selectList(any(QueryWrapper.class));
    }
}