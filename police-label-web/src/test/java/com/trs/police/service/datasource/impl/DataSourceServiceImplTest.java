package com.trs.police.service.datasource.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.converter.DataSourceConverter;
import com.trs.police.dto.DataSourceDTO;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.DataSourceType;
import com.trs.police.mapper.DataSourceMapper;
import com.trs.police.service.baseService.CommonService;
import com.trs.police.vo.DataSourceVO;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.builder.base.RestfulResultsV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test class for DataSourceServiceImpl
 */
public class DataSourceServiceImplTest {

    @Mock
    private DataSourceMapper dataSourceMapper;

    @Mock
    private CommonService commonService;

    @Mock
    private Executor dataSourceTaskExecutor;

    @InjectMocks
    private DataSourceServiceImpl dataSourceService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(dataSourceService, "dataSourceTaskExecutor", dataSourceTaskExecutor);
    }

    @Test
    public void testGetDataSourceById() {
        // Prepare test data
        Long dataSourceId = 1L;
        
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setName("Test Data Source");
        dataSource.setType(DataSourceType.MYSQL);
        
        // Mock behavior
        when(dataSourceMapper.selectById(dataSourceId)).thenReturn(dataSource);
        
        // Execute
        DataSource result = dataSourceService.getDataSourceById(dataSourceId);
        
        // Verify
        assertNotNull(result);
        assertEquals(dataSourceId, result.getId());
        assertEquals("Test Data Source", result.getName());
        assertEquals(DataSourceType.MYSQL, result.getType());
    }

    @Test
    public void testDeleteDataSource() {
        // Prepare test data
        Long dataSourceId = 1L;
        
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setName("Test Data Source");
        dataSource.setType(DataSourceType.MYSQL);
        dataSource.setFeatureCount(0);
        dataSource.setLabelCount(0);
        dataSource.setModelCount(0);
        
        // Mock behavior
        when(dataSourceMapper.selectById(dataSourceId)).thenReturn(dataSource);
        when(dataSourceMapper.deleteById(dataSourceId)).thenReturn(1);
        
        // Execute
        dataSourceService.deleteDataSource(dataSourceId);
        
        // Verify
        verify(dataSourceMapper, times(1)).deleteById(dataSourceId);
    }

    @Test
    public void testChangeDataSourceStatus() {
        // Prepare test data
        Long dataSourceId = 1L;
        Integer status = 1;
        
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setName("Test Data Source");
        dataSource.setType(DataSourceType.MYSQL);
        dataSource.setCreateStatus(0);
        
        // Mock behavior
        when(dataSourceMapper.selectById(dataSourceId)).thenReturn(dataSource);
        
        // Execute
        dataSourceService.changeDataSourceStatus(dataSourceId, status);
        
        // Verify
        verify(dataSourceMapper, times(1)).updateById(any(DataSource.class));
    }
}