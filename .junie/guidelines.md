# Police Label Project Development Guidelines

This document provides essential information for developers working on the Police Label project.

## Build/Configuration Instructions

### Prerequisites
- Java 11
- Maven 3.6+
- <PERSON>cos (for configuration management)

### Building the Project
The project uses <PERSON><PERSON> for build management. To build the project:

```bash
# Build the entire project
mvn clean install

# Skip tests
mvn clean install -DskipTests

# Build a specific module
mvn clean install -pl police-label-core
mvn clean install -pl police-label-web
```

### Configuration
The project uses <PERSON>cos for configuration management. Different profiles are available:

- **dev-local**: Default profile for local development (http://localhost:8848)
- **test-local**: Local testing profile
- **develop**: Development environment
- **test**: Test environment
- **ys-local**: YS local environment

To specify a profile when running:

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev-local
```

### Docker Build
The project includes Docker build configuration. To build a Docker image:

```bash
mvn clean package dockerfile:build
```

## Testing Information

### Testing Framework
The project uses JUnit 5 (Jupiter) for testing. There are two types of tests:

1. **Unit Tests**: Simple tests for utility classes and other components without Spring dependencies
2. **Spring Boot Tests**: Tests that require the Spring context

### Running Tests

```bash
# Run all tests
mvn test

# Run tests in a specific module
mvn test -pl police-label-core
mvn test -pl police-label-web

# Run a specific test class
mvn test -Dtest=StringUtilTest

# Run a specific test method
mvn test -Dtest=StringUtilTest#testIsEmpty
```

### Creating New Tests

#### Unit Tests
For simple unit tests (without Spring dependencies):

1. Create a test class in the corresponding module's test directory
2. Use JUnit 5 annotations (@Test, etc.)
3. Use assertions to verify expected behavior

Example:

```java
package com.trs.police.common.core.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class StringUtilTest {
    @Test
    public void testIsEmpty() {
        assertTrue(StringUtil.isEmpty(null));
        assertTrue(StringUtil.isEmpty(""));
        assertTrue(StringUtil.isEmpty("   "));
        assertFalse(StringUtil.isEmpty("test"));
    }
}
```

#### Spring Boot Tests
For tests that require the Spring context:

1. Create a test class in the corresponding module's test directory
2. Add the @SpringBootTest annotation
3. Autowire the components you want to test

Example:

```java
package com.trs.police;

import com.trs.police.service.fieldsService.FieldsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class FieldTest {
    @Autowired
    private FieldsService fieldsService;

    @Test
    public void test() {
        // Test code here
    }
}
```

## Additional Development Information

### Code Style
The project uses checkstyle for code style enforcement. The configuration is in `codestyle/checkstyle.xml`. Code that doesn't comply with the style guidelines will fail the build.

### Project Structure
- **police-label-core**: Core module with common utilities, configurations, and base classes
- **police-label-web**: Web module with controllers, services, and web-specific components

### Important Utilities
- **StringUtil**: Utility for string operations
- **GeoUtils**: Utilities for geographic operations
- **DateUtil/TimeUtil**: Utilities for date and time operations
- **JsonUtil**: Utilities for JSON operations

### Database Access
The project uses MyBatis Plus for database access. Custom type handlers are available for special data types.

### Logging
The project uses SLF4J with Lombok's @Slf4j annotation for logging.

### Error Handling
The project includes a CustomExceptionHandler for global exception handling.

### MapStruct
The project uses MapStruct for object mapping between DTOs, entities, and view objects.