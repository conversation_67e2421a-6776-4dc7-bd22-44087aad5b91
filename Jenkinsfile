import java.util.stream.Collectors

def models = new HashSet<String>();


// 哪些应用中用到Apache CFX作为webservice的客户端
def projectUseCFX = new HashSet<String>()
projectUseCFX.add("police-label-web")


// 受到变更影响的模块
def changedModels = new HashSet()

def project_version = "v0.0.1-SNAPSHOT"

// 构建的分支名
def branchName = params.getOrDefault('branchName', 'develop')
echo "参数[branchName]=" + branchName
def inputBranchName = params.getOrDefault('inputBranchName', '')
echo "参数[inputBranchName]=" + inputBranchName
if(!inputBranchName.isEmpty()) {
    echo "手动输入了构建的分支: " + inputBranchName
    branchName = inputBranchName
}
def useTag = false
def tag_name = params.getOrDefault('tag_name', 'notExist')
echo "参数[tag_name]=" + tag_name
// 屏蔽默认值
if(!tag_name.isEmpty() && !"notExist".equals(tag_name)) {
    echo "输入了构建的tag_name: " + tag_name
    useTag = true
}
// 是否是人工触发构建
def manualOperationFlag = params.getOrDefault('manualOperationFlag', false)
echo "参数[manualOperationFlag]=" + manualOperationFlag
// 是否跳过部署
def skipDeploy = params.getOrDefault('skipDeploy', false)
echo "参数[skipDeploy]=" + skipDeploy
// 默认构建的模块
def buildModels = params.getOrDefault('buildModels', '*')
echo "参数[buildModels]=" + buildModels
// 默认构建的模块
def inputBuildModels = params.getOrDefault('inputBuildModels', '')
echo "参数[inputBuildModels]=" + inputBuildModels
if(!inputBuildModels.isEmpty()) {
    echo "手动输入了构建的模块列表: " + inputBuildModels
    buildModels = inputBuildModels
}
def apolloNamespace
if(useTag){
    apolloNamespace = tag_name.contains('test') ? "test" : "develop"
} else {
    apolloNamespace = params.getOrDefault('apolloNamespace', 'develop')
}
echo "参数[apolloNamespace]=" + apolloNamespace
pipeline {
    agent any

    tools {
        maven 'maven-3.8.1'
        jdk 'openjdk-11'
    }

    stages {
        stage('checkout') {
            steps {
                script {
                    if(!useTag) {
                        checkout scmGit(branches: [[name: branchName]], extensions: [], userRemoteConfigs: [[credentialsId: 'gitlab', url: 'http://gitlab.devdemo.trs.net.cn/ga/police-label.git']])
                    } else {
                        checkout scmGit(branches: [[name: tag_name]], extensions: [], userRemoteConfigs: [[credentialsId: 'gitlab', url: 'http://gitlab.devdemo.trs.net.cn/ga/police-label.git']])
                    }
                    def git_commit_time = sh(returnStdout: true, script: 'git show -s --format=%cd --date=format-local:%Y%m%d.%H%M').trim()
                    echo "git commit对应的时间为：" + git_commit_time
                }
            }
        }

//         stage('SonarQube Analysis') {
//             steps {
//                withSonarQubeEnv("13-sonar") {
//                  sh 'mvn clean verify sonar:sonar -Dmaven.test.skip=true -Dsonar.projectKey=ga_police-cloud_AYQcQzBfp3gHi03Gga_q'
//                 }
//             }
//         }

        stage('check changed models') {
            steps {
                script {
                    if(manualOperationFlag) {
                        echo "这个是人工触发，对应的buildModels为" + buildModels
                        if(!'*'.equals(buildModels)){
                            changedModels.addAll(buildModels.split(','))
                        }
                    } else {
                        def changedFiles = getChangeFilePathSet()
                        echo "与上次成功构建比较,此次构建项目存在变更的文件集合为: ${changedFiles}"

                        // def paths = changedFiles.findAll { it.endsWith(".java") || it.endsWith(".yaml") || it.endsWith(".yml") || it.endsWith(".xml") || it.endsWith(".sql") || it == "Dockerfile" }
                        // 理论上以police路径开头下面的文件发生了变化
                        // 有必要判断影响某些模块，其他路径下的文件有了变化应该是触发全量
                        def paths = changedFiles.findAll { it.startsWith("police") }
                        echo "与上次成功构建比较,此次构建项目存在变更且影响代码构建的的文件集合为: ${paths}"

                        for (def path in paths) {
                            // 普通模块变动只构建该模块
                            changedModels.addAll(models.findAll { path.contains(it) })

                            // 通用模块变动就构建全部模块
                            if (path.contains('police-label-core/')) {
                                changedModels.addAll(models)
                            }
                        }
                    }

                    // 如果没有检测到变动就构建全部
                    if (changedModels.isEmpty()) {
                        echo '检测到变更模块为空,可能是由于多次执行构建导致,将编译所有模块'
                        changedModels.addAll(models)
                    }
                   //changedModels.remove("data-storm")

                    echo "最终需要重新编译的模块有: ${changedModels}"
                }
            }
        }

        stage('build') {
            steps {
                script {
                    if (!changedModels.isEmpty()) {
                        sh "mvn -pl ${changedModels.collect { (it == "police-label-web" || it == "police-data-storm" || it == "police-projects") ? it : ("police-apps/" + it) }.join(",")} -am  clean package -Dmaven.test.skip=true -T 8"
                    }
                }
            }
        }

        stage('build images') {
            steps {
                script {
                    def remote = [:]
                    remote.name = 'jenkins'
                    remote.host = '************'
                    remote.port = 1022
                    remote.user = 'root'
                    remote.password = 'Dyc3r@trs'
                    remote.allowAnyHosts = true

//                     def path = "${WORKSPACE}".replace("/var/", "/TRS/")
                    def path = "${WORKSPACE}"

                    stage("Remote ssh") {
                        def git_branch = apolloNamespace
                        def git_tag = useTag ? "-" + tag_name : ""
                        def git_abbrev = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()[0..6]
                        def git_commit_time = sh(returnStdout: true, script: 'git show -s --format=%cd --date=format-local:%Y%m%d.%H%M').trim()
                        sh '''
builderName=mybuilder
if test `docker buildx ls | grep $builderName | wc -l` -gt 0
then
    # docker buildx rm $builderName
    docker buildx use --default $builderName
    echo "切换到$builderName"
else
    echo "不存在${builderName}，开始创建$builderName"
    # 创建builder
    docker buildx create --use --name=$builderName --driver docker-container --driver-opt image=harbor.trscd.com.cn/baseapp/buildkit:master
fi
                        '''
                        // 插件文档 https://plugins.jenkins.io/ssh-steps/
                        changedModels.collect {
                            def app_name = "${it}"
                            def image_tag = "harbor.trscd.com.cn/trs-police-yunshao/${app_name}-${git_branch}:${project_version}${git_tag}-${git_abbrev}-${git_commit_time}"
                            def subPath = (it == "police-label-web" || it == "police-data-storm" || it == "police-projects") ? it : ("police-apps/" + it)
//                             sshCommand remote: remote, command: "docker buildx build --platform linux/amd64,linux/arm64/v8 --build-arg JAR_FILE=${it}-${project_version.replaceFirst('v','')}.jar --push -t ${image_tag} ${path}/${subPath}"
                            if(projectUseCFX.contains(it)) {
                                echo "webservice客户端的工程需要解压后执行"
                                sh "rm -rf ${path}/${subPath}/target/opt"
                                sh "mkdir -p ${path}/${subPath}/target/opt"
                                sh "cd ${path}/${subPath}/target/opt;jar -xvf ../${it}-${project_version.replaceFirst('v','')}.jar"
                            }
                            sh "docker buildx build --platform linux/amd64,linux/arm64/v8 --build-arg JAR_FILE=${it}-${project_version.replaceFirst('v','')}.jar --push -t ${image_tag} ${path}/${subPath}"
                        }
                    }
                }
            }
        }

        stage('deploy') {
            steps {
                script {
                    def git_branch = apolloNamespace
                    def git_abbrev = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()[0..6]
                    def git_commit_time = sh(returnStdout: true, script: 'git show -s --format=%cd --date=format-local:%Y%m%d.%H%M').trim()
                    if(skipDeploy) {
                         echo "只是构建镜像，不进行部署"
                    }
                    // 依次重启服务
                    for (def changedModel in changedModels) {
                        def namespace = git_branch == "develop" ? "trs-police-yunshao" : "trs-police-yunshao-${git_branch}"
                        def app_name = git_branch == "develop" ? "${changedModel}" : "${changedModel}-${git_branch}"
                        def git_tag = useTag ? "-" + tag_name : ""
                        def image_tag = "harbor.trscd.com.cn/trs-police-yunshao/${changedModel}-${git_branch}:${project_version}${git_tag}-${git_abbrev}-${git_commit_time}"
                        if(!skipDeploy) {
                            sh "curl --location --request POST 'http://************:90/devops/openapi/app/v1/deploy?namespace=${namespace}&appName=${app_name}&image=${image_tag}' --header 'Authorization: Basic MTM4ODg4ODg4ODg6dHJzYWRtaW4xMjM='"
                        }
                        echo "镜像为:${image_tag}"
                    }
                }
            }
        }
    }
}

//获取变更文件列表，返回HashSet，注意添加的影响文件路径不含仓库目录名
@NonCPS
def getChangeFilePathSet() {
    def changedFiles = new HashSet<String>();
    for (int i = 0; i < currentBuild.changeSets.size(); i++) {
        def entries = currentBuild.changeSets[i].items
        for (int j = 0; j < entries.length; j++) {
            def entry = entries[j]
            changedFiles.addAll(entry.getAffectedPaths());
        }
    }
    return changedFiles;
}
