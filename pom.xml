<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.trs</groupId>
    <artifactId>police-label</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>police-label</name>
    <packaging>pom</packaging>
    <description>police标签微服务工程</description>

    <modules>
        <module>police-label-core</module>
        <module>police-label-web</module>
        <module>police-label-engine</module>
    </modules>

    <properties>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot-admin.version>2.5.4</spring-boot-admin.version>
        <spring-cloud.version>2020.0.5</spring-cloud.version>
        <spring-boot.version>2.5.8</spring-boot.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
        <maven-checkstyle-plugin-version>3.1.2</maven-checkstyle-plugin-version>
        <dockerfile-maven-plugin.version>1.4.13</dockerfile-maven-plugin.version>
        <git-commit-id-plugin.version>4.9.10</git-commit-id-plugin.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <mysql-connector.version>8.0.28</mysql-connector.version>
        <druid-spring-boot-starter.version>1.2.8</druid-spring-boot-starter.version>
        <elasticsearch.version>7.17.4</elasticsearch.version>
        <media-base-web-version>1.4.29</media-base-web-version>
        <media-base-version>1.4.35</media-base-version>
        <db.sdk.version>3.2.35</db.sdk.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <lombok.version>1.18.22</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>8.5.3</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-dependencies</artifactId>
                <version>${spring-boot-admin.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.trs.web</groupId>
                <artifactId>media_base_web_builder</artifactId>
                <version>${media-base-web-version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>db-sdk-core</artifactId>
                <version>${db.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>db-sdk-bean</artifactId>
                <version>${db.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>db-sql-core</artifactId>
                <version>${db.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.es</groupId>
                <artifactId>esbean</artifactId>
                <version>ES7.4.37</version>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>trs-oracle-sdk</artifactId>
                <version>${db.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>db-sdk-core</groupId>
                        <artifactId>com.trs.db</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>trs-mongodb-sdk</artifactId>
                <version>${db.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>db-sdk-core</groupId>
                        <artifactId>com.trs.db</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>trs-postgresql-sdk</artifactId>
                <version>${db.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>db-sdk-core</groupId>
                        <artifactId>com.trs.db</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>trs-clickhouse-sdk</artifactId>
                <version>${db.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>db-sdk-core</artifactId>
                        <groupId>com.trs.db</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>trs-es-sdk</artifactId>
                <version>${db.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>db-sdk-core</artifactId>
                        <groupId>com.trs.db</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.trs.db</groupId>
                <artifactId>trs-hive-sdk</artifactId>
                <version>${db.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs</groupId>
                <artifactId>media_base</artifactId>
                <version>${media-base-version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.midend</groupId>
                <artifactId>trs-sdk</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>1.12.2</version>
            </dependency>
            <dependency>
                <groupId>io.github.draco1023</groupId>
                <artifactId>poi-tl-ext</artifactId>
                <version>0.4.17-poi5</version>
            </dependency>
            <!--webservice 依赖-->
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
                <version>3.5.5</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>3.0.4</version>
            </dependency>
            <!--postgres-->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.6.0</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.ws</groupId>
                <artifactId>jaxws-ri</artifactId>
                <version>2.3.6</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.5.3</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.5.3</version>
            </dependency>
            <dependency>
                <groupId>io.github.godfather1103</groupId>
                <artifactId>common-gb-checker-and-parser</artifactId>
                <version>1.0.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.trs</groupId>
                <artifactId>trs-ai-api-spring-boot-starter</artifactId>
                <version>2.7</version>
            </dependency>
            <dependency>
                <groupId>com.trs</groupId>
                <artifactId>media_base_log</artifactId>
                <version>1.9.3</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.9.0</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>TRS</id>
            <name>middle end repository</name>
            <url>http://mvn.devdemo.trs.net.cn/repository/maven-releases/</url>
        </repository>
        <repository>
            <id>TRS-Repo</id>
            <url>https://nexus.trscd.com.cn/repository/public/</url>
        </repository>
        <repository>
            <id>aliyun</id>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
        <repository>
            <id>huaweicloudsdk</id>
            <url>https://repo.huaweicloud.com/repository/maven/huaweicloudsdk/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>aliyun</id>
            <url>https://maven.aliyun.com/repository/public</url>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <configuration>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                            <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                            <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                            <nonFilteredFileExtension>asc</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <executable>true</executable>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!--用于构建时获取git参数-->
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>${git-commit-id-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>get-the-git-infos</id>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                            <phase>initialize</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <dateFormat>yyyyMMdd.HHmm</dateFormat>
                        <dotGitDirectory>${project.parent.parent.basedir}/.git</dotGitDirectory>
                        <generateGitPropertiesFile>true</generateGitPropertiesFile>
                        <generateGitPropertiesFilename>${project.basedir}/src/main/resources/git.properties
                        </generateGitPropertiesFilename>
                        <includeOnlyProperties>
                            <includeOnlyProperty>^git.branch$</includeOnlyProperty>
                            <includeOnlyProperty>^git.build.+$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.id.abbrev$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.id$</includeOnlyProperty>
                        </includeOnlyProperties>
                        <skipPoms>true</skipPoms>
                        <replacementProperties>
                            <replacementProperty>
                                <property>git.branch</property>
                                <regex>false</regex>
                                <forceValueEvaluation>false</forceValueEvaluation>
                                <token>origin/</token>
                                <value/>
                            </replacementProperty>
                        </replacementProperties>
                    </configuration>
                </plugin>
                <!--用于构建docker镜像-->
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${dockerfile-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>default</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <repository>harbor.trscd.com.cn/trs-police-yunshao/${project.artifactId}-${git.branch}
                        </repository>
                        <tag>v${project.version}-${git.commit.id.abbrev}-${git.commit.time}</tag>
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- 注入Git信息 -->
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
            <!--保证编译使用的jdk版本以及编码-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!--检查代码编码规范,不符合规范将无法打包-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin-version}</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>8.36.2</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>checkstyle-validation</id>
                        <phase>compile</phase>
                        <configuration>
                            <configLocation>codestyle/checkstyle.xml</configLocation>
                            <encoding>UTF-8</encoding>
                            <consoleOutput>true</consoleOutput>
                            <failOnViolation>true</failOnViolation>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <sourceDirectories>
                        <sourceDirectory>${project.build.sourceDirectory}</sourceDirectory>
                    </sourceDirectories>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>profiles/**</exclude>
                    <exclude>template/**</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>template/**</include>
                </includes>
                <excludes>
                    <exclude>profiles/**</exclude>
                </excludes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>dev-local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!-- nacos 地址与账号信息 -->
                <spring.cloud.nacos.server-addr>http://localhost:8848</spring.cloud.nacos.server-addr>
                <spring.cloud.nacos.namespace>police</spring.cloud.nacos.namespace>
                <spring.cloud.nacos.username>nacos</spring.cloud.nacos.username>
                <spring.cloud.nacos.password>nacos</spring.cloud.nacos.password>
            </properties>
        </profile>
        <profile>
            <id>test-local</id>
            <properties>
                <!-- nacos 地址与账号信息 -->
                <spring.cloud.nacos.server-addr>http://localhost:8848</spring.cloud.nacos.server-addr>
                <spring.cloud.nacos.namespace>ys-test</spring.cloud.nacos.namespace>
                <spring.cloud.nacos.username>nacos</spring.cloud.nacos.username>
                <spring.cloud.nacos.password>nacos</spring.cloud.nacos.password>
            </properties>
        </profile>
        <profile>
            <id>develop</id>
            <properties>
                <!-- nacos 地址与账号信息 -->
                <spring.cloud.nacos.server-addr>http://nacos.develop.ys.trs:8888</spring.cloud.nacos.server-addr>
                <spring.cloud.nacos.namespace>police</spring.cloud.nacos.namespace>
                <spring.cloud.nacos.username>nacos</spring.cloud.nacos.username>
                <spring.cloud.nacos.password>nacos</spring.cloud.nacos.password>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!-- nacos 地址与账号信息 -->
                <spring.cloud.nacos.server-addr>http://10.18.20.131:8848</spring.cloud.nacos.server-addr>
                <spring.cloud.nacos.namespace>ys-test</spring.cloud.nacos.namespace>
                <spring.cloud.nacos.username>nacos</spring.cloud.nacos.username>
                <spring.cloud.nacos.password>nacos</spring.cloud.nacos.password>
            </properties>
        </profile>
        <profile>
            <id>ys-local</id>
            <properties>
                <!-- nacos 地址与账号信息 -->
                <spring.cloud.nacos.server-addr>http://10.18.20.131:8848</spring.cloud.nacos.server-addr>
                <spring.cloud.nacos.namespace>ys-local</spring.cloud.nacos.namespace>
                <spring.cloud.nacos.username>nacos</spring.cloud.nacos.username>
                <spring.cloud.nacos.password>nacos</spring.cloud.nacos.password>
            </properties>
        </profile>
        <profile>
            <id>ys-dev</id>
            <properties>
                <!-- nacos 地址与账号信息 -->
                <spring.cloud.nacos.server-addr>http://10.18.20.131:8848</spring.cloud.nacos.server-addr>
                <spring.cloud.nacos.namespace>ys-dev</spring.cloud.nacos.namespace>
                <spring.cloud.nacos.username>nacos</spring.cloud.nacos.username>
                <spring.cloud.nacos.password>nacos</spring.cloud.nacos.password>
            </properties>
        </profile>
    </profiles>
</project>
