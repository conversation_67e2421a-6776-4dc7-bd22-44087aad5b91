# 标签系统流程图和时序图

## 流程图

```mermaid
graph TD
    A[用户] -->|1. 配置标签计算逻辑| B[police-label-web]
    B -->|2. 保存配置| C[数据库]
    D[定时任务] -->|3. 周期性触发| B
    B -->|4. 读取配置| C
    B -->|5. 提交任务| E[police-label-engine]
    E -->|6. 提交到Spark集群| F[Spark集群]
    F -->|7. 执行计算| F
    F -->|8. 计算结果| G[ES数据库]
    F -->|9. 执行状态| E
    E -->|10. 执行状态| B
    B -->|11. 更新状态| C
    A -->|12. 查看结果| B
    B -->|13. 读取结果| G

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bfb,stroke:#333,stroke-width:2px
    style D fill:#fbb,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style F fill:#fbf,stroke:#333,stroke-width:2px
    style G fill:#bfb,stroke:#333,stroke-width:2px
```

## 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Web as police-label-web
    participant DB as 数据库
    participant Scheduler as 定时任务调度器
    participant Engine as police-label-engine
    participant Spark as Spark集群
    participant ES as ES数据库

    User->>Web: 1. 配置标签计算逻辑
    Web->>DB: 2. 保存标签配置
    Note over Web,DB: 包括表选择、算子配置等

    Scheduler->>Web: 3. 触发定时任务
    Web->>DB: 4. 读取标签配置
    Web->>Engine: 5. 提交标签计算任务
    Note over Web,Engine: 传递标签ID、配置信息等

    Engine->>Spark: 6. 提交Spark任务
    Note over Engine,Spark: 生成任务名称，按指定规则

    Spark->>Spark: 7. 执行标签计算
    Spark->>ES: 8. 存储计算结果
    Spark->>Engine: 9. 返回执行状态
    Engine->>Web: 10. 返回执行状态
    Web->>DB: 11. 更新标签状态
    Note over Web,DB: 成功/失败/异常信息

    User->>Web: 12. 查看标签结果
    Web->>ES: 13. 读取标签数据
    Web->>User: 14. 展示标签结果
```

## 组件说明

### police-label-web (标签服务)
- 提供标签配置的Web界面和API
- 管理标签的生命周期
- 调度标签计算任务
- 展示标签计算结果

### police-label-engine (标签计算模块)
- 基于Spark Java开发
- 提供各种标签计算算子
- 负责将配置转换为Spark任务
- 监控Spark任务执行状态

### Spark集群
- 执行分布式计算任务
- 处理大规模数据
- 执行标签计算逻辑

### 数据库
- 存储标签配置信息
- 存储标签元数据
- 记录标签执行状态

### ES数据库
- 存储标签计算结果
- 支持高效的标签数据查询

## 主要流程说明

1. **标签配置**：
   - 用户在Web界面配置标签，包括选择数据表、设置过滤条件、配置算子等
   - 系统将配置保存到数据库

2. **任务调度**：
   - 系统根据配置的周期时间，注册定时任务
   - 定时任务触发时，读取标签配置并提交到标签计算模块

3. **任务执行**：
   - 标签计算模块将配置转换为Spark任务并提交到Spark集群
   - Spark集群执行计算任务，处理数据并生成标签结果
   - 计算结果存储到ES数据库

4. **状态反馈**：
   - Spark任务执行完成后，返回执行状态
   - 标签计算模块将状态传递给标签服务
   - 标签服务更新数据库中的标签状态

5. **结果查询**：
   - 用户可以在Web界面查看标签执行状态和结果
   - 系统从ES数据库读取标签数据并展示

## 错误处理

- 如果标签计算过程中出现异常，Spark任务会捕获异常并返回错误信息
- 标签计算模块将错误信息传递给标签服务
- 标签服务将错误信息记录到数据库，并在Web界面展示
- 用户可以根据错误信息修改标签配置并重新提交

## Spark任务命名规则

Spark任务名称按照以下规则生成：
```
label_[标签ID]_[时间戳]
```

例如：`label_12345_20230601120000`

这样可以确保任务名称的唯一性，并方便追踪特定标签的执行历史。