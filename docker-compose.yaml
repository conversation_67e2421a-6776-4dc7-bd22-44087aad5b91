version: '3.8'
services:
    ## 数据库
    mysql:
        image: mysql:8.2.0
        restart: on-failure
        hostname: police-mysql
        container_name: police-mysql
        command: --default-authentication-plugin=mysql_native_password
        environment:
            ## root用户密码
            - MYSQL_ROOT_PASSWORD=root
        volumes:
            ## 启动容器时候需要执行的sql
            - ./initialization_script/init-db.sql:/docker-entrypoint-initdb.d/initdb.sql
            ## 数据存储目录
            - ./docker-volumes/mysql:/var/lib/mysql
        networks:
            - ${network}
        ports:
            - "3306:3306"
        deploy:
            resources:
                limits:
                    cpus: "1"
                    memory: 1G
                reservations:
                    memory: 256M
    ## redis缓存服务器
    redis:
        image: redis:7.2.3
        restart: on-failure
        hostname: police-redis
        container_name: police-redis
        command: redis-server --requirepass trsadmin@028
        networks:
            - ${network}
        volumes:
            - ./docker-volumes/redis/:/data/  ## redis数据存储
        ports:
            - "6379:6379"
        deploy:
            resources:
                limits:
                    cpus: "4"
                    memory: 1G
                reservations:
                    memory: 64M

    ## 微服务注册中心与配置中心
    nacos:
        image: nacos/nacos-server:v2.2.3-slim
        restart: on-failure
        hostname: police-nacos
        container_name: police-nacos
        command:
            - nacos
        depends_on:
            - mysql
        networks:
            - ${network}
        environment:
            - MODE=standalone
            - SPRING_DATASOURCE_PLATFORM=mysql  #使用mysql作为存储
            - MYSQL_SERVICE_HOST=police-mysql
            - MYSQL_SERVICE_PORT=3306
            - MYSQL_SERVICE_DB_NAME=nacos
            - MYSQL_SERVICE_USER=root
            - MYSQL_SERVICE_PASSWORD=root
            - NACOS_AUTH_ENABLE=true
            - NACOS_AUTH_TOKEN=8b92c609089f74db3c5ee04bd7d4d89e8b92c609089f74db3c5ee04bd7d4d89e
            - NACOS_AUTH_IDENTITY_KEY=nacos
            - NACOS_AUTH_IDENTITY_VALUE=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjpbeyJ4eHh4IjoieXl5In1dLCJpYXQiOjE2Nzk1NzU0NzIsImV4cCI6MTY3OTUwMDc5OSwiYXVkIjoiIiwiaXNzIjoiIiwic3ViIjoiIn0.nhN_hKcnjlX0QW-kQj2beLehBzrQnB1IhhJZe2WO-c0
        ports:
            - "8848:8848"
        deploy:
            resources:
                limits:
                    cpus: '1'
                    memory: 1G
                reservations:
                    memory: 256M
networks:
    police:
        external: true