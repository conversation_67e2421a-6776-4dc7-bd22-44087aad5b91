package com.trs.police.common.core.utils;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.junit.jupiter.api.Test;

class TimeUtilTest {

    @Test
    void split() {
        LocalDateTime beginTime = LocalDateTime.of(2023, 6, 12, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2023, 8, 11, 0, 0, 0);
        List<LocalDateTime> timePoints = TimeUtil.split(beginTime, endTime, ChronoUnit.MONTHS);
        assertThat(timePoints).hasSize(3);
        assertThat(timePoints).isEqualTo(List.of(
            LocalDateTime.of(2023, 6, 12, 0, 0, 0),
            LocalDateTime.of(2023, 7, 12, 0, 0, 0),
            LocalDateTime.of(2023, 8, 11, 0, 0, 0)
        ));
    }
}