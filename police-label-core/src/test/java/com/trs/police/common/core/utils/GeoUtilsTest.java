package com.trs.police.common.core.utils;

import com.trs.police.common.core.vo.Geometries;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.proj4j.*;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.trs.police.common.core.utils.GeoUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for GeoUtils
 *
 * <AUTHOR>
 * @create 2024-10-10 12:27
 */
@Slf4j
public class GeoUtilsTest {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();

    private static final CoordinateReferenceSystem WGS_84 = new CRSFactory().createFromName("epsg:4326");
    private static final CoordinateReferenceSystem MERCATOR = new CRSFactory().createFromName("epsg:3857");

    /**
     * test
     *
     * @param args arguments
     */
    public static void main(String[] args) {
        String p1 = wgs84ToMercator("POINT(105.477618 28.890501)");
        log.info(p1);
        String p2 = wgs84ToMercator("POINT(105.498475 28.884949)");
        log.info(p2);
        int height = 50;

        // 矩形
        Coordinate c1 = wktToCoordinate(p1);
        Coordinate c2 = wktToCoordinate(p2);
        Coordinate[] coordinates = lineToRectangleCoordinates(c1, c2, height);
        Polygon polygon = GEOMETRY_FACTORY.createPolygon(
                Arrays.stream(coordinates).map(c -> convert(c, MERCATOR, WGS_84)).toArray(Coordinate[]::new));
        //输出wkt
        log.info(polygon.toText());
    }

    @NotNull
    private static Coordinate convert(Coordinate srcPoint, CoordinateReferenceSystem srcCrs,
                                      CoordinateReferenceSystem targetCrs) {
        // 创建坐标转换对象
        CoordinateTransform transform = new BasicCoordinateTransform(srcCrs, targetCrs);
        // 定义原始WGS84坐标点
        ProjCoordinate srcCoordinate = new ProjCoordinate(srcPoint.getX(), srcPoint.getY());
        // 目标Mercator坐标点
        ProjCoordinate targetCoordinate = new ProjCoordinate();
        // 进行坐标转换
        transform.transform(srcCoordinate, targetCoordinate);
        // 输出WKT字符串
        return new Coordinate(doubleScale(targetCoordinate.x), doubleScale(targetCoordinate.y));
    }

    @Test
    void testPointInInlineGeometry() {
        // Test point in China
        String result1 = GeoUtils.getInstance().pointInInlineGeometry(
                GeoUtils.makePointByLongitudeLatitude(104.070379, 30.572357)
        );
        assertNotNull(result1);
        
        // Test point outside China with custom handler
        String result2 = GeoUtils.getInstance().pointInInlineGeometry(
                GeoUtils.makePointByLongitudeLatitude(104.070379, -30.572357),
                it -> "no"
        );
        assertEquals("no", result2);
        
        // Test with boolean return type
        Boolean result3 = GeoUtils.getInstance().pointInInlineGeometry(
                GeoUtils.makePointByLongitudeLatitude(104.070379, 30.572357),
                it -> true,
                it -> false
        );
        assertTrue(result3);
    }
    
    @Test
    void testMakePointByLongitudeLatitude() {
        // Test creating a point WKT from longitude and latitude
        String point = GeoUtils.makePointByLongitudeLatitude(120.0, 30.0);
        assertEquals("POINT(120.0 30.0)", point);
        
        // Test with negative coordinates
        String negativePoint = GeoUtils.makePointByLongitudeLatitude(-120.0, -30.0);
        assertEquals("POINT(-120.0 -30.0)", negativePoint);
    }
    
    @Test
    void testIsPointInGeometry() {
        // Create a test polygon
        String polygon = "POLYGON((100 20, 110 20, 110 30, 100 30, 100 20))";
        
        // Test point inside polygon
        String pointInside = "POINT(105 25)";
        assertTrue(GeoUtils.isPointInGeometry(pointInside, polygon));
        
        // Test point outside polygon
        String pointOutside = "POINT(95 25)";
        assertFalse(GeoUtils.isPointInGeometry(pointOutside, polygon));
        
        // Test with a set of polygons
        Set<String> polygons = new HashSet<>();
        polygons.add(polygon);
        polygons.add("POLYGON((90 10, 95 10, 95 15, 90 15, 90 10))");
        
        // Point inside first polygon
        assertTrue(GeoUtils.isPointInGeometry(pointInside, polygons));
        
        // Point outside all polygons
        String pointOutsideAll = "POINT(80 80)";
        assertFalse(GeoUtils.isPointInGeometry(pointOutsideAll, polygons));
        
        // Point inside second polygon
        String pointInsideSecond = "POINT(92 12)";
        assertTrue(GeoUtils.isPointInGeometry(pointInsideSecond, polygons));
    }
    
    @Test
    void testGetDistance() {
        // Test distance calculation between two points
        double distance = GeoUtils.getDistance(30.0, 120.0, 30.1, 120.1);
        assertTrue(distance > 0);
        
        // Test distance to self should be 0
        double selfDistance = GeoUtils.getDistance(30.0, 120.0, 30.0, 120.0);
        assertEquals(0.0, selfDistance, 0.001);
    }
    
    @Test
    void testWgs84ToMercatorAndBack() {
        try {
            // Test WGS84 to Mercator conversion
            String wgs84Point = "POINT(120.0 30.0)";
            String mercatorPoint = GeoUtils.wgs84ToMercator(wgs84Point);
            assertNotNull(mercatorPoint);
            assertTrue(mercatorPoint.startsWith("POINT("));
            
            // Test Mercator back to WGS84
            String backToWgs84 = GeoUtils.mercatorToWgs84(mercatorPoint);
            assertNotNull(backToWgs84);
            assertTrue(backToWgs84.startsWith("POINT("));
            
            // The coordinates should be approximately the same after round-trip conversion
            // Extract coordinates for comparison
            String[] originalCoords = wgs84Point.replace("POINT(", "").replace(")", "").split(" ");
            String[] convertedCoords = backToWgs84.replace("POINT(", "").replace(")", "").split(" ");
            
            double originalX = Double.parseDouble(originalCoords[0]);
            double originalY = Double.parseDouble(originalCoords[1]);
            double convertedX = Double.parseDouble(convertedCoords[0]);
            double convertedY = Double.parseDouble(convertedCoords[1]);
            
            // Allow for small rounding differences
            assertEquals(originalX, convertedX, 0.001);
            assertEquals(originalY, convertedY, 0.001);
        } catch (Exception e) {
            fail("Exception should not be thrown: " + e.getMessage());
        }
    }
    
    @Test
    void testConvertCircleToPolygon() {
        // Create a test circle with longitude, latitude and radius
        Geometries circle = new Geometries();
        // Set properties using reflection since we don't have access to the actual implementation
        try {
            java.lang.reflect.Field longitudeField = Geometries.class.getDeclaredField("longitude");
            longitudeField.setAccessible(true);
            longitudeField.set(circle, 120.0);
            
            java.lang.reflect.Field latitudeField = Geometries.class.getDeclaredField("latitude");
            latitudeField.setAccessible(true);
            latitudeField.set(circle, 30.0);
            
            java.lang.reflect.Field radiusField = Geometries.class.getDeclaredField("radius");
            radiusField.setAccessible(true);
            radiusField.set(circle, 1000); // 1km radius
            
            // Convert to polygon
            String polygonWkt = GeoUtils.convertCircleToPolygon(circle);
            assertNotNull(polygonWkt);
            assertTrue(polygonWkt.startsWith("POLYGON(("));
        } catch (Exception e) {
            fail("Exception should not be thrown: " + e.getMessage());
        }
    }
    
    @Test
    void testDoubleScale() {
        // Test rounding to 6 decimal places
        double result = GeoUtils.doubleScale(123.4567891234);
        assertEquals(123.456789, result, 0.0000001);
        
        // Test with negative number
        double negativeResult = GeoUtils.doubleScale(-123.4567891234);
        assertEquals(-123.456789, negativeResult, 0.0000001);
    }
}
