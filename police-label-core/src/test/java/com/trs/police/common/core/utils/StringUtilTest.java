package com.trs.police.common.core.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for StringUtil
 */
public class StringUtilTest {

    @Test
    public void testIsEmpty() {
        // Test with null string
        assertTrue(StringUtil.isEmpty(null));
        
        // Test with empty string
        assertTrue(StringUtil.isEmpty(""));
        
        // Test with whitespace string
        assertTrue(StringUtil.isEmpty("   "));
        
        // Test with non-empty string
        assertFalse(StringUtil.isEmpty("test"));
    }
    
    @Test
    public void testRemoveSpecialCharacters() {
        // Test with null string
        assertNull(StringUtil.removeSpecialCharacters(null));
        
        // Test with string containing special characters
        assertEquals("test\\%test\\_test", StringUtil.removeSpecialCharacters("test%test_test"));
        
        // Test with string containing backslashes
        assertEquals("test\\\\test", StringUtil.removeSpecialCharacters("test\\test"));
    }
    
    @Test
    public void testConvertToUnderscore() {
        // Test with camelCase string
        assertEquals("camel_case", StringUtil.convertToUnderscore("camelCase"));
        
        // Test with PascalCase string
        assertEquals("_pascal_case", StringUtil.convertToUnderscore("PascalCase"));
        
        // Test with already underscore string
        assertEquals("already_underscore", StringUtil.convertToUnderscore("already_underscore"));
    }
    
    @Test
    public void testUnderlineToCamel() {
        // Test with underscore string
        assertEquals("camelCase", StringUtil.underlineToCamel("camel_case"));
        
        // Test with multiple underscores
        assertEquals("camelCaseTest", StringUtil.underlineToCamel("camel_case_test"));
        
        // Test with already camelCase string (no underscores)
        assertEquals("camelcase", StringUtil.underlineToCamel("camelcase"));
    }
}