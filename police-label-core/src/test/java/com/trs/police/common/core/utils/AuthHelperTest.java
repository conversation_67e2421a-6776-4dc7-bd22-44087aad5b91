package com.trs.police.common.core.utils;

import com.trs.police.common.core.vo.permission.SimpleUserVO;
import io.vavr.control.Try;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AuthHelperTest {

    @Test
    void getNotNullSimpleUser() {

        Try error = Try.of(()->AuthHelper.getNotNullSimpleUser(()->null));
        assertTrue(error.isFailure());
//        error.getCause().printStackTrace();

        SimpleUserVO vo = AuthHelper.getNotNullSimpleUser(()->{
            SimpleUserVO defaultUser = new SimpleUserVO();
            defaultUser.setUserName("测试");
            defaultUser.setDeptShortName("测试部门");
            return defaultUser;
        });

        assertEquals(vo.getUserName(), "测试");
        assertEquals(vo.getDeptShortName(), "测试部门");
        assertTrue(vo.getUserId() == null);
    }
}