package com.trs.police.common.core.entity.node;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.vo.Geometries;

import java.util.Objects;

/**
 * 多边形
 *
 * <AUTHOR>
 */
public class GeoValue implements Value {

    private String value;

    public GeoValue(String value) {
        this.value = value;
    }

    @Override
    public String getValueString() {
        if (Objects.isNull(value) || value.isEmpty()) {
            return value;
        }
        if (value.startsWith("{")) {
            Geometries geo = JSON.parseObject(value, Geometries.class);
            return geo.convertToPolygon();
        }
        return value;
    }

    @Override
    public Boolean isNull() {
        return Objects.isNull(value) || value.isEmpty();
    }

    @Override
    public Boolean isEmpty() {
        return false;
    }
}
