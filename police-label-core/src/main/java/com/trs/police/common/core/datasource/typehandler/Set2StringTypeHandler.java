package com.trs.police.common.core.datasource.typehandler;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * mybatis数据类型转换器,处理数据库逗号分隔字符串的转换
 *
 * <AUTHOR>
 */
public class Set2StringTypeHandler implements TypeHandler<Set<String>> {

    private static final String DELIMITER = ",";

    @Override
    public void setParameter(PreparedStatement ps, int i, Set<String> parameter, JdbcType jdbcType)
        throws SQLException {
        ps.setString(i, StringUtils.join(parameter, DELIMITER));
    }

    @Override
    public Set<String> getResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return StringUtils.isBlank(value) ? Collections.emptySet()
            : Arrays.stream(StringUtils.split(value, DELIMITER)).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return StringUtils.isBlank(value) ? Collections.emptySet()
            : Arrays.stream(StringUtils.split(value, DELIMITER)).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return StringUtils.isBlank(value) ? Collections.emptySet()
            : Arrays.stream(StringUtils.split(value, DELIMITER)).collect(Collectors.toSet());
    }
}
