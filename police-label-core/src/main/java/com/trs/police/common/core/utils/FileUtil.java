package com.trs.police.common.core.utils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件工具
 *
 * <AUTHOR>
 * @date 2022/7/6 16:24
 */
@Slf4j
public class FileUtil {

    private FileUtil(){

    }

    /**
     * byte转显示size
     *
     * @param size  byte大小
     * @param index 要保留的小数位数
     * @return java.lang.String 保留小数后的文件大小
     **/
    public static String byteCountToDisplaySize(long size, Integer index) {
        String displaySize;
        String format = "%." + index + "f";
        if (size / 1152921504606846976L > 0L) {
            displaySize = String.format(format, size / 1152921504606846976f) + " EB";
        } else if (size / 1125899906842624L > 0L) {
            displaySize = String.format(format, size / 1125899906842624f) + " PB";
        } else if (size / 1099511627776L > 0L) {
            displaySize = String.format(format, size / 1099511627776f) + " TB";
        } else if (size / 1073741824L > 0L) {
            displaySize = String.format(format, size / 1073741824f) + " GB";
        } else if (size / 1048576L > 0L) {
            displaySize = String.format(format, size / 1048576f) + " MB";
        } else if (size / 1024L > 0L) {
            displaySize = String.format(format, size / 1024f) + " KB";
        } else {
            displaySize = size + " bytes";
        }
        return displaySize;
    }


    /**
     * 将resource中的文件复制到指定路径
     *
     * @param filePath 文件路径
     * @return 复制后的路径
     */
    public static String createTempFile(String filePath) {
        try {
            //获取当前项目所在的绝对路径
            String filePathPrefix = System.getProperty("user.dir");
            log.info("project run path：" + filePathPrefix);
            //获取模板下的路径
            String newFilePath = filePathPrefix + filePath;
            log.info("actual filePath:" + newFilePath);
            //检查项目运行时的src下的对应路径
            File newFile = new File(newFilePath);
            if (newFile.isFile() && newFile.exists()) {
                return newFilePath;
            }
            //当项目打成jar包会运行下面的代码，而且复制一份到src路径下
            if (filePath.charAt(0) == '/') {
                filePath = filePath.substring(1);
            }
            InputStream certStream = FileUtil.class.getClassLoader().getResourceAsStream(filePath);
            assert certStream != null;
            byte[] certData = IOUtils.toByteArray(certStream);
            FileUtils.writeByteArrayToFile(newFile, certData);
            return newFilePath;
        } catch (IOException e) {
            log.error("复制文件失败 --> 异常信息：" + e);
        }
        return null;
    }

    /**
     *  下载项目内的文件
     *
     * @param response response
     * @param path 相对地址
     * @param fileName 下载后的文件名
     */
    public static void downloadFile(HttpServletResponse response, String path, String fileName){
        try (InputStream inStream = FileUtil.class.getResourceAsStream(path)) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download;");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            byte[] b = new byte[1024];
            int len;
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
        }catch (Exception e){
            log.error("下载文件:[{}]失败！", path);
            e.printStackTrace();
        }

    }
}
