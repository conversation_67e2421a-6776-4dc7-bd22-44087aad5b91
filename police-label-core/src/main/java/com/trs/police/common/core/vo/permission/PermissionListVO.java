package com.trs.police.common.core.vo.permission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 权限列表
 *
 * <AUTHOR> yanghy
 * @date : 2022/7/25 14:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionListVO {

    /**
     * 权限id
     */
    private Long id;
    /**
     * 权限名称
     */
    private String name;
    /**
     * 类型
     */
    private String type;
    /**
     * 描述
     */
    private String description;
    /**
     * 创建人  示例：王芳（青羊区涉利益诉求专班）
     */
    private String creator;

    /**
     * 创建时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
