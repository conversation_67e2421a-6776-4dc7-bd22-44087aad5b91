package com.trs.police.common.core.vo.node;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.FieldTypeMapping;

/**
 * 值工厂
 */
public class ValueFactory {

    /**
     * 获取值
     *
     * @param value 字符串格式的值
     * @param type 字段类型
     * @return o
     */
    public static Object getValue(String value, FieldTypeMapping type) {
        switch (type) {
            case STRING:
                return value;
            case NUMBER:
                return value != null ? Double.parseDouble(value) : null;
            case DATETIME:
                return value != null ? TimeUtils.stringToDate(value) : null;
            default:
                return value;
        }
    }

    /**
     * 获取值
     *
     * @param value 字符串格式的值
     * @param type 字段类型
     * @return o
     */
    public static Object getValue(String value, String type) {
        return getValue(value, FieldTypeMapping.fromName(type));
    }
}
