package com.trs.police.common.core.utils;

import cn.com.westone.wcspsdk.AuthSecretParameterSpec;
import cn.com.westone.wcspsdk.CryptoServicePlatform;
import cn.com.westone.wcspsdk.InvalidParameterException;
import cn.com.westone.wcspsdk.WCSPException;
import cn.com.westone.wcspsdk.baseservice.co.*;
import cn.com.westone.wcspsdk.baseservice.km.KMSecretKey;
import cn.com.westone.wcspsdk.baseservice.km.KMService;
import cn.com.westone.wcspsdk.baseservice.km.KeyNoUpdateStrategySpec;
import cn.com.westone.wcspsdk.impl.base.WCSPCO;
import cn.com.westone.wcspsdk.typicalservice.FileSecurityService;
import cn.com.westone.wcspsdk.typicalservice.LightDataService;
import cn.com.westone.wcspsdk.typicalservice.RecordSecurityService;
import cn.com.westone.wcspsdk.typicalservice.SessionEncryptService;
import cn.com.westone.wcspsdk.util.ConversionUtils;
import cn.com.westone.wcspsdk.util.KeyUtils;
import cn.com.westone.wcspsdk.util.cert.X501DName;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static cn.com.westone.wcspsdk.util.ConversionUtils.Data;

/**
 * wcspsdk加密工具类
 *
 */
@Slf4j
public class WcspsdkUtils {
    /**
     * 必看
     * 首次部署时，需要先访问接口 /global/public/wcsp/initKeyId 获取keyId
     * 然后添加到nacos配置com.trs.wcspsdk.keyId
     * 其他必需nacos配置：
     * 认证密码：com.trs.wcspsdk.secret
     * 密码服务平台URL：com.trs.wcspsdk.url
     * 租户id: com.trs.wcspsdk.tenantId
     * 应用ID：com.trs.wcspsdk.appId
     * （非必需）wcspSDK的工作目录: com.trs.wcspsdk.workdir
     *
     * 应用集成密码服务SDK的调用流程主要分为以下五个步骤：
     * 1．在密码服务平台开通需要的密码服务，获取租户ID(tenantId)、应用ID(appId)、认证密码(secret)等信息
     * 2.在初始化参数列表中配置密码服务平台URL(url)和SDK工作目录(workdir)等信息；
     * 3．使用租户ID、应用ID、初始化参数列表对SDK进行初始化；
     * 4．使用认证密码或第三方应用的授权认证码对SDK进行认证；
     * 5．获取需要的各密码服务实例；
     * 6．调用各密码服务提供的功能接口完成业务功能；
     * 7．终结SDK。
     * 注意事项：
     * 1、密码服务 SDK的对象(如：CryptoServicePlatform)不能多线程同时调用，
     * 可以把把密码服务 SDK 的对象放到 ThreadLocal中或使用对象池，以保证一个对象同一时刻只会被一个线程调用，而且可以获得更好的性能；
     * 2、密码服务 SDK 的对象在抛出异常之后不建议在继续使用，建议抛出异常之后重新获取和初始化新的对象进行使用
     *
     * 使用密码机需要添加以下三个参数，并且 COService coService = COService.getInstance(COService.SERVICE_TYPE_CO_LOCAL, platform);
     * params.put(CryptoServicePlatform.INIT_PARAM_HSM_IP, "192.xxx.207.26");//密码机ip
     * params.put(CryptoServicePlatform.INIT_PARAM_HSM_PORT, 6666);//密码机端口
     * params.put(CryptoServicePlatform.INIT_PARAM_COSVC_LOCAL_TYPE, CryptoServicePlatform.COSVC_LOCAL_TYPE_SDF);
     *
     *
     * url: 密码服务平台URL，只能使用域名访问不能直接使用ip访问，如果不能使用DNS,则需要在本机hosts文件中配置域名ip映射
     */
    private static String url = "https://xxx.xxx.com:32443";

    /**
     * tenantId: 租户ID，开通密码服务后通过密码服务平台获取
     */
    private static String tenantId = "";

    /**
     * appId: 应用ID，开通密码服务后通过密码服务平台获取
     */
    private static String appId = "";

    /**
     * secret: 认证密码，开通密码服务后通过密码服务平台获取
     */
    private static String secret = "";

    /**
     * params: 密码服务平台初始化参数
     */
    private static HashMap<String, Object> params = new HashMap<>();

    /**
     * 使用密码机PIN码为：11111111 本地软卡PIN码为：123456,如果不使用密码机和软卡则不需要pin
     */
    private static String pin = "";

    /**
     * WcspSDK的工作目录需要读写权限，需要应用系统自己先创建完成
     */
    private static String workdir = "workdir";

    private static ThreadLocal<CryptoServicePlatform> cryptoServicePlatform = new ThreadLocal<>();

    static {
        params.put(CryptoServicePlatform.INIT_PARAM_URL, url);
        params.put(CryptoServicePlatform.INIT_PARAM_WORKDIR, workdir);
    }

    /**
     * 加密解密示例
     *
     * @param args args
     */
    public static void main(String[] args) {
        //批量加密和解密 短数据(不大于4096字节)代码示例
        sm4BatchShortDataEncryptAndDecrypt();

        //加密和解密 长数据(大于4096字节)代码示例
        sm4LongDataEncryptAndDecrypt();

        //MAC 短数据(不大于4096字节)完整性保护代码示例
        macRecordIntegrity();

        //MAC 长数据(大于4096字节)完整性保护代码示例
        macLongRecordIntegrity();

        //SM2算法 签名验签
        sm2ShortDataSignAndVerify();

        //文件加解密1(密文输出到文件)
        fileEncryptStorage1();

        //文件加解密2(密文输出到内存)
        fileEncryptStorage2();

        //双端通信会话信源加解密代码示例
        sessionEncrypt();
    }

    /**
     * 初始化密钥标识keyId
     *
     * @return keyId
     */
    public static String initKeyId() throws InvalidParameterException, WCSPException {
        CryptoServicePlatform platform = getCryptoServicePlatform();
        return createSecretKeySm4(platform);
    }

    private static String getKeyId() throws WCSPException {
        String keyId = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.keyId");
        if(StringUtil.isEmpty(keyId)){
            throw new WCSPException("未配置keyId！");
        }
        return keyId;
    }

    private static String getMacKeyId() throws WCSPException {
        String keyId = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.macKeyId");
        if(StringUtil.isEmpty(keyId)){
            throw new WCSPException("macKeyId！");
        }
        return keyId;
    }

    /**
     * 初始化密钥标识IV
     *
     * @return IV
     */
    public static String initIV() throws InvalidParameterException, WCSPException {
        CryptoServicePlatform platform = getCryptoServicePlatform();
        COService coService = getCoService(platform);
        byte[] iv = coService.generateRandom(16);
        return Data.toBase64String(iv);
    }

    private static String getIV() throws WCSPException {
        String iv = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.IV");
        if(StringUtil.isEmpty(iv)){
            throw new WCSPException("未配置IV！");
        }
        return iv;
    }

    private static String getMacIV() throws WCSPException {
        String iv = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.macIV");
        if(StringUtil.isEmpty(iv)){
            throw new WCSPException("未配置macIV！");
        }
        return iv;
    }

    /**
     * 加密
     *
     * @param data 待加密的数据
     * @return 加密后的数据
     */
    public static String sm4DataLightEncrypt(String data) throws Exception {
        byte[] bytes = Data.fromUTF8String(data);
        try {
            // 获取敏感数据轻量级加解密服务实例
            LightDataService ldService = getLightDataService();
            // 对示例敏感数据进行加密并将密文转换为Base64编码的字符串
            byte[] encryptBytes = ldService.encrypt(bytes);
            String encData = Data.toBase64String(encryptBytes);

            return encData;
        } catch (Exception e) {
            log.error("wcsp加密失败", e);
            // 密码服务 SDK 的对象在抛出异常之后不建议在继续使用，建议抛出异常之后重新获取和初始化新的对象进行使用
            cryptoServicePlatform.set(null);
            throw e;
        }
    }

    /**
     * 批量加密
     *
     * @param datas 待加密的数据
     * @return 加密后的数据
     */
    public static List<String> sm4DataLightEncrypt(List<String> datas) throws Exception {
        byte[][] dataBytes = datas.stream().map(data -> Data.fromUTF8String(data)).collect(Collectors.toList()).toArray(byte[][]::new);
        try {
            // 获取敏感数据轻量级加解密服务实例
            LightDataService ldService = getLightDataService();
            // 对示例敏感数据进行加密并将密文转换为Base64编码的字符串
            byte[][] encryptBytes = ldService.encrypt(dataBytes);
            List<String> encDatas = Arrays.stream(encryptBytes).map(Data::toBase64String).collect(Collectors.toList());

            return encDatas;
        } catch (Exception e) {
            log.error("wcsp加密失败", e);
            // 密码服务 SDK 的对象在抛出异常之后不建议在继续使用，建议抛出异常之后重新获取和初始化新的对象进行使用
            cryptoServicePlatform.set(null);
            throw e;
        }
    }

    /**
     * 解密
     *
     * @param data 待解密的数据
     * @return 解密后的数据
     */
    public static String sm4DataLightDecrypt(String data) throws Exception {
        byte[] bytes = Data.fromBase64String(data);
        try {
            LightDataService ldService = getLightDataService();
            // 对示例敏感数据进行加密并将密文转换为Base64编码的字符串
            byte[] decryptBytes = ldService.decrypt(bytes);
            String decryptBytesData = Data.toUTF8String(decryptBytes);

            return decryptBytesData;
        } catch (Exception e) {
            log.error("wcsp解密失败", e);
            // 密码服务 SDK 的对象在抛出异常之后不建议在继续使用，建议抛出异常之后重新获取和初始化新的对象进行使用
            cryptoServicePlatform.set(null);
            throw e;
        }
    }

    /**
     * 批量解密
     *
     * @param datas 待解密的数据
     * @return 解密后的数据
     */
    public static List<String> sm4DataLightDecrypt(List<String> datas) throws Exception {
        byte[][] dataBytes = datas.stream().map(data -> Data.fromBase64String(data)).collect(Collectors.toList()).toArray(byte[][]::new);
        try {
            LightDataService ldService = getLightDataService();
            // 对示例敏感数据进行加密并将密文转换为Base64编码的字符串
            byte[][] decryptBytes = ldService.decrypt(dataBytes);
            List<String> decryptDatas = Arrays.stream(decryptBytes).map(Data::toUTF8String).collect(Collectors.toList());

            return decryptDatas;
        } catch (Exception e) {
            log.error("wcsp解密失败", e);
            // 密码服务 SDK 的对象在抛出异常之后不建议在继续使用，建议抛出异常之后重新获取和初始化新的对象进行使用
            cryptoServicePlatform.set(null);
            throw e;
        }
    }

    /**
     * 完整性解密
     *
     * @param data 数据
     * @return 解密后的数据
     */
    public static String mac(String data) {
        Boolean checkMac = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.checkMac", Boolean.class, false);
        if(!checkMac){
            return null;
        }
        try {
            RecordSecurityService securityService = getRecordSecurityService();
            return securityService.calculateMAC(data);
        } catch (Exception e) {
            log.error("mac解密失败", e);
            // 密码服务 SDK 的对象在抛出异常之后不建议在继续使用，建议抛出异常之后重新获取和初始化新的对象进行使用
            cryptoServicePlatform.set(null);
            throw new RuntimeException(e.getCause());
        }
    }

    /**
     * 完整性验证
     *
     * @param mac 加密数据
     * @param data 原始数据
     * @return 解密后的数据
     */
    public static Boolean verifyMac(String mac, String data){
        Boolean checkMac = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.checkMac", Boolean.class, false);
        if(!checkMac){
            return true;
        }
        try {
            RecordSecurityService securityService = getRecordSecurityService();
            return securityService.verifyMAC(mac, data);
        } catch (Exception e) {
            log.error("mac验证失败", e);
            // 密码服务 SDK 的对象在抛出异常之后不建议在继续使用，建议抛出异常之后重新获取和初始化新的对象进行使用
            cryptoServicePlatform.set(null);
            throw new RuntimeException(e.getCause());
        }
    }

    /**
     * SM4算法 批量加密和解密短数据(不大于4096字节)代码示例
     * 注：
     * 1.加密和解密使用的密钥，密钥的唯一标识keyId，需要应用系统自己保存，加密和解密需要使用相同的keyId
     * 2.使用SM4_CBC加解密
     *
     * @return 结果
     */
    private static boolean sm4BatchDataLightEncrypt() {
        String data1 = "待加密的示例敏感数据";
        String data2 = "待加密的长长长长长长示例敏感数据";
        byte[][] bytes = {Data.fromUTF8String(data1), Data.fromUTF8String(data2)};

        try {
            log.info("--------应用服务端 敏感数据轻量级批量加解密服务接口调用示例--------");

            log.info("---该租户需开通密钥管理服务和远程密码计算服务，并授权给应用使用");

            log.info("---获取密钥生成服务实例");
            CryptoServicePlatform platform = getCryptoServicePlatform();

            log.info("---生成密钥，保存返回的密钥标识字符串供以后使用");
            String keyId = createSecretKeySm4(platform);

            log.info("---获取敏感数据轻量级加解密服务实例");
            LightDataService ldService = LightDataService.getInstance(platform, keyId);
            //不传入keyId,使用默认的加解密密钥
            //LightDataService ldService = LightDataService.getInstance(kgService.platform());

            log.info("---对示例敏感数据进行加密并将密文转换为Base64编码的字符串");
            byte[][] encrypt = ldService.encrypt(bytes);

            log.info("---将加密后的敏感数据保存或传输");

            log.info("---对Base64编码的密文字符串进解码和解密获得明文的示例敏感数据");
            byte[][] decrypt = ldService.decrypt(encrypt);
            if (!Arrays.equals(Data.fromUTF8String(data1), decrypt[0]) || !Arrays.equals(Data.fromUTF8String(data2), decrypt[1])) {
                throw new Exception("demo dataLightEncrypt_1 run failed!");
            }
            log.info("---示例数据批量加解密正确");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        
        return true;
    }

    /**
     * MAC算法 MAC短数据(不大于4096字节)代码示例
     * 注：
     * 1.MAC使用的密钥，密钥的唯一标识keyId，需要应用系统自己保存，MAC使用相同的keyId
     * 2.使用SM4_MAC算法
     *
     * @return 结果
     */
    private static boolean macRecordIntegrity() {
        String field1 = "需要完整性保护记录的第一个字段";
        String field2 = "需要完整性保护记录的第二个字段";
        String field3 = "需要完整性保护记录的第三个字段";

        try {
            log.info("--------应用服务端 记录数据安全服务接口调用示例   记录完整性保护--------");

            log.info("---该租户需开通密钥管理服务和远程密码计算服务，并授权给应用使用");

            log.info("---获取密钥生成服务实例");
            CryptoServicePlatform platform = getCryptoServicePlatform();

            log.info("---生成密钥，保存返回的密钥标识字符串供以后使用");
            String keyId = createSecretKeySm4(platform);

            log.info("---获取敏感数据轻量级加解密服务实例");
            RecordSecurityService rsService = RecordSecurityService.getInstance(platform, keyId);
            //不传入keyId,使用默认的MAC密钥
//            RecordSecurityService rsService = RecordSecurityService.getInstance(kgService.platform());

            log.info("---初次计算记录的MAC值，并与记录一起保存");
            String mac = rsService.calculateMAC(field1, field2, field3);

            log.info("---以后需要读取记录时再次计算记录的MAC值，并与初次计算的MAC值对比");
            if (!rsService.verifyMAC(mac, field1, field2, field3)) {
                throw new Exception("demo recordIntegrity_1 run failed!");
            }
            log.info("---示例数据完整性校验正确");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        
        return true;
    }

    /**
     * MAC算法 MAC长数据(大于4096字节)代码示例
     * *注：
     * 1.MAC使用的密钥，密钥的唯一标识keyId，需要应用系统自己保存，MAC使用相同的keyId
     * 2.使用SM4_MAC算法
     *
     * @return 结果
     */
    private static boolean macLongRecordIntegrity() {
        byte[] data1 = new byte[2 * 1024 * 1024];
        int length = data1.length;

        try {
            log.info("--------应用终端 基础密码服务接口调用示例   CBCMac数据完整性保护和校验 密钥管理服务 密码计算服务--------");

            log.info("---密码服务平台初始化");
            CryptoServicePlatform platform = getCryptoServicePlatform();
            KMService kmService = getKmService(platform);

            log.info("---在密钥管理服务中生成CBCMac完整性保护密钥，从中获取并保存密钥标识字符串供以后使用");
            String keyId = createSecretKeySm4(platform);
            log.info("---通过保存的密钥标识字符串从密钥管理服务中重新获取完整性保护密钥");
            KMSecretKey kmSecretKey = kmService.getSecretKey(keyId);

            log.info("---密码计算服务初始化");
            COService coService = getCoService(platform);

            log.info("---产生CBCMac完整性保护所需的iv，该数据需与密钥标识字符串一起保存");
            byte[] iv = coService.generateRandom(16);

            log.info("---初次计算数据的MAC值，并保存");
            byte[] cbcMac1 = null;
            log.info("---通过密码计算服务获取CBCMac运算对象，用于CBCMac运算，算法填充为PKCS7");
            try (Mac mac = coService.getMac(kmSecretKey, COService.ALGORITHM_SM4_MAC, new IVParameterSpec(iv))) {
                byte[] temp;
                int offset = 0;
                while (offset + WCSPCO.WCSP_CO_MAX_DATA_LENGTH <= length) {
                    temp = Arrays.copyOfRange(data1, offset, offset + WCSPCO.WCSP_CO_MAX_DATA_LENGTH);
                    mac.update(temp);
                    offset += WCSPCO.WCSP_CO_MAX_DATA_LENGTH;
                }
                if (offset < length) {
                    temp = Arrays.copyOfRange(data1, offset, length);
                    cbcMac1 = mac.doFinal(temp);
                } else {
                    cbcMac1 = mac.doFinal();
                }
            }


            log.info("---以后需要使用数据时再次计算数据MAC值，并与初次计算的MAC值对比");

            byte[] cbcMac2 = null;
            log.info("---通过密码计算服务获取CBCMac运算对象，用于CBCMac运算，算法填充为PKCS7");
            try (Mac mac = coService.getMac(kmSecretKey, COService.ALGORITHM_SM4_MAC, new IVParameterSpec(iv))) {
                byte[] temp;
                int offset = 0;
                while (offset + WCSPCO.WCSP_CO_MAX_DATA_LENGTH <= length) {
                    temp = Arrays.copyOfRange(data1, offset, offset + WCSPCO.WCSP_CO_MAX_DATA_LENGTH);
                    mac.update(temp);
                    offset += WCSPCO.WCSP_CO_MAX_DATA_LENGTH;
                }
                if (offset < length) {
                    temp = Arrays.copyOfRange(data1, offset, length);
                    cbcMac2 = mac.doFinal(temp);
                } else {
                    cbcMac2 = mac.doFinal();
                }
            }
            if (!Arrays.equals(cbcMac1, cbcMac2)) {
                throw new Exception("demo dataCBCMac_KM_RemoteCO_2 run failed!");
            }
            log.info("---示例数据CBCMac完整性校验正确");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        
        return true;
    }

    /**
     * SM4算法加密 和 解密 短数据(不大于4096字节)代码示例
     * 注：
     * 1.加密和解密使用的密钥，通过KMService创建，创建后可以得到密钥的唯一标识keyId，需要应用系统自己保存，加密和解密需要使用相同的keyId
     * <p>
     * 2.加密和解密需要使用相同的: keyId iv 加密模式(CBC) 填充模式(BLOCK_PADDING_PKCS7)
     *
     * @return 结果
     */
    private static boolean sm4ShortDataEncryptAndDecrypt() {
        //加密原文
        byte[] data = ConversionUtils.Data.fromUTF8String("待加密的示例数据");
        try {
            log.info("--------应用服务端 基础密码服务接口调用示例   SM4数据加解密 密钥管理服务 远程密码计算服务--------");

            log.info("---获取密码服务平台对象");
            CryptoServicePlatform platform = getCryptoServicePlatform();


            log.info("---获取密钥管理服务对象，该租户需开通密钥管理服务，并授权给应用使用");
            KMService kmService = getKmService(platform);

            log.info("---在密钥管理服务中生成SM4对称加密密钥，从中获取并保存密钥标识字符串供以后使用");
            //keyId iv需要用户应用保存，加解密要使用相同的keyId  iv
            String keyId = createSecretKeySm4(platform);
            //通过keyId获取密钥
            KMSecretKey kmSecretKey = kmService.getSecretKey(keyId);

            log.info("---获取远程密码计算服务对象，该租户需开通远程密码计算服务，并授权给应用使用");
            COService coService = getCoService(platform);


            log.info("---产生SM4_CBC加解密所需的iv，该数据需与密钥标识字符串一起保存");
            byte[] iv = coService.generateRandom(16);


            log.info("---进行SM4_CBC加密运算，算法填充为PKCS7");
            byte[] encData = coService.encryptShortData(kmSecretKey, COService.ALGORITHM_SM4_CBC, new IVParameterSpec(iv), data);

            log.info("---进行SM4_CBC解密运算，算法填充为PKCS7");
            byte[] decData = coService.decryptShortData(kmSecretKey, COService.ALGORITHM_SM4_CBC, new IVParameterSpec(iv), encData);


            if (!Arrays.equals(data, decData)) {
                throw new Exception("demo SM4_ShortDataEncryptAndDecrypt run failed!");
            }
            log.info("---示例数据加解密正确");


        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * SM4算法 批量加密和解密短数据(每个数据不大于4096字节)代码示例
     * 注：
     * 1.加密和解密使用的密钥，通过KMService创建，创建后可以得到密钥的唯一标识keyId，需要应用系统自己保存，加密和解密需要使用相同的keyId
     * <p>
     * 2.加密和解密需要使用相同的: keyId iv 加密模式(CBC) 填充模式(BLOCK_PADDING_PKCS7)
     *
     * @return 结果
     */
    private static boolean sm4BatchShortDataEncryptAndDecrypt() {
        //加密原文
        byte[][] datas = {Data.fromUTF8String("待加密的示例数据1"),
                Data.fromUTF8String("待加密的示例数据2"),
                Data.fromUTF8String("待加密的示例数据3")};
        try {
            log.info("--------应用服务端 基础密码服务接口调用示例   SM4数据加解密 密钥管理服务 远程密码计算服务--------");

            log.info("---获取密码服务平台对象");
            CryptoServicePlatform platform = getCryptoServicePlatform();


            log.info("---获取密钥管理服务对象，该租户需开通密钥管理服务，并授权给应用使用");
            KMService kmService = getKmService(platform);

            log.info("---在密钥管理服务中生成SM4对称加密密钥，从中获取并保存密钥标识字符串供以后使用");
            //keyId iv需要用户应用保存，加解密要使用相同的keyId  iv
            String keyId = createSecretKeySm4(platform);
            //通过keyId获取密钥
            KMSecretKey kmSecretKey = kmService.getSecretKey(keyId);

            log.info("---获取远程密码计算服务对象，该租户需开通远程密码计算服务，并授权给应用使用");
            COService coService = getCoService(platform);


            log.info("---产生SM4_CBC加解密所需的iv，该数据需与密钥标识字符串一起保存");
            byte[] iv = coService.generateRandom(16);

            byte[][] encDatas = null;
            try (ShortDataEncryptor encryptor = coService.getShortDataEncryptor(kmSecretKey, COService.ALGORITHM_SM4_CBC, new IVParameterSpec(iv))) {
                log.info("---通过短数据加密运算对象对海量短数据进行批量加密");
                encDatas = encryptor.encrypt(datas);
            }

            byte[][] decDatas = null;
            log.info("---通过密码计算服务获取短数据解密运算对象，用于SM4_ECB解密运算，密钥、iv、算法填充必须与加密相同");
            try (ShortDataDecryptor decryptor = coService.getShortDataDecryptor(kmSecretKey, COService.ALGORITHM_SM4_CBC, new IVParameterSpec(iv))) {
                log.info("---通过短数据解密运算对象对海量数据进行批量解密");
                decDatas = decryptor.decrypt(encDatas);
            }

            for (int i = 0; i < datas.length; ++i) {
                if (!Arrays.equals(datas[i], decDatas[i])) {
                    throw new Exception("demo SM4_BatchShortDataEncryptAndDecrypt run failed!");
                }
            }
            log.info("---示例数据加解密正确");


        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * SM4算法加密 和 解密 长数据(大于4096字节)代码示例
     * 注：
     * 1.加密和解密使用的密钥，通过KMService创建，创建后可以得到密钥的唯一标识keyId，需要应用系统自己保存，加密和解密需要使用相同的keyId
     * <p>
     * 2.加密和解密需要使用相同的: keyId iv 加密模式(CBC) 填充模式(BLOCK_PADDING_PKCS7)
     *
     * @return 结果
     */
    private static boolean sm4LongDataEncryptAndDecrypt() {
        //加密原文
        byte[] data = new byte[10 * 1024 * 1024];

        try {
            log.info("--------应用服务端 基础密码服务接口调用示例   SM4数据加解密 密钥管理服务 远程密码计算服务--------");

            log.info("---获取密码服务平台对象");
            CryptoServicePlatform platform = getCryptoServicePlatform();


            log.info("---获取密钥管理服务对象，该租户需开通密钥管理服务，并授权给应用使用");
            KMService kmService = getKmService(platform);

            log.info("---在密钥管理服务中生成SM4对称加密密钥，从中获取并保存密钥标识字符串供以后使用");
            //keyId iv需要用户应用保存，加解密要使用相同的keyId  iv
            String keyId = createSecretKeySm4(platform);
            //通过keyId获取密钥
            KMSecretKey kmSecretKey = kmService.getSecretKey(keyId);

            log.info("---获取远程密码计算服务对象，该租户需开通远程密码计算服务，并授权给应用使用");
            COService coService = getCoService(platform);


            log.info("---产生SM4_CBC加解密所需的iv，该数据需与密钥标识字符串一起保存");
            byte[] iv = coService.generateRandom(16);

            //加密运算部分

            byte[] encData = new byte[0];//密文保存字节数组
            int length = data.length;
            try (Encryptor encryptor = coService.getEncryptor(kmSecretKey, COService.ALGORITHM_SM4_CBC, new IVParameterSpec(iv))) {
                byte[] temp;
                byte[] cipherTemp;
                int offset = 0;
                while (offset + WCSPCO.WCSP_CO_MAX_DATA_LENGTH <= length) {
                    temp = Arrays.copyOfRange(data, offset, offset + WCSPCO.WCSP_CO_MAX_DATA_LENGTH);
                    cipherTemp = encryptor.update(temp);
                    int length1 = encData.length;
                    encData = Arrays.copyOf(encData, length1 + cipherTemp.length);
                    System.arraycopy(cipherTemp, 0, encData, length1, cipherTemp.length);
                    offset += WCSPCO.WCSP_CO_MAX_DATA_LENGTH;
                }
                if (offset < length) {
                    temp = Arrays.copyOfRange(data, offset, length);
                    cipherTemp = encryptor.doFinal(temp);
                } else {
                    cipherTemp = encryptor.doFinal();
                }
                int oldlength = encData.length;
                encData = Arrays.copyOf(encData, oldlength + cipherTemp.length);
                System.arraycopy(cipherTemp, 0, encData, oldlength, cipherTemp.length);
            }

            //解密运算部分

            byte[] decData = new byte[0];//明文保存字节数组
            int length2 = encData.length;
            log.info("---通过密码计算服务获取解密运算对象，用于SM4_CBC解密运算，密钥、iv、算法填充必须与加密相同");
            try (Decryptor decryptor = coService.getDecryptor(kmSecretKey, COService.ALGORITHM_SM4_CBC, new IVParameterSpec(iv))) {
                log.info("---通过解密运算对象对数据进行解密，配合update()可以实现对数据的分段解密");
                byte[] temp;
                byte[] plainTemp;
                int offset = 0;
                while (offset + WCSPCO.WCSP_CO_MAX_DATA_LENGTH <= length2) {
                    temp = Arrays.copyOfRange(encData, offset, offset + WCSPCO.WCSP_CO_MAX_DATA_LENGTH);
                    plainTemp = decryptor.update(temp);
                    int length1 = decData.length;
                    decData = Arrays.copyOf(decData, length1 + plainTemp.length);
                    System.arraycopy(plainTemp, 0, decData, length1, plainTemp.length);
                    offset += WCSPCO.WCSP_CO_MAX_DATA_LENGTH;
                }
                if (offset < length2) {
                    temp = Arrays.copyOfRange(encData, offset, length2);
                    plainTemp = decryptor.doFinal(temp);
                } else {
                    plainTemp = decryptor.doFinal();
                }
                int oldlength = decData.length;
                decData = Arrays.copyOf(decData, oldlength + plainTemp.length);
                System.arraycopy(plainTemp, 0, decData, oldlength, plainTemp.length);
            }


            if (!Arrays.equals(data, decData)) {
                throw new Exception("demo SM4_ShortDataEncryptAndDecrypt run failed!");
            }
            log.info("---示例数据加解密正确");


        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        
        return true;
    }

    /**
     * SM2算法签名验签
     * 1. 可以通过证书或者公钥数据验签
     *
     * @return 结果
     */
    private static boolean sm2ShortDataSignAndVerify() {

        //签名原文
        byte[] data = Data.fromUTF8String("待签名的示例数据");

        try {
            log.info("--------应用服务端 基础密码服务接口调用示例   SM4数据加解密 密钥管理服务 远程密码计算服务--------");

            log.info("---获取密码服务平台对象");
            CryptoServicePlatform platform = getCryptoServicePlatform();

            log.info("---获取远程密码计算服务对象，该租户需开通远程密码计算服务，并授权给应用使用");
            COService coService = getCoService(platform);

            //公钥数据
            byte[] publicKeyData = null;
            //签名值
            byte[] signatureData = null;

            //签名
            log.info("---在密码计算服务中获取SM2非对称签名密钥对");
            try (KeyPair keyPair = coService.getKeyPair(COService.ALGORITHM_SM2, COService.KEY_USAGE_SIGN)) {
                log.info("---从密钥对中获取签名公钥数据并转换为Base64编码的字符串，给第三方用于验签");
                publicKeyData = keyPair.publicKey().getKeyData();
                log.info("---通过密码计算服务进行SM2签名");
                signatureData = coService.signShortData(keyPair.privateKey(), COService.ALGORITHM_SM3_SM2_1, data);

            }

            //验签
            // 获取公钥   公钥也可以从证书里面获取
            //PublicKey publicKey=X509Certificate.getInstance(certData).toPublicKey(); certData:证书数据
            PublicKey publicKey = KeyUtils.createPublicKey(COService.ALGORITHM_SM2, publicKeyData);
            log.info("---通过密码计算服务进行SM2验签");
            if (!coService.verifyShortData(publicKey, COService.ALGORITHM_SM3_SM2_1, data, signatureData)) {
                throw new Exception("demo dataSignSM2_RemoteCO_1 run failed!");
            }
            log.info("---示例数据签名验签正确");

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        
        return true;
    }

    /**
     * MAC算法 支持 SM4_MAC  HMAC_SM3
     *
     * @return 结果
     */
    private static boolean macData() {

        byte[] data1 = Data.fromUTF8String("需要完整性保护的示例数据的第一部分");
        byte[] data2 = Data.fromUTF8String("需要完整性保护的示例数据的第二部分");
        byte[] data3 = Data.fromUTF8String("需要完整性保护的示例数据的第三部分");

        try {
            log.info("--------应用服务端 基础密码服务接口调用示例   SM4数据加解密 密钥管理服务 远程密码计算服务--------");

            log.info("---获取密码服务平台对象");
            CryptoServicePlatform platform = getCryptoServicePlatform();

            log.info("---获取密钥管理服务对象，该租户需开通密钥管理服务，并授权给应用使用");
            KMService kmService = getKmService(platform);

            log.info("---在密钥管理服务中生成SM4对称加密密钥，从中获取并保存密钥标识字符串供以后使用");
            //keyId iv需要用户应用保存，加解密要使用相同的keyId  iv
            String keyId = createSecretKeySm4(platform);
            //通过keyId获取密钥
            KMSecretKey kmSecretKey = kmService.getSecretKey(keyId);

            log.info("---获取远程密码计算服务对象，该租户需开通远程密码计算服务，并授权给应用使用");
            COService coService = getCoService(platform);

            log.info("---产生SM4_CBC加解密所需的iv，该数据需与密钥标识字符串一起保存");
            byte[] iv = coService.generateRandom(16);

            log.info("---初次计算数据的MAC值，并保存");
            String cbcMac1 = null;
            log.info("---通过密码计算服务获取CBCMac运算对象，用于CBCMac运算，算法填充为PKCS7");
            try (Mac mac = coService.getMac(kmSecretKey, COService.ALGORITHM_SM4_MAC, new IVParameterSpec(COService.BLOCK_PADDING_PKCS7, iv))) {
//            try (Mac mac = coService.getMac(kmSecretKey, COService.ALGORITHM_HMAC_SM3)) {
                log.info("---通过CBCMac运算对象对数据进行消息鉴别码运算，配合update()可以实现对数据的分段运算");
                mac.update(data1);
                mac.update(data2);
                mac.update(data3);
                cbcMac1 = Data.toBase64String(mac.doFinal());
            }


            log.info("---以后需要使用数据时再次计算数据MAC值，并与初次计算的MAC值对比");
            String cbcMac2 = null;
            log.info("---通过密码计算服务获取CBCMac运算对象，用于CBCMac运算，算法填充为PKCS7");
            try (Mac mac = coService.getMac(kmSecretKey, COService.ALGORITHM_SM4_MAC, new IVParameterSpec(COService.BLOCK_PADDING_PKCS7, iv))) {
//                try (Mac mac = coService.getMac(kmSecretKey, COService.ALGORITHM_HMAC_SM3)) {
                log.info("---通过CBCMac运算对象对数据进行消息鉴别码运算，配合update()可以实现对数据的分段运算");
                mac.update(data1);
                mac.update(data2);
                mac.update(data3);
                cbcMac2 = Data.toBase64String(mac.doFinal());
            }
            if (!cbcMac1.equals(cbcMac2)) {
                throw new Exception("demo Mac_Data run failed!");
            }
            log.info("---示例数据CBCMac完整性校验正确");

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 文件加解密 代码示例1
     * 1.将明文文件内容读取加密，输出到指定密文文件中
     *
     * @return 结果
     */
    private static boolean fileEncryptStorage1() {

        byte[] data = "待加密的示例数据".getBytes();
        //明文文件路径
        String plainFile = "plainFile";
        //密文文件路径
        String cipherFile = "cipherFile";

        try {
            log.info("--------应用服务端 文件数据安全服务接口调用示例   文件存储加解密--------");

            log.info("---获取密钥生成服务实例");
            CryptoServicePlatform platform = getCryptoServicePlatform();

            log.info("---生成密钥，保存返回的密钥标识字符串供以后使用");
            String keyId = createSecretKeySm4(platform);

            log.info("---获取文件数据安全服务实例，并校验密码计算服务的用户PIN码");
            FileSecurityService fsService = FileSecurityService.getInstance(platform);

            log.info("---准备明文文件");
            try (FileOutputStream fos = new FileOutputStream(plainFile)) {
                fos.write(data);
            }

            log.info("---将明文文件加密为密文文件");
            try (FileInputStream fis = new FileInputStream(plainFile);
                 FileOutputStream fos = new FileOutputStream(cipherFile)) {
                fsService.encrypt(keyId, fis, fos);
            }

            log.info("---将密文文件解密为明文文件");
            try (FileInputStream fis = new FileInputStream(cipherFile);
                 FileOutputStream fos = new FileOutputStream(plainFile)) {
                fsService.decrypt(fis, fos);
            }


            byte[] decData = new byte[data.length];
            try (FileInputStream fis = new FileInputStream(plainFile)) {
                if (decData.length != fis.read(decData) || -1 != fis.read()) {
                    throw new Exception("demo fileEncrypt run failed!");
                }
            }

            if (!Arrays.equals(data, decData)) {
                throw new Exception("demo fileEncrypt_Storage_1 run failed!");
            }

            log.info("---示例文件加解密正确");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        
        return true;
    }

    /**
     * 文件加解密 代码示例1-SDF
     * 1.将明文文件内容读取加密，输出到指定密文文件中
     *
     * @return 结果
     */
    private static boolean fileEncryptStorage1Sdf() {

        byte[] data = "待加密的示例数据".getBytes();
        //明文文件路径
        String plainFile = "plainFile";
        //密文文件路径
        String cipherFile = "cipherFile";

        try {
            log.info("--------应用服务端 文件数据安全服务接口调用示例   文件存储加解密--------");

            log.info("---获取密钥生成服务实例");
            CryptoServicePlatform platform = getCryptoServicePlatformSdf(true);

            log.info("---生成密钥，保存返回的密钥标识字符串供以后使用");
            String keyId = createSecretKeySm4(platform);

            log.info("---获取文件数据安全服务实例，并校验密码计算服务的用户PIN码");
            FileSecurityService fsService = FileSecurityService.getInstance(platform,true);

            log.info("---准备明文文件");
            try (FileOutputStream fos = new FileOutputStream(plainFile)) {
                fos.write(data);
            }

            log.info("---将明文文件加密为密文文件");
            try (FileInputStream fis = new FileInputStream(plainFile);
                 FileOutputStream fos = new FileOutputStream(cipherFile)) {
                fsService.encrypt(keyId, fis, fos);
            }

            log.info("---将密文文件解密为明文文件");
            try (FileInputStream fis = new FileInputStream(cipherFile);
                 FileOutputStream fos = new FileOutputStream(plainFile)) {
                fsService.decrypt(fis, fos);
            }


            byte[] decData = new byte[data.length];
            try (FileInputStream fis = new FileInputStream(plainFile)) {
                if (decData.length != fis.read(decData) || -1 != fis.read()) {
                    throw new Exception("demo fileEncrypt run failed!");
                }
            }

            if (!Arrays.equals(data, decData)) {
                throw new Exception("demo fileEncrypt_Storage_1 run failed!");
            }

            log.info("---示例文件加解密正确");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    /**
     * 文件加解密 代码示例2
     * 1.将文件内容读取加密，输出到内存的字节数组中
     *
     * @return 结果
     */
    private static boolean fileEncryptStorage2() {
        // tenantId: 租户ID，开通密码服务后通过密码服务平台获取
        // appId: 应用ID，开通密码服务后通过密码服务平台获取
        // params: 密码服务平台初始化参数
        // secret: 认证密码，开通密码服务后通过密码服务平台获取

        byte[] data = new byte[4 * 1024 * 1024];
        //明文文件路径
        String plainFile = "plainFile";
        //密文文件路径
        String cipherFile = "cipherFile";

        try {
            log.info("--------应用服务端 文件数据安全服务接口调用示例   文件加密传输 转加密存储--------");
            log.info("---该租户需开通密钥管理服务和密码计算服务，并授权给应用使用");
            log.info("---获取密钥生成服务实例");
            CryptoServicePlatform platform = getCryptoServicePlatform();
            log.info("---生成存储加密密钥，保存返回的密钥标识字符串供以后使用");
            String storageKeyId = createSecretKeySm4(platform);

            log.info("---准备明文文件");
            try (FileOutputStream fos = new FileOutputStream(plainFile)) {
                fos.write(data);
            }


            log.info("---获取文件数据安全服务实例，并校验密码计算服务的用户PIN码");
            FileSecurityService fsService = FileSecurityService.getInstance(platform);

            log.info("---从文件数据安全服务实例中获取加密输入数据流，读取明文文件加密后输出密文数据");
            byte[] encData =  new byte[0];
            try (FileInputStream fis = new FileInputStream(plainFile);
                 FileOutputStream fos = new FileOutputStream(cipherFile);
                 FileSecurityService.EncryptedInputStream efis = fsService.getEncryptedInputStream(storageKeyId, fis)) {
                byte[] temp = new byte[63 * 1024];
                int len = 0;
                while ((len = efis.read(temp)) != -1) {
                    fos.write(temp, 0, len);
                    int length = encData.length;
                    encData = Arrays.copyOf(encData, length + len);
                    System.arraycopy(temp, 0, encData, length, len);
                }
            }

            log.info("---将密文数据通过网络传输");

            byte[] decData = new byte[0];
            try (FileInputStream fis = new FileInputStream(cipherFile);
                 FileSecurityService.DecryptedInputStream dfis = fsService.getDecryptedInputStream(fis)) {
                int len = 0;
                byte[] temp = new byte[63 * 1024];
                while ((len = dfis.read(temp)) != -1) {
                    int length = decData.length;
                    decData = Arrays.copyOf(decData, length + len);
                    System.arraycopy(temp, 0, decData, length, len);
                }
            }

            if (!Arrays.equals(data, decData)) {
                throw new Exception("demo fileEncrypt_Transmission_Storage_1 run failed!");
            }

            log.info("---示例文件加解密正确");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        
        return true;
    }

    /**
     * 文件加解密 代码示例2
     * 1.将文件内容读取加密，输出到内存的字节数组中
     *
     * @return 结果
     */
    private static boolean fileEncryptStorage2Sdf() {
        // tenantId: 租户ID，开通密码服务后通过密码服务平台获取
        // appId: 应用ID，开通密码服务后通过密码服务平台获取
        // params: 密码服务平台初始化参数
        // secret: 认证密码，开通密码服务后通过密码服务平台获取

        byte[] data = new byte[4 * 1024 * 1024];
        //明文文件路径
        String plainFile = "plainFile";
        //密文文件路径
        String cipherFile = "cipherFile";

        try {
            log.info("--------应用服务端 文件数据安全服务接口调用示例   文件加密传输 转加密存储--------");
            log.info("---该租户需开通密钥管理服务和密码计算服务，并授权给应用使用");
            log.info("---获取密钥生成服务实例");
            CryptoServicePlatform platform = getCryptoServicePlatformSdf(true);
            log.info("---生成存储加密密钥，保存返回的密钥标识字符串供以后使用");
            String storageKeyId = createSecretKeySm4(platform);

            log.info("---准备明文文件");
            try (FileOutputStream fos = new FileOutputStream(plainFile)) {
                fos.write(data);
            }


            log.info("---获取文件数据安全服务实例，并校验密码计算服务的用户PIN码");
            FileSecurityService fsService = FileSecurityService.getInstance(platform,true);

            log.info("---从文件数据安全服务实例中获取加密输入数据流，读取明文文件加密后输出密文数据");
            byte[] encData = null;
            try (FileInputStream fis = new FileInputStream(plainFile);
                 FileOutputStream fos = new FileOutputStream(cipherFile);
                 FileSecurityService.EncryptedInputStream efis = fsService.getEncryptedInputStream(storageKeyId, fis)) {
                byte[] temp = new byte[63 * 1024];
                int len = 0;
                while ((len = efis.read(temp)) != -1) {
                    fos.write(temp, 0, len);
                    int length = encData.length;
                    encData = Arrays.copyOf(encData, length + len);
                    System.arraycopy(temp, 0, encData, length, len);
                }
            }

            log.info("---将密文数据通过网络传输");

            byte[] decData = new byte[0];
            try (FileInputStream fis = new FileInputStream(cipherFile);
                 FileSecurityService.DecryptedInputStream dfis = fsService.getDecryptedInputStream(fis)) {
                int len = 0;
                byte[] temp = new byte[63 * 1024];
                while ((len = dfis.read(temp)) != -1) {
                    int length = decData.length;
                    decData = Arrays.copyOf(decData, length + len);
                    System.arraycopy(temp, 0, decData, length, len);
                }
            }

            if (!Arrays.equals(data, decData)) {
                throw new Exception("demo fileEncrypt_Transmission_Storage_1 run failed!");
            }

            log.info("---示例文件加解密正确");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        
        return true;
    }

    /**
     * 双端通信会话加解密代码示例
     * 双端可以是WEB端->服务端  服务端-》服务端
     *
     * @return 结果
     */
    private static boolean sessionEncrypt() {

        String message = "待加密的示例敏感消息";

        try {
            log.info("--------应用服务端 会话消息加解密服务接口调用示例--------");

            log.info("---获取会话发起方密钥协商数据并发送给会话响应方");
            String initiatorKeyAgreementData = getKeyAgreementData();

            log.info("---获取会话消息加解密服务实例");
            CryptoServicePlatform platform = getCryptoServicePlatform();
            SessionEncryptService seService = SessionEncryptService.getInstance(platform);
            log.info("---通过会话消息加解密服务获取会话响应方对象");
            try (SessionEncryptService.SessionResponder responder = seService.getSessionResponder(initiatorKeyAgreementData)) {

                //响应方加密数据
                String encMessage = responder.encryptMessage(message);

                //获取会话响应方密钥协商数据
                String responderKeyAgreementData = responder.getKeyAgreementData();

                //将密文和密钥协商数据发送给会话发起方
                String initiatorMsg = sessionInitiatorEncAndDec(responderKeyAgreementData, encMessage);

                //会话响应方解密获得明文的示例敏感消息
                String decMessage = responder.decryptMessage(initiatorMsg);


                if (!message.equals(decMessage)) {
                    throw new Exception("demo Session_Encrypt run failed!");
                }
                log.info("---示例消息加解密正确");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        
        return true;
    }

    /**
     * 获取会话发起方密钥协商数据
     *
     * @return 结果 密钥协商数据
     */
    private static String getKeyAgreementData() {
        CryptoServicePlatform platform = null;
        try {
            platform = getCryptoServicePlatform();
            SessionEncryptService seService = SessionEncryptService.getInstance(platform);
            try (SessionEncryptService.SessionInitiator initiator = seService.getSessionInitiator()) {
                log.info("---获取会话发起方密钥协商数据");
                String initiatorKeyAgreementData = initiator.getKeyAgreementData();
                return initiatorKeyAgreementData;
            }
        } catch (InvalidParameterException | WCSPException e) {
            e.printStackTrace();
            throw new RuntimeException("获取会话发起方密钥协商数据失败", e);
        }

    }

    /**
     * 获取会话发起方密钥协商数据
     *
     * @param responderKeyAgreementData responderKeyAgreementData
     * @param encMessage                 encMessage
     * @return 结果 密钥协商数据
     */
    private static String sessionInitiatorEncAndDec(String responderKeyAgreementData, String encMessage) {
        CryptoServicePlatform platform = null;
        try {
            platform = getCryptoServicePlatform();
            SessionEncryptService seService = SessionEncryptService.getInstance(platform);
            try (SessionEncryptService.SessionInitiator initiator = seService.getSessionInitiator()) {
                log.info("---初始化会话发起方对象");
                initiator.init(responderKeyAgreementData);
                //解密响应方加密的数据
                String decMessage = initiator.decryptMessage(encMessage);
                String message = decMessage;
                //加密数据
                String encryptMessage = initiator.encryptMessage(message);
                return encryptMessage;
            }
        } catch (InvalidParameterException | WCSPException e) {
            e.printStackTrace();
            throw new RuntimeException("获取会话发起方密钥协商数据失败", e);
        }

    }

    /**
     * 构建P10请求数据
     *
     * @return 结果
     */
    private static String buildPkcs10csRequest() {

        try {
            log.info("--------应用服务端 基础密码服务接口调用示例   SM4数据加解密 远程密码计算服务--------");

            log.info("---密码服务平台初始化");
            CryptoServicePlatform platform = getCryptoServicePlatform();
            log.info("---远程密码计算服务初始化，该租户需开通远程密码计算服务，并授权给应用使用");
            COService coService = getLocalCoService(platform, "123456");
            KeyPair keyPair = coService.getKeyPair(COService.ALGORITHM_SM2, COService.KEY_USAGE_SIGN);
            //构建p10请求对象
            X501DName x501dName = new X501DName.Builder().
                    addAttribute(X501DName.AttrType.C, "CN")
                    .addAttribute(X501DName.AttrType.O, "某机构")
                    .addAttribute(X501DName.AttrType.OU, "某部门")
                    .addAttribute(X501DName.AttrType.OU, "某部门")
                    .addAttribute(X501DName.AttrType.CN, "wumd")
                    .addAttribute(X501DName.AttrType.ST, "某省")
                    .addAttribute(X501DName.AttrType.L, "某街道")
                    .build();
            byte[] bytes = coService.buildPKCS10CSRequest(keyPair, x501dName);
            String encodeToString = Base64.getEncoder().encodeToString(bytes);
            return encodeToString;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("构建p10请求失败");

        }
    }

    /**
     * 级联密钥代码示例
     *
     * @param tenantId tenantId
     * @param appId appId
     * @param params params
     * @param secret secret
     * @return 结果
     */
    public static boolean keyPrivilege(String tenantId, String appId, HashMap<String, Object> params, String secret) {

        try {
            log.info("--------应用服务端 基础密码服务接口调用示例   SM4数据加解密 远程密码计算服务--------");

            log.info("---密码服务平台初始化");
            CryptoServicePlatform platform = CryptoServicePlatform.getInstance();
            platform.init(tenantId, appId, params);

            log.info("---密码服务平台认证，通过认证后应用服务端才能使用远程密码计算服务");
            platform.authorize((new AuthSecretParameterSpec.Builder()).setSecret(secret).build());

            log.info("---远程密码计算服务初始化，该租户需开通远程密码计算服务，并授权给应用使用");
            COService coService = COService.getInstance(COService.SERVICE_TYPE_CO_REMOTE, platform);
            coService.init();

            KMService kmService = KMService.getInstance(platform);
            kmService.init();
            KMSecretKey kmSecretKey = kmService.createSecretKey(KMService.ALGORITHM_SM4, (AlgorithmParameterSpec) null,
                    null, null, (new KeyNoUpdateStrategySpec.Builder()).build());
            String keyId = kmSecretKey.keyId();
            String appid = "ca0a3a3bb58241908845ab6b30228091";

            kmService.grantKeyPrivilege(keyId, appid, null, KMService.KeyPrivilege.ACCESS_UPDATE, 0);

//            kmService.revokeKeyPrivilege(keyId,appid,null, KMService.KeyPrivilege.ACCESS_UPDATE);


            CryptoServicePlatform platform1 = CryptoServicePlatform.getInstance();
            platform1.init("m1usi1c04ib22sum", appid, params);

            log.info("---密码服务平台认证，通过认证后应用服务端才能使用远程密码计算服务");
            platform1.authorize((new AuthSecretParameterSpec.Builder()).setSecret("5261068fe2f34982ad9320e665238ae3").build());
            KMService kmService1 = KMService.getInstance(platform1);
            kmService1.init();
            COService coService1 = COService.getInstance(COService.SERVICE_TYPE_CO_REMOTE, platform1);
            coService1.init();
            byte[] publicKeyData = {0x00, 0x01, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    (byte) 0x8e, (byte) 0x81, (byte) 0xcb, (byte) 0xbe, (byte) 0x90, 0x61, (byte) 0xb9, (byte) 0xc7, 0x48, (byte) 0x82, 0x4b, 0x04, 0x27, 0x08, (byte) 0xd4, (byte) 0x8e,
                    0x3d, 0x3d, 0x57, (byte) 0xbc, 0x70, (byte) 0xb6, 0x78, 0x6a, (byte) 0xcc, 0x24, (byte) 0x8b, 0x0e, (byte) 0xaf, (byte) 0x95, 0x03, 0x0a,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x61, (byte) 0x91, 0x4f, 0x0f, (byte) 0xed, 0x58, (byte) 0xfb, (byte) 0x97, (byte) 0x88, (byte) 0xbb, 0x48, (byte) 0xc4, (byte) 0xd6, (byte) 0xd3, (byte) 0x86, (byte) 0xb3,
                    0x5d, (byte) 0xe6, (byte) 0xaf, 0x54, 0x50, (byte) 0x80, 0x72, 0x0c, (byte) 0xb2, (byte) 0xe3, 0x26, 0x47, 0x4f, (byte) 0x97, (byte) 0xac, 0x0b};
            log.info("---在密码计算服务中生成SM4_CBC对称加密密钥");
            KMSecretKey secretKey = kmService1.getSecretKey(keyId);
            byte[] wrapped = secretKey.getWrapped(KeyUtils.createPublicKey(COService.ALGORITHM_SM2, publicKeyData));

            return true;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static LightDataService getLightDataService() throws InvalidParameterException, WCSPException {
        // 获取密钥生成服务实例
        CryptoServicePlatform platform = getCryptoServicePlatform();
        // 获取keyId
        String keyId = getKeyId();
        String iv = getIV();
        // 获取敏感数据轻量级加解密服务实例
        return LightDataService.getInstance(platform, keyId, iv);
    }

    private static RecordSecurityService getRecordSecurityService() throws InvalidParameterException, WCSPException {
        // 获取密钥生成服务实例
        CryptoServicePlatform platform = getCryptoServicePlatform();
        // 获取keyId
        String keyId = getMacKeyId();
        String iv = getMacIV();
        // 获取敏感数据轻量级加解密服务实例
        return RecordSecurityService.getInstance(platform, keyId, iv);
    }

    /**
     * 获取密码服务平台类对象 主要进行SDK的初始化和认证工作
     *
     * @return 结果 CryptoServicePlatform 注：CryptoServicePlatform对象不能多线程同时使用 不能是单例对象或者全局唯一对象
     */
    private static CryptoServicePlatform getCryptoServicePlatform() throws InvalidParameterException, WCSPException {
        CryptoServicePlatform platform = cryptoServicePlatform.get();
        if(platform == null){
            cryptoServicePlatform.set(getCryptoServicePlatform(true));
        }
        return cryptoServicePlatform.get();
    }

    /**
     * 获取密码服务平台类对象 主要进行SDK的初始化和认证工作
     *
     * @param auth 是否认证 true：认证    false：不认证；  注：使用
     * @return 结果 CryptoServicePlatform
     */
    private static CryptoServicePlatform getCryptoServicePlatform(boolean auth) throws InvalidParameterException, WCSPException {
        log.info("---密码服务平台初始化");
        CryptoServicePlatform platform = CryptoServicePlatform.getInstance();
        String tenantId = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.tenantId");
        String appId = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.appId");
        String url = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.url");
        String workdir = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.workdir");
        HashMap<String, Object> params = new HashMap<>();
        params.put(CryptoServicePlatform.INIT_PARAM_URL, url);
        params.put(CryptoServicePlatform.INIT_PARAM_WORKDIR, workdir);
        platform.init(tenantId, appId, params);
        if (auth) {
            String secret = BeanFactoryHolder.getEnv().getProperty("com.trs.wcspsdk.secret", "x");
            log.info("---密码服务平台认证，通过认证后应用服务端才能使用密钥管理服务和远程密码计算服务");
            platform.authorize((new AuthSecretParameterSpec.Builder()).setSecret(secret).build());
        }
        return platform;
    }

    /**
     * 密码机ip
     */
    private static final String HSM_IP="192.xxx.207.26";

    /**
     * 密码机端口
     */
    private static final int  HSM_PORT=6666;

    private static CryptoServicePlatform getCryptoServicePlatformSdf(boolean auth) throws InvalidParameterException, WCSPException {
        HashMap<String, Object> params1 = new HashMap<>();
        params1.put(CryptoServicePlatform.INIT_PARAM_URL, url);
        params1.put(CryptoServicePlatform.INIT_PARAM_WORKDIR, workdir);
        params1.put(CryptoServicePlatform.INIT_PARAM_HSM_IP, HSM_IP);//密码机ip
        params1.put(CryptoServicePlatform.INIT_PARAM_HSM_PORT, HSM_PORT);//密码机端口
        params1.put(CryptoServicePlatform.INIT_PARAM_COSVC_LOCAL_TYPE, CryptoServicePlatform.COSVC_LOCAL_TYPE_SDF);
        log.info("---密码服务平台初始化");
        CryptoServicePlatform platform = CryptoServicePlatform.getInstance();
        platform.init(tenantId, appId, params1);
        if (auth) {
            log.info("---密码服务平台认证，通过认证后应用服务端才能使用密钥管理服务和远程密码计算服务");
            platform.authorize((new AuthSecretParameterSpec.Builder()).setSecret(secret).build());
        }
        return platform;
    }

    /**
     * 获取KMService，KMService主要负责密钥的创建、获取和保存等
     *
     * @param platform platform
     * @return 结果
     */
    private static KMService getKmService(CryptoServicePlatform platform) {
        KMService kmService = KMService.getInstance(platform);
        kmService.init();
        return kmService;
    }

    private static String createSecretKeySm4(CryptoServicePlatform platform) {
        KMService kmService = KMService.getInstance(platform);
        kmService.init();
        try (KMSecretKey kmServiceSecretKey = kmService.createSecretKey(KMService.ALGORITHM_SM4, (AlgorithmParameterSpec) null, null, null, (new KeyNoUpdateStrategySpec.Builder()).build());
        ) {
            String keyId = kmServiceSecretKey.keyId();
            return keyId;
        } catch (InvalidParameterException | WCSPException e) {
            e.printStackTrace();
            throw new RuntimeException("创建密钥失败", e);
        }
    }

    /**
     * 获取COService对象，COService对象的方法主要包括：加解密 签名验签 MAC运算 文件加解密
     *
     * @param platform platform
     * @return 结果
     */
    private static COService getCoService(CryptoServicePlatform platform) throws InvalidParameterException {
        COService coService = COService.getInstance(COService.SERVICE_TYPE_CO_REMOTE, platform);
        coService.init();
        return coService;

    }

    /**
     * 获取本地COService对象
     *
     * @param platform platform
     * @param pin      PIN码
     * @return 结果
     */
    private static COService getLocalCoService(CryptoServicePlatform platform, String pin) throws InvalidParameterException, WCSPException {
        COService coService = COService.getInstance(COService.SERVICE_TYPE_CO_LOCAL, platform);
        coService.init();
        coService.verifyPIN(pin);
        return coService;
    }
}