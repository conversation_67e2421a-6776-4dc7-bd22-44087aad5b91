package com.trs.police.common.core.entity;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 当前用户信息
 *
 * <AUTHOR>
 * @date 2022/04/17
 */
@Data
public class CurrentUser implements Serializable {

    private static final long serialVersionUID = -4352868070794165001L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号码
     */
    private String idNumber;

    /**
     * 用户级别(派出所，区县，市级，省级)
     */
    private UserLevel userLevel;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 性别
     */
    private String gender;

    /**
     * 办公电话
     */
    private String telephone;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 警号
     */
    private String policeCode;

    /**
     * 账号有效期限,默认三年
     */
    private LocalDateTime validDate;

    /**
     * 账号状态
     */
    private Integer status;

    /**
     * 个人签章图片id
     */
    private Long signature;

    /**
     * 个人签章图片
     */
    private String signatureImg;

    /**
     * 组织签章图片
     */
    private String deptSignatureImg;

    /**
     * 个人头像图片位置
     */
    private String avatar;

    /**
     * 当前登录部门
     */
    private DeptDto dept;

    /**
     * 功能模块权限
     */
    private Set<String> modules;

    /**
     * 操作 权限
     */
    private Set<String> operations;

    /**
     * 角色
     */
    private Set<Long> roleIds;
    /**
     * 职务
     */
    private String duty;

    /**
     * 身份证
     */
    private String zjhm;

    /**
     * mac
     */
    private String mac;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 获取用户登录部门id
     *
     * @return 部门id
     */
    public Long getDeptId() {
        return dept.getId();
    }

    /**
     * 获取用户登录部门id
     *
     * @return 部门id
     */
    public String getDeptCode() {
        return dept.getCode();
    }
    /**
     * 生成userDeptVO
     *
     * @return {@link UserDeptVO}
     */
    public UserDeptVO toUserDeptVO() {
        return new UserDeptVO(this.id, this.dept.getId());
    }

    /**
     * 根据dto创建用户信息（不包括部门）
     *
     * @param dto 用户dto
     * @return 用户基本信息
     */
    public static CurrentUser of(UserDto dto){
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(dto.getId());
        currentUser.setRealName(dto.getRealName());
        currentUser.setIdNumber(dto.getIdNumber());
        currentUser.setGender(dto.getGender());
        currentUser.setMobile(dto.getTel());
        currentUser.setEmail(dto.getEmail());
        currentUser.setPoliceCode(dto.getPoliceCode());
        currentUser.setValidDate(dto.getValidDate());
        currentUser.setSignature(dto.getSignature());
        currentUser.setAvatar(dto.getAvatar());
        currentUser.setDuty(dto.getDuty());
        currentUser.setStatus(dto.getStatus());
        return currentUser;
    }

    /**
     * 判断是否是自己
     *
     * @param user 用户
     * @return 是否是自己
     */
    public Boolean isSelf(SimpleUserVO user) {
        return this.getId().equals(user.getUserId()) && this.getDeptId().equals(user.getDeptId());
    }


    /**
     * 判断是否是自己
     *
     * @param user 用户
     * @return 是否是自己
     */
    public Boolean isSelf(CurrentUser user) {
        return this.getId().equals(user.getId()) && this.getDeptId().equals(user.getDeptId());
    }



    /**
     * 用户级别
     *
     * <AUTHOR>
     */
    @Data
    public static class UserLevel{

        private Integer code;

        private String name;
    }
}
