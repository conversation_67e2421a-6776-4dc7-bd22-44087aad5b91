package com.trs.police.common.core.vo;

import com.trs.db.sdk.meta.data.MetaDataField;
import com.trs.db.sdk.meta.data.MetaDataTable;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据库表及字段信息对象
 *
 * <AUTHOR>
 * @since 2021/3/30 11:45
 */
@Getter
@Setter
public class TableVO extends BaseVO {

    private static final long serialVersionUID = -3665804199014368530L;

    // 数据库名称
    private String tableName;

    // 表别名
    private String aliaseName;

    // 表描述
    private String tableComment;

    private List<TableFieldVO> fieldList = new ArrayList<>();

    public TableVO() {
    }

    public TableVO(MetaDataTable metaDataTable) {
        this.setTableName(metaDataTable.getTableName());
        this.setAliaseName(metaDataTable.getAliaseName());
        this.setTableComment(metaDataTable.getTableComment());
        List<MetaDataField> fields = metaDataTable.getFields();
        if (fields != null) {
            List<TableFieldVO> tableField = fields.stream().map(f -> {
                TableFieldVO tableFieldVO = new TableFieldVO();
                tableFieldVO.setName(f.getFieldName());
                tableFieldVO.setType(f.getFieldType() == null ? "未知类型" : f.getFieldType().getValue());
                tableFieldVO.setOriginalType(f.getOriginalFieldType());
                tableFieldVO.setDescription(f.getDescription());
                tableFieldVO.setDefaultValue(f.getDefaultValue());
                return tableFieldVO;
            }).collect(Collectors.toList());
            this.setFieldList(tableField);
        }
    }
}
