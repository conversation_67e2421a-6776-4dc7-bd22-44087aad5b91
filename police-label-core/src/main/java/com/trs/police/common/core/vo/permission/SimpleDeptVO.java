package com.trs.police.common.core.vo.permission;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 部门vo
 *
 * <AUTHOR>
 * @date 2023/02/07
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class SimpleDeptVO implements Serializable {

    private static final long serialVersionUID = -326214278362528761L;
    /**
     * 部门id
     */
    @NotNull(message = "责任单位id不能为空！")
    private Long deptId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 部门名称
     */
    private Long deptType;

    /**
     * 被@的单位的类型:
     * 主责单位-responsibleDept
     * 处置单位-handlerDept
     * 抄送单位-copyDept
     */
    private String atDeptType;

    /**
     * 区域代码
     */
    private String districtCode;

    private String districtName;

    /**
     * 区域简称
     */
    private String districtShortName;

    /**
     * 区域主要名称
     */
    private String districtMainName;

    /**
     * 简单的部门类型 只有 省、市、区县、派出所 四个类型
     */
    private Integer deptSimpleType;

    public SimpleDeptVO(Long deptId, String deptCode, String deptName, String shortName, Long deptType, String atDeptType, String districtCode, String districtName, String districtShortName, String districtMainName) {
        this.deptId = deptId;
        this.deptCode = deptCode;
        this.deptName = deptName;
        this.shortName = shortName;
        this.deptType = deptType;
        this.atDeptType = atDeptType;
        this.districtCode = districtCode;
        this.districtName = districtName;
        this.districtShortName = districtShortName;
        this.districtMainName = districtMainName;
    }
}
