package com.trs.police.common.core.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yanghy
 * @date : 2022/8/22 14:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeyValueTypeVO implements Serializable {

    private static final long serialVersionUID = -3418488139615533151L;
    private String key;
    private Object value;
    private String type;

    private static final String TIME_PARAMS = "timeParams";
    private static final String ARRAY = "array";
    private static final String CASE_LABEL = "caseLabel";
    private static final String LONG_ARRAY = "longArray";

    /**
     * 只有key-value时
     *
     * @param key   键
     * @param value 值
     */
    public KeyValueTypeVO(String key, Object value) {
        this.key = key;
        this.value = value;
    }

    /**
     * 获取处理后的值
     *
     * @return {@link Object}
     */
    public Object getValue() {
        if (TIME_PARAMS.equals(type)) {
            return JsonUtil.parseObject(JsonUtil.toJsonString(value), TimeParams.class);
        }
        return value;
    }

    /**
     * 获取处理后的值
     *
     * @return {@link Object}
     */
    @JsonIgnore
    public Object getProcessedValue() {
        if (TIME_PARAMS.equals(type)) {
            return JsonUtil.parseObject(JsonUtil.toJsonString(value), TimeParams.class);
            //TODO array类型名称修改为tree；统一所有树状多选列表的查询
        } else if (ARRAY.equals(type) || CASE_LABEL.equals(type)) {
            return nestingListSimplification(value);
        } else if (LONG_ARRAY.equals(type)) {
            return JsonUtil.objectToArray(value, Long.class);
        }
        return value;
    }

    /**
     * 嵌套label list整理为单层列表
     *
     * @param value 数据
     * @return 结果
     */
    public static List<Object> nestingListSimplification(Object value) {
        if (Objects.isNull(value)) {
            return Collections.emptyList();
        }
        // 兼容一层数组
        String json = JsonUtil.toJsonString(value);
        if(!json.startsWith("[[") && !json.startsWith("[\n[")){
            List<Object> objects = JSONObject.parseArray(json, Object.class);
            return objects==null?Collections.emptyList():objects;
        }
        List<List<Object>> treeIds = JsonUtil.toNestingList(json, Object.class);
        if (treeIds.isEmpty()) {
            return Collections.emptyList();
        } else {
            return treeIds.stream().map(list -> {
                if (list.isEmpty()) {
                    return null;
                }
                return list.get(list.size() - 1);
            }).filter(Objects::nonNull).collect(Collectors.toList());
        }
    }


    /**
     * 嵌套label list平铺
     *
     * @param value 数据
     * @return 结果
     */
    public static List<Object> tiledList(Object value) {
        if (Objects.isNull(value)) {
            return Collections.emptyList();
        }
        List<List<Long>> treeIds = JsonUtil.toNestingList(JsonUtil.toJsonString(value), Long.class);
        if (treeIds.isEmpty()) {
            return Collections.emptyList();
        } else {
            return treeIds.stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        }
    }

    /**
     * 从filterParams获取指定key的value
     *
     * @param filterParams 筛选参数
     * @param key          字段名
     * @param clazz        类型
     * @param <T>          类型
     * @return 结果
     */
    public static <T> T getSingleFilterParam(List<KeyValueTypeVO> filterParams, String key, Class<T> clazz) {
        for (KeyValueTypeVO vo : filterParams) {
            if (vo.getKey().equals(key) && vo.getValue() != null) {
                return JsonUtil.parseSpecificObject(vo.getValue(), clazz);
            }
        }
        return null;
    }

    /**
     * 从filterParams获取指定key-value
     *
     * @param filterParams 筛选参数
     * @param key          key
     * @return {@link KeyValueTypeVO}
     */
    public static KeyValueTypeVO get(List<KeyValueTypeVO> filterParams, String key) {
        if (key == null) {
            return null;
        }
        for (KeyValueTypeVO filterParam : filterParams) {
            if (key.equals(filterParam.getKey())) {
                return filterParam;
            }
        }
        return null;
    }

}
