package com.trs.police.common.core.utils;

import com.trs.police.common.core.constant.ExceptionMessageConstant;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2022/02/15
 */
public class AuthHelper {

    private AuthHelper() {
    }

    /**
     * 获取当前线程用户
     *
     * @return 当前登录用户
     */
    public static CurrentUser getCurrentUser() {
        OAuth2Authentication authentication =
            (OAuth2Authentication) SecurityContextHolder.getContext().getAuthentication();
        try {
            return JsonUtil.OBJECT_MAPPER.convertValue(authentication.getUserAuthentication().getDetails(),
                CurrentUser.class);
        } catch (Exception ignore) {
            return null;
        }

    }

    /**
     * 获取当前线程用户(只包含用户重要信息)
     *
     * @return 当前登录用户
     */
    public static CurrentUser getSimpleCurrentUser() {
        OAuth2Authentication authentication =
            (OAuth2Authentication) SecurityContextHolder.getContext().getAuthentication();
        try {
            CurrentUser currentUser = JsonUtil.OBJECT_MAPPER.convertValue(
                authentication.getUserAuthentication().getDetails(),
                CurrentUser.class);
            CurrentUser simpleUser = new CurrentUser();
            simpleUser.setId(currentUser.getId());
            simpleUser.setRealName(currentUser.getRealName());
            simpleUser.setIdNumber(currentUser.getIdNumber());
            simpleUser.setDept(currentUser.getDept());
            return simpleUser;
        } catch (Exception ignore) {
            return null;
        }

    }

    /**
     * 获取当前线程用户
     *
     * @return 当前登录用户
     */
    public static CurrentUser getNotNullUser() {
        CurrentUser currentUser = getCurrentUser();
        if (currentUser != null) {
            return currentUser;
        } else {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
    }

    /**
     * 获取当前线程用户(SimpleUserVO)
     *
     * @return 当前登录用户 SimpleUserVO
     */
    public static SimpleUserVO getNotNullSimpleUser() {
        CurrentUser currentUser = getCurrentUser();
        if (currentUser != null) {
            return new SimpleUserVO(currentUser);
        } else {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
    }

    /**
     * 获得当前线程用户（SimpleUserVO）,如果没有获得，将按照指定逻辑返回一个。如果指定逻辑返回的用户为null或者异常，将抛出异常
     *
     * @param defaultUserIfNull 默认的用户信息创建
     *
     * @return 返回用户信息
     */
    public static SimpleUserVO getNotNullSimpleUser(Supplier<SimpleUserVO> defaultUserIfNull) {
        CurrentUser currentUser = getCurrentUser();
        if (currentUser != null) {
            return new SimpleUserVO(currentUser);
        } else {
            try{
                SimpleUserVO defaultUser = defaultUserIfNull.get();
                if(defaultUser == null) {
                    throw new TRSException(String.format("无法获得当前用户，哪怕初始一个默认的。 %s",ExceptionMessageConstant.CANT_FIND_CURRENT_USER));
                }
                return defaultUser;
            }catch (Exception ex) {
                throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER, ex);
            }
        }
    }
}
