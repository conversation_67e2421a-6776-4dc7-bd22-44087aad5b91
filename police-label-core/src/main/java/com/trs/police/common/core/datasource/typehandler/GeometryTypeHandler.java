package com.trs.police.common.core.datasource.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;

import java.sql.*;

/**
 * @ClassName GeometryTypeHandler
 * @Description 地理位置转换处置类
 * <AUTHOR>
 * @Date 2024/12/27 20:47
 **/
@MappedJdbcTypes(JdbcType.OTHER)
@MappedTypes(String.class)
public class GeometryTypeHandler extends BaseTypeHandler<String> {

    private final WKTReader wktReader = new WKTReader();
    private final WKTWriter wktWriter = new WKTWriter();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            // 验证WKT格式
            Geometry geometry = wktReader.read(parameter);
            String standardWkt = wktWriter.write(geometry);

            // 使用MySQL特定的JDBC方法，指定SRID为4326
            Statement stmt = ps.getConnection().createStatement();
            ResultSet rs = stmt.executeQuery("SELECT ST_GeomFromText('" + standardWkt + "') as geom");
            if (rs.next()) {
                byte[] geometryBytes = rs.getBytes("geom");
                ps.setBytes(i, geometryBytes);
            }
            rs.close();
            stmt.close();
        } catch (ParseException e) {
            throw new SQLException("Invalid WKT format: " + parameter, e);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        byte[] bytes = rs.getBytes(columnName);
        if (bytes == null) {
            return null;
        }

        PreparedStatement pstmt = rs.getStatement().getConnection()
                .prepareStatement("SELECT IF(ST_SRID(?) = 0, ST_AsText(?), ST_AsText(ST_SwapXY(?))) as wkt");
        pstmt.setBytes(1, bytes);
        pstmt.setBytes(2, bytes);
        pstmt.setBytes(3, bytes);
        ResultSet wktRs = pstmt.executeQuery();

        String wkt = null;
        if (wktRs.next()) {
            wkt = wktRs.getString("wkt");
        }
        wktRs.close();
        pstmt.close();
        return wkt;
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        byte[] bytes = rs.getBytes(columnIndex);
        if (bytes == null) {
            return null;
        }

        PreparedStatement pstmt = rs.getStatement().getConnection()
                .prepareStatement("SELECT IF(ST_SRID(?) = 0, ST_AsText(?), ST_AsText(ST_SwapXY(?))) as wkt");
        pstmt.setBytes(1, bytes);
        pstmt.setBytes(2, bytes);
        pstmt.setBytes(3, bytes);
        ResultSet wktRs = pstmt.executeQuery();

        String wkt = null;
        if (wktRs.next()) {
            wkt = wktRs.getString("wkt");
        }
        wktRs.close();
        pstmt.close();
        return wkt;
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        byte[] bytes = cs.getBytes(columnIndex);
        if (bytes == null) {
            return null;
        }

        PreparedStatement pstmt = cs.getConnection()
                .prepareStatement("SELECT IF(ST_SRID(?) = 0, ST_AsText(?), ST_AsText(ST_SwapXY(?))) as wkt");
        pstmt.setBytes(1, bytes);
        pstmt.setBytes(2, bytes);
        pstmt.setBytes(3, bytes);
        ResultSet wktRs = pstmt.executeQuery();

        String wkt = null;
        if (wktRs.next()) {
            wkt = wktRs.getString("wkt");
        }
        wktRs.close();
        pstmt.close();
        return wkt;
    }
}
