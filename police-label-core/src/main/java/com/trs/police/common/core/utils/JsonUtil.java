package com.trs.police.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.trs.police.common.core.json.deserializer.UtcToDateDeserializer;
import com.trs.police.common.core.json.deserializer.UtcToDateTimeDeserializer;
import com.trs.police.common.core.json.serializer.DateTimeToUtcSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Json工具类基于jackson实现
 *
 * <AUTHOR>
 * @date 2020/11/9
 **/
@Slf4j
public class JsonUtil {


    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private JsonUtil() {
    }

    static {
        SimpleModule module = new SimpleModule();
        module.addSerializer(LocalDateTime.class, new DateTimeToUtcSerializer());
        module.addSerializer(LocalDate.class, new DateTimeToUtcSerializer());
        module.addDeserializer(LocalDateTime.class, new UtcToDateTimeDeserializer());
        module.addDeserializer(LocalDate.class, new UtcToDateDeserializer());

        OBJECT_MAPPER.registerModule(module)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 读json文件
     *
     * @param filePath 文件路径
     * @return json
     * @throws IOException IO异常
     */
    public static JsonNode readJsonFile(String filePath) throws IOException {
        return OBJECT_MAPPER.readTree(new File(filePath));
    }

    /**
     * json字符串转实体类
     *
     * @param jsonText json文本
     * @param clazz    实体类型
     * @param <T>      泛型参数
     * @return 转出来的实体类实例，转换失败返回null
     */
    public static <T> T parseObject(String jsonText, Class<T> clazz) {
        if (StringUtils.isBlank(jsonText)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonText, clazz);
        } catch (Exception e) {
            log.error("json string to object error: ", e);
            return null;
        }
    }

    /**
     * object类型转实体类
     *
     * @param o        object类型
     * @param clazz    实体类型
     * @param <T>      泛型参数
     * @return 转出来的实体类实例，转换失败返回null
     */
    public static <T> T parseSpecificObject(Object o, Class<T> clazz) {
        if (o == null) {
            return null;
        }

        String jsonText;
        if (o instanceof String) {
            jsonText = o.toString();
        } else {
            jsonText = toJsonString(o);
        }

        if (StringUtils.isBlank(jsonText)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonText, clazz);
        } catch (Exception e) {
            log.error("json string to object error: ", e);
            return null;
        }
    }

    /**
     * json字符串转json对象
     *
     * @param jsonText json文本
     * @return json对象，转换失败返回null
     */
    public static JsonNode parseJsonNode(String jsonText) {
        if (StringUtils.isBlank(jsonText)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readTree(jsonText);
        } catch (Exception e) {
            log.error("json string to json node error: ", e);
            return null;
        }
    }

    /**
     * 将Object转换为json
     *
     * @param object 需要序列化的变量
     * @return Json
     */
    public static JsonNode objectToJsonNode(Object object) {
        if (object instanceof byte[]) {
            return parseJsonNode(new String((byte[]) object));
        } else if (object instanceof String) {
          return parseJsonNode((String) object);
        } else {
            return parseJsonNode(toJsonString(object));
        }
    }

    /**
     * 将Object转换为List
     *
     * @param <T>    范型
     * @param object 需要序列化的变量
     * @param clazz  类型参数
     * @return Json
     */
    public static <T> List<T> objectToArray(Object object, Class<T> clazz) {
        if (object instanceof byte[]) {
            return parseArray(new String((byte[]) object), clazz);
        } else if (object instanceof String) {
            return parseArray((String) object, clazz);
        } else {
            return parseArray(toJsonString(object), clazz);
        }
    }

    /**
     * json字符串转实体类列表
     *
     * @param jsonText json文本
     * @param clazz    实体类型
     * @param <T>      泛型参数
     * @return 转换出来的列表，转换失败返回emptyList
     */
    public static <T> List<T> parseArray(String jsonText, Class<T> clazz) {
        if (StringUtils.isBlank(jsonText)) {
            return Collections.emptyList();
        }
        CollectionType collectionType = TypeFactory.defaultInstance().constructCollectionType(List.class, clazz);
        try {
            return OBJECT_MAPPER.readValue(jsonText, collectionType);
        } catch (Exception e) {
            log.error("json string to array error: ", e);
            return Collections.emptyList();
        }
    }

    /**
     * json字符串转map
     *
     * @param jsonText json文本
     * @param clazz    值对象的类型
     * @param <T>      泛型参数
     * @return 转换出来的map，转换失败返回emptyMap
     */
    public static <T> Map<String, T> parseMap(String jsonText, Class<T> clazz) {
        if (StringUtils.isEmpty(jsonText)) {
            return Collections.emptyMap();
        }
        MapType mapType = TypeFactory.defaultInstance().constructMapType(HashMap.class, String.class, clazz);
        try {
            return OBJECT_MAPPER.readValue(jsonText, mapType);
        } catch (Exception e) {
            log.error("json string to map error: ", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 实体类转json字符串
     *
     * @param object 实体类对象
     * @return 转换出来的json字符串，转换失败返回emptyString
     */
    public static String toJsonString(Object object) {
        try {
            return OBJECT_MAPPER.writer().writeValueAsString(object);
        } catch (Exception e) {
            log.error("object to json string error: ", e);
            return "";
        }
    }

    /**
     * 实体类转json字符串
     *
     * @param object 实体类对象
     * @return 转换出来的json字符串，转换失败返回emptyString
     */
    public static String toPrettyJsonString(Object object) {
        try {
            JsonNode jsonNode = JsonUtil.parseJsonNode(toJsonString(object));
            if (Objects.isNull(jsonNode)){
                return "";
            }
            return jsonNode.toPrettyString();
        } catch (Exception e) {
            log.error("object to pretty json string error: ", e);
            return "";
        }
    }

    /**
     * 实体类转json字符数组
     *
     * @param object 实体类对象
     * @return 转换出来的json字符数组，转换失败返回emptyArray
     */
    public static byte[] toJsonBytes(Object object) {
        try {
            return OBJECT_MAPPER.writer().writeValueAsBytes(object);
        } catch (IOException e) {
            log.error("object to json bytes error: ", e);
            return new byte[]{};
        }
    }


    /**
     * 将object转换为list
     *
     * @param obj   {@link Object}
     * @param clazz list的类型
     * @param <T>   类型
     * @return list
     */
    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return Collections.emptyList();
    }

    /**
     * 处理嵌套list
     *
     * @param listStr 字符串
     * @param clazz   最内层类型
     * @param <T>     类型
     * @return 列表
     */
    public static <T> List<List<T>> toNestingList(String listStr, Class<T> clazz) {
        List<List<T>> result = new ArrayList<>();
        List<Object> objectList = parseArray(listStr, Object.class);
        for (Object listObject : objectList) {
            if (listObject instanceof List<?>) {
                List<T> innerList = parseArray(toJsonString(listObject), clazz);
                result.add(innerList);
            }
        }
        return result;
    }

    /**
     * map to jsonNode
     *
     * @param map Map
     * @return JsonNode
     */
    public static JsonNode mapToJsonNode(Map<String, Object> map) {
        return OBJECT_MAPPER.valueToTree(map);
    }

    /**
     * list to jsonNode
     *
     * @param list List
     * @param <T> 类型
     * @return JsonNode
     */
    public static <T> JsonNode listToJsonNode(List<T> list) {
        return OBJECT_MAPPER.valueToTree(list);
    }

    /**
     * jsonNode转换List
     *
     * @param <T> 类型
     * @param jsonNode JsonNode
     * @param clazz    列表类型
     * @return 列表
     */
    public static <T> List<T> jsonNodeToList(JsonNode jsonNode, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readerForListOf(clazz).readValue(jsonNode);
        } catch (IOException e) {
            log.error("jsonNode to list error! ", e);
        }
        return Collections.emptyList();
    }

    /**
     * object/string转换为指定类型
     *
     * @param object 实体类
     * @param clazz 类型
     * @param <T> 类型
     * @return 结果
     */
    public static <T> T getObject(Object object, Class<T> clazz) {
        try {
            if (object.getClass() == clazz) {
                return (T) object;
            } else {
                return parseObject(toJsonString(object), clazz);
            }
        } catch (Exception e) {
            log.error("类型转换出错！", e);
        }
        return null;
    }

    /**
     * 判断文本是否是json字符串
     *
     * @param text text
     * @return boolean
     */
    public static boolean isValidJson(String text) {
        // 如果是 null 或空字符串，返回 false
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        try {
            // 解析为对象
            Object json = JSON.parse(text);
            // 检查解析后的结果是否为 JSONObject 或 JSONArray
            return json instanceof JSONObject || json instanceof JSONArray;
        } catch (JSONException e) {
            return false;
        }
    }
}
