package com.trs.police.common.core.datasource.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * mybatis 审计 填充数据的创建以及更新时间用户信息
 *
 * <AUTHOR>
 */
@Slf4j
public class MybatisMetaObjectHandler implements MetaObjectHandler {

    /**
     * 创建时间
     */
    private static final String FIELD_CREATE_TIME = "createTime";

    /**
     * 创建用户主键
     */
    private static final String FIELD_CREATE_USER_ID = "createUserId";

    /**
     * 创建单位主键
     */
    private static final String FIELD_CREATE_DEPT_ID = "createDeptId";

    /**
     * 更新时间
     */
    private static final String FIELD_UPDATE_TIME = "updateTime";

    /**
     * 更新用户主键
     */
    private static final String FIELD_UPDATE_USER_ID = "updateUserId";

    /**
     * 更新单位主键
     */
    private static final String FIELD_UPDATE_DEPT_ID = "updateDeptId";


    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.isNull(this.getFieldValByName(FIELD_UPDATE_TIME, metaObject))) {
            this.strictInsertFill(metaObject, FIELD_UPDATE_TIME, LocalDateTime.class, LocalDateTime.now());
        }
        if (Objects.isNull(this.getFieldValByName(FIELD_CREATE_TIME, metaObject))) {
            this.strictInsertFill(metaObject, FIELD_CREATE_TIME, LocalDateTime.class, LocalDateTime.now());
        }
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            return;
        }
        this.strictInsertFill(metaObject, FIELD_CREATE_USER_ID, Long.class, currentUser.getId());
        this.strictInsertFill(metaObject, FIELD_CREATE_DEPT_ID, Long.class, currentUser.getDept().getId());
        this.strictInsertFill(metaObject, FIELD_UPDATE_USER_ID, Long.class, currentUser.getId());
        this.strictInsertFill(metaObject, FIELD_UPDATE_DEPT_ID, Long.class, currentUser.getDept().getId());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName(FIELD_UPDATE_TIME, LocalDateTime.now(), metaObject);
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            return;
        }
        this.setFieldValByName(FIELD_UPDATE_USER_ID, currentUser.getId(), metaObject);
        this.setFieldValByName(FIELD_UPDATE_DEPT_ID, currentUser.getDept().getId(), metaObject);
    }
}
