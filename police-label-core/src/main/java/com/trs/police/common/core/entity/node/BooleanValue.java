package com.trs.police.common.core.entity.node;


/**
 * 布尔
 *
 * <AUTHOR>
 */
public class BooleanValue implements Value {

    private String value;

    public BooleanValue(String value) {
        this.value = value;
    }

    @Override
    public String getValueString() {
        return value;
    }

    @Override
    public Boolean isNull() {
        return value  == null || value.isEmpty();
    }

    @Override
    public Boolean isEmpty() {
        return false;
    }
}
