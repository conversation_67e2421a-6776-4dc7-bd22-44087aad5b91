package com.trs.police.common.core.vo.node;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.DataBaseFieldMappingType;
import com.trs.police.common.core.constant.ValueType;
import com.trs.police.common.core.entity.node.comparable.ComparableValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 行
 */
@Data
@NoArgsConstructor
public class Row {

    private List<FieldValue> rowData;

    public Row(List<FieldValue> rowData) {
        this.rowData = rowData;
    }

    /**
     * 获取字段值
     *
     * @param fieldInfoVO 字段信息
     * @return 字段值
     */
    public Optional<FieldValue> getValue(FieldInfoVO fieldInfoVO) {
        return rowData
                .stream()
                .filter(v -> v.getId().equals(fieldInfoVO.getId()))
                .findAny();
    }

    /**
     * 获取两个字段的比较器
     *
     * @param thisField 字段
     * @param otherField 字段
     * @return 比较器
     */
    public static Comparator<Row> getComparable(FieldInfoVO thisField, FieldInfoVO otherField) {
        return (r1,r2) -> {
            DataBaseFieldMappingType type = DataBaseFieldMappingType.getType(thisField.getTypeCode());
            String other = r2.getValue(otherField)
                    .map(FieldValue::getValue)
                    .orElse("");
            String thisValue = r1.getValue(thisField)
                    .map(FieldValue::getValue)
                    .orElse("");
            if (StringUtils.isEmpty(other)) {
                return 1;
            }
            if (StringUtils.isEmpty(thisValue)) {
                return -1;
            }
            switch (type) {
                case NUMBER:
                    return ((ComparableValue) new ValueWrapper(ValueType.NUMBER, new String[]{thisValue}).getTargetValue(0)).compareTo(other);
                case DATETIME:
                    return ((ComparableValue) new ValueWrapper(ValueType.TIME_ENTERED, new String[]{thisValue}).getTargetValue(0)).compareTo(other);
              default:
                  throw new RuntimeException("不支持的列类型");

          }
        };
    }
}
