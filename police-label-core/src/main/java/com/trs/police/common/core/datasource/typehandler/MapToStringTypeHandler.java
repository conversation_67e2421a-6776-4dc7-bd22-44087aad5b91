package com.trs.police.common.core.datasource.typehandler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/13
 */
public class MapToStringTypeHandler extends BaseTypeHandler<Map<String, Object>> {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final MapType MAP_TYPE = TypeFactory.defaultInstance()
        .constructMapType(HashMap.class, String.class, Object.class);

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Map<String, Object> stringObjectMap,
        JdbcType jdbcType) throws SQLException {
        try {
            preparedStatement.setString(i, OBJECT_MAPPER.writeValueAsString(stringObjectMap));
        } catch (JsonProcessingException e) {
            throw new SQLException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        try {
            return OBJECT_MAPPER.readValue(resultSet.getString(s), MAP_TYPE);
        } catch (JsonProcessingException e) {
            throw new SQLException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        try {
            return OBJECT_MAPPER.readValue(resultSet.getString(i), MAP_TYPE);
        } catch (JsonProcessingException e) {
            throw new SQLException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        try {
            return OBJECT_MAPPER.readValue(callableStatement.getString(i), MAP_TYPE);
        } catch (JsonProcessingException e) {
            throw new SQLException(e.getMessage());
        }
    }
}
