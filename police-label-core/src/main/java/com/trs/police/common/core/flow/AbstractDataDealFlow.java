package com.trs.police.common.core.flow;

import com.trs.police.common.core.process.DataProcessCenter;
import com.trs.police.common.core.process.IProcessBehavior;
import io.vavr.Predicates;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 数据处理流程抽象类
 *
 * @param <T> 输入参数
 * @param <R> r
 * @param <TARGET> 输出对象
 *
 * @author:wen.wen
 * @create 2022-05-20 09:38
 **/
public abstract class AbstractDataDealFlow<T, R, TARGET> implements IDataDealFlow<T, R> {

    private final List<Predicate<T>> predicateList = new ArrayList<>();

    /**
     * 目标对象的操作流程
     */
    private final List<IProcessBehavior<List<TARGET>, List<TARGET>>> targetDataProcessList = new ArrayList<>();

    /**
     * 持久化数据操作流程
     */
    private final List<IProcessBehavior<List<TARGET>, List<TARGET>>> persistProcessList = new ArrayList<>();

    /**
     * 输入参数的操作流程
     */
    private final List<IProcessBehavior<List<T>, List<T>>> inputDataProcessList = new ArrayList<>();



    @Override
    public R persist(List<T> inputDatas) throws Throwable {
        //非空判断
        if (CollectionUtils.isEmpty(inputDatas)) {
            return null;
        }
        //处理前置操作
        inputDatas = beforeFlowStart(inputDatas);
        //执行过滤
        if (!CollectionUtils.isEmpty(predicateList)) {
            inputDatas = inputDatas.stream().filter((data) -> Predicates.allOf(predicateList.toArray(new Predicate[0])).test(data)).collect(Collectors.toList());
        }
        //当输入数据处理流程不为空时，执行数据处理流程
        if (!CollectionUtils.isEmpty(inputDataProcessList)) {
            DataProcessCenter.build().addProcess(inputDataProcessList).start(inputDatas);
        }
        //转换成目前类型
        List<TARGET> targetList = inputDatas.stream().map(this::sourceToTarget).filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(targetList)) {
            //当目前对象的操作流程不为空时，执行数据处理流程
            if (!CollectionUtils.isEmpty(targetDataProcessList)) {
                DataProcessCenter.build().addProcess(targetDataProcessList).start(targetList);
            }
            //当持久化操作流程不为空时，则进行持久化操作流程
            if (!CollectionUtils.isEmpty(persistProcessList)) {
                DataProcessCenter.build().addProcess(persistProcessList).start(targetList);
            }
        }

        return null;
    }

    /**
     * 添加对输入数据的转换操作
     *
     * @param processBehavior 操作流程
     */
    public void addInputDataProcess(IProcessBehavior<List<T>, List<T>> processBehavior) {
        this.inputDataProcessList.add(processBehavior);
    }

    /**
     * 添加对转换后的数据的操作处理流程
     *
     * @param processBehavior 操作流程
     */
    public void addTargetDataProcess(IProcessBehavior processBehavior) {
        this.targetDataProcessList.add(processBehavior);
    }

    /**
     * 添加持久化操作流程
     *
     * @param processBehavior 持久化操作流程
     */
    public void addPersistProcess(IProcessBehavior processBehavior) {
        this.persistProcessList.add(processBehavior);
    }

    /**
     * 添加对原始数据的过滤操作
     *
     * @param filter 过滤器
     */
    public void addFilter(Predicate<T> filter) {
        this.predicateList.add(filter);
    }

    /**
     * 前置操作,例如一些结构化数据的操作行为
     *
     * @param datas 数据
     * @return 数据
     */
    protected List<T> beforeFlowStart(List<T> datas) {
        return datas;
    }


    /**
     * 将输入数据转换为目标对象
     *
     * @param data 数据
     * @return 目标对象
     */
    protected abstract TARGET sourceToTarget(T data);

}
