package com.trs.police.common.core.process;

import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 数据处理
 *
 *
 * @param <T> 源
 * @param <R> 结果
 *
 * <AUTHOR>
 * @date 2019/7/8
 */
@FunctionalInterface
@Component
public interface IProcessBehavior<T, R> {

    /**
     * 处理数据
     *
     * @param t t
     * @return 结果
     */
    R apply(T t) throws Throwable;

    /**
     * 前一次process的执行结果作为本次process的参数
     *
     * @param before before
     * @param <V> V
     * @return 结果
     */
    default <V> IProcessBehavior<V, R> compose(IProcessBehavior<? super V, ? extends T> before) {
        Objects.requireNonNull(before);
        return (V v) -> this.apply(before.apply(v));
    }

    /**
     * 当前process的执行结果作为下一process的参数
     *
     * @param after after
     * @param <V> v
     * @return 结果
     */
    default <V> IProcessBehavior<T, V> andThen(IProcessBehavior<? super R, ? extends V> after) {
        Objects.requireNonNull(after);
        return (T t) -> after.apply(this.apply(t));
    }

    /**
     * identity
     *
     * @param <T> t
     * @return 结果
     */
    static <T> IProcessBehavior<T, T> identity() {
        return t -> t;
    }
}
