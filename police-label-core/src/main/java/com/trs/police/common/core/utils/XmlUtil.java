package com.trs.police.common.core.utils;

import com.trs.police.common.core.excpetion.TRSException;
import java.io.StringReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.adapters.XmlAdapter;

/**
 * xml工具类
 *
 * <AUTHOR>
 * @date 2023/02/10
 */
public class XmlUtil {

    private XmlUtil() {
    }

    /**
     * XML转pojo
     *
     * @param clazz pojo类型
     * @param xml   xml字符串
     * @param <T>   范型参数
     * @return 转换好的pojo对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertXmlToObject(Class<T> clazz, String xml) {
        try {
            JAXBContext context = JAXBContext.newInstance(clazz);
            Unmarshaller um = context.createUnmarshaller();
            return (T) um.unmarshal(new StringReader(xml));
        } catch (JAXBException je) {
            throw new TRSException(String.format("Exception while Unmarshaller: %s", je.getMessage()));
        }
    }

    /**
     * xml字符串和日期时间转换适配器
     *
     * <AUTHOR>
     */
    public static class LocalDateTimeAdapter extends XmlAdapter<String, LocalDateTime> {

        private final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        @Override
        public String marshal(LocalDateTime dateTime) {
            return dateTime.format(dateFormat);
        }

        @Override
        public LocalDateTime unmarshal(String dateTime) {
            return LocalDateTime.parse(dateTime, dateFormat);
        }
    }
}
