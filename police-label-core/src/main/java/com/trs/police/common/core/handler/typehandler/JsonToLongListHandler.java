package com.trs.police.common.core.handler.typehandler;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * json转List Long
 *
 * <AUTHOR>
 * @date 2022/6/22 10:00
 */
@Slf4j
public class JsonToLongListHandler extends JacksonTypeHandler {

    @Override
    protected List<Long> parse(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        try {
            List<Long> longs = JsonUtil.parseArray(json, Long.class);
            return longs;
        } catch (Exception e) {
            log.error("解析长整数列表失败", e);
            List<Long> ids = JsonUtil.parseArray(json, Integer.class).stream()
                    .map(Integer::longValue)
                    .collect(Collectors.toList());
            return ids;
        }
    }

    public JsonToLongListHandler(Class<?> type) {
        super(type);
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return rs.getString(columnName) == null ? new ArrayList<>() : parse(rs.getString(columnName));
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return rs.getString(columnIndex) == null ? new ArrayList<>() : parse(rs.getString(columnIndex));
    }

    @Override
    public List<Long> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return cs.getString(columnIndex) == null ? new ArrayList<>() : parse(cs.getString(columnIndex));
    }
}
