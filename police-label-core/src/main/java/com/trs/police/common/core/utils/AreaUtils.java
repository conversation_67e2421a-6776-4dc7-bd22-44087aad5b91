package com.trs.police.common.core.utils;

import com.trs.common.base.PreConditionCheck;
import lombok.Getter;
import net.sourceforge.pinyin4j.PinyinHelper;

import java.util.Locale;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: zhang.wenquan
 * @description: 地域工具类
 * @date: 2022/5/30 16:30
 * @version: 1.0
 */
public class AreaUtils {

    private static final String PREFIX_V2 = "156";

    /**
     * 将6位的地域码转换成15位的第二版地域码
     *
     * @param code 地域码
     * @return v2地域
     */
    public static String convertCodeToV2(String code) {
        PreConditionCheck.checkNotEmpty(code, "地域不能为空");
        // 标准的中国地域
        if (code.length() == 6) {
            return String.format("156%s000000", code);
        }
        // 其它地域原样返回
        return code;
    }

    /**
     * 移除地域后面的0
     *
     * @param code 地域码
     *
     * @return 移除地域后面的0
     */
    public static String areaPrefix(String code) {
        PreConditionCheck.checkNotEmpty(code, "地域码不能为空");
        String prefix = code.replaceAll("0*$", "");
        String s = prefix.replaceAll(PREFIX_V2, "");
        // 对重庆市（500000）这种特俗地域 至少返回2位
        return (s.length() == 1) ? String.format("%s0", prefix) : prefix;
    }


    /**
     * 获取对应等级的地域码
     *
     * @param areaCode 地域码
     * @param level 等级
     *
     * @return 对应等级的地域码
     */
    public static String areaCodeWithLevel(String areaCode, Level level) {
        Objects.requireNonNull(level, "未指定等级");
        String[] strings = splitCode(areaCode);
        switch (level) {
            case PROVINCE:
                return strings[0] + "0000";
            case CITY:
                return strings[0] + strings[1] + "00";
            default:
                return areaCode;
        }
    }

    /**
     * 获取地域等级
     *
     * @param code 地域码
     * @return 等级
     */
    public static Level level(String code) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        String[] codes = splitCode(code);
        if (!"00".equals(codes[2])) {
            return Level.COUNTY;
        }
        if (!"00".equals(codes[1])) {
            return Level.CITY;
        }
        return Level.PROVINCE;
    }

    /**
     * 地域模糊检索的前缀
     *
     * @param code 地域码
     *
     * @return 模糊前缀
     */
    public static String areaKey(String code) {
        String[] strings = splitCode(code);
        String result = strings[0];
        if ("00".equals(strings[1])) {
            return result;
        }
        result += strings[1];
        if ("00".equals(strings[2])) {
            return result;
        }
        result += strings[2];
        return result;
    }


    private static String[] splitCode(String code) {
        if (Objects.isNull(code) || code.length() == 0) {
            throw new IllegalArgumentException("无效的地域编码" + String.valueOf(code));
        }
        // 不足6位进行补足
        if (code.length() < 6) {
            int fillLength = 6 - code.length();
            StringBuilder builder = new StringBuilder().append(code);
            for (int i = 0; i < fillLength; i++) {
                builder.append("0");
            }
            code = builder.toString();
        }
        // 按照等级分割
        String level1 = code.substring(0, 2);
        String level2 = code.substring(2, 4);
        String level3 = code.substring(4, 6);
        return new String[] {level1, level2, level3};
    }

    private static void checkCode(String code) {
        if (null == code || code.length() < 6) {
            throw new IllegalArgumentException("无效的地域编码" + String.valueOf(code));
        }
    }

    /**
     * 地域等级
     *
     * <AUTHOR>
     */
    public enum Level {
        // 省级
        PROVINCE(1, "省级"),
        // 市级
        CITY(2, "市级"),
        // 区县
        COUNTY(3, "区县");

        @Getter
        private Integer code;

        @Getter
        private String name;

        Level(Integer code, String name) {
            this.code = code;
            this.name = name;
        }


        /**
         * 获取子级别
         *
         * @return 子级别
         */
        public Level child() {
            switch (this) {
                case PROVINCE:
                    return CITY;
                case CITY:
                    return COUNTY;
                case COUNTY:
                    return COUNTY;
                default:
                    return null;
            }
        }

        /**
         * 根据代码获取等级
         *
         * @param code 代码
         * @return 等级
         */
        public static Optional<Level> getByCoe(Integer code) {
            for (Level value : Level.values()) {
                if (value.code.equals(code)) {
                    return Optional.of(value);
                }
            }
            return Optional.empty();
        }
    }

    /**
     * 获取首字母
     *
     * @param codeName 地域名称
     * @return 首字母
     */
    public static String getFirstLetter(String codeName) {
        StringBuilder initials = new StringBuilder();
        for (int i = 0; i < codeName.length(); i++) {
            char ch = codeName.charAt(i);
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch);
            if (pinyinArray != null && pinyinArray.length > 0) {
                // 取每个汉字的首字母
                initials.append(pinyinArray[0].charAt(0));
            } else {
                // 如果不是汉字，直接返回空
                return "";
            }
        }
        return initials.toString().toUpperCase(Locale.ROOT);
    }

}
