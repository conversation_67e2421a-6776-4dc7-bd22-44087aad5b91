package com.trs.police.common.core.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/11 13:52
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeyTypeTreeVO implements TreeVO{
    private String key;

    private String type;

    private List<KeyTypeTreeVO> children = new ArrayList<>();


    public KeyTypeTreeVO(String key, String type) {
        this.key = key;
        this.type = type;
    }

    @Override
    public Object getId() {
        return null;
    }

    @Override
    public Object getPid() {
        return null;
    }

    @Override
    public String getName() {
        return null;
    }
}
