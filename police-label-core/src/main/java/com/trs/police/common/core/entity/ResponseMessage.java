package com.trs.police.common.core.entity;


import java.util.HashMap;

/**
 * 封装http restful请求结果
 *
 * <AUTHOR>
 * @date 2022/02/14
 */
public class ResponseMessage extends HashMap<String, Object> {

    private static final long serialVersionUID = -8713837118340960775L;

    private static final String MESSAGE = "message";

    private static final String DATA = "data";

    private static final String SUCCESS = "success";

    private static final String CODE = "code";

    /**
     * 构造器
     *
     * @param message message
     * @return {@link ResponseMessage}
     */
    public ResponseMessage message(String message) {
        this.put(MESSAGE, message);
        return this;
    }

    /**
     * 构造器
     *
     * @param data payload
     * @return {@link ResponseMessage}
     */
    public ResponseMessage data(Object data) {
        this.put(DATA, data);
        return this;
    }

    /**
     * 构造器
     *
     * @param success success
     * @return {@link ResponseMessage}
     */
    public ResponseMessage success(Boolean success) {
        this.put(SUCCESS, success);
        return this;
    }

    @Override
    public ResponseMessage put(String key, Object value) {
        super.put(key, value);
        return this;
    }

    /**
     * message getter
     *
     * @return message
     */
    public String getMessage() {
        return String.valueOf(get(MESSAGE));
    }

    /**
     * data getter
     *
     * @return data
     */
    public Object getData() {
        return get(DATA);
    }

    /**
     * 构造错误的消息体
     *
     * @param msg 消息文本
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage error(String msg) {
        ResponseMessage responseMessage = new ResponseMessage();
        responseMessage.put(CODE, 500);
        responseMessage.put(SUCCESS, false);
        responseMessage.put(MESSAGE, msg);
        responseMessage.put(DATA, null);
        return responseMessage;
    }

    /**
     * 构造错误信息的应答消息
     *
     * @param status 错误状态码
     * @param msg    错误消息
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage errorWithStatus(int status, String msg) {
        ResponseMessage responseMessage = new ResponseMessage();
        responseMessage.put(CODE, status);
        responseMessage.put(SUCCESS, false);
        responseMessage.put(MESSAGE, msg);
        responseMessage.put(DATA, null);
        return responseMessage;
    }

    /**
     * 构造成功信息的应答消息
     *
     * @param o 消息体
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage ok(Object o) {
        ResponseMessage responseMessage = new ResponseMessage();
        responseMessage.put(CODE, 200);
        responseMessage.put(SUCCESS, true);
        responseMessage.put(MESSAGE, null);
        responseMessage.put(DATA, o);
        return responseMessage;
    }

    /**
     * 构造成功信息的应答消息
     *
     * @param status 状态码
     * @param o      消息体
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage okWithStatus(int status, Object o) {
        ResponseMessage responseMessage = new ResponseMessage();
        responseMessage.put(CODE, status);
        responseMessage.put(SUCCESS, true);
        responseMessage.put(MESSAGE, "操作成功");
        responseMessage.put(DATA, o);
        return responseMessage;
    }

    /**
     * 构造未授权信息的应答消息
     *
     * @param o 消息体
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage unauthorized(Object o) {

        ResponseMessage responseMessage = new ResponseMessage();
        responseMessage.put(CODE, 401);
        responseMessage.put(SUCCESS, false);
        responseMessage.put(MESSAGE, "角色未授权");
        responseMessage.put(DATA, o);
        return responseMessage;
    }

    /**
     * 构造逻辑错误信息的应答消息
     *
     * @param message 错误提示信息
     * @return com.trs.police.common.core.entity.ResponseMessage 返回给请求方的响应消息
     **/
    public static ResponseMessage bizError(String message) {
        ResponseMessage responseMessage = new ResponseMessage();
        responseMessage.put(CODE, 500);
        responseMessage.put(SUCCESS, false);
        responseMessage.put(MESSAGE, message);
        responseMessage.put(DATA, null);
        return responseMessage;
    }

}