package com.trs.police.common.core.vo.permission;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 用户VO
 *
 * <AUTHOR>
 * @date 2022/06/19
 */
@Data
public class UserVO implements Serializable {

    private static final long serialVersionUID = -7421002526727826297L;

    /**
     * 主键
     */
    @NotNull(message = "主键为空", groups = Update.class)
    private Long userId;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号为空")
    private String idNumber;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名为空")
    private String name;

    /**
     * 性别
     */
    private String gender;

    /**
     * 警号
     */
    private String policeCode;

    /**
     * 手机号
     */
    private String tel;
    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 电子邮件
     */
    private String email;
    /**
     * 职务
     */
    private String duty;

    /**
     * 部门
     */
    private List<UserDeptVo> deptList;

    /**
     * 密码
     */
    private String userPassword;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 人像
     */
    private String avatar;

    /**
     * 岗位代码
     */
    private Long postCode;

    /**
     * 分组校验
     */
    public @interface Create {

    }

    /**
     * 分组校验
     */
    public @interface Update {

    }

    /**
     * 存储用户与部门相关的信息
     */
    @Data
    @NoArgsConstructor
    public static class UserDeptVo implements Serializable {

        private static final long serialVersionUID = -2107120094450384225L;
        /**
         * 部门Id
         */
        private Long deptId;
        /**
         * 部门名称
         */
        private String deptName;

        /**
         * 部门简称
         */
        private String shortName;

        /**
         * 角色权限Id数组
         */
        private List<Long> rolePermissionIds;
        /**
         * 数据权限Id数组
         */
        private List<Long> dataPermissionIds;

        public UserDeptVo(Long deptId, String deptName, List<Long> rolePermissionIds, List<Long> dataPermissionIds) {
            this.deptId = deptId;
            this.deptName = deptName;
            this.rolePermissionIds = rolePermissionIds;
            this.dataPermissionIds = dataPermissionIds;
        }

        public UserDeptVo(Long deptId, String deptName, String shortName,List<Long> rolePermissionIds, List<Long> dataPermissionIds) {
            this.deptId = deptId;
            this.deptName = deptName;
            this.shortName = shortName;
            this.rolePermissionIds = rolePermissionIds;
            this.dataPermissionIds = dataPermissionIds;
        }
    }
}
