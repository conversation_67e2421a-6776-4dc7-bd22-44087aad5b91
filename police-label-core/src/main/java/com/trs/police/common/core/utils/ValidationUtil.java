package com.trs.police.common.core.utils;

/**
 * 校验工具类$
 *
 * <AUTHOR> yanghy
 * @date : 2022/8/26 9:54
 */
public class ValidationUtil {

    private ValidationUtil() {}

    /**
     * 身份证前17位系数
     */
    private static final int[] COEFFICIENT = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    /**
     * 尾数校验位
     */
    private static final char[] LAST = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

    /**
     * 身份证校验
     *
     * @param identity 身份证
     * @return {@link Boolean}
     */
    public static Boolean validateIdentity(String identity){
        if (identity == null || identity.length() != 18) {
            //非18位直接返回false
            return false;
        }
        //前17位加权和
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            if (!Character.isDigit(identity.charAt(i))) {
                //前17位必须是数字,如果不是直接返回false
                return false;
            }
            //对前17位进行加权求和
            sum += Integer.parseInt(identity.charAt(i) + "") * COEFFICIENT[i];
        }

        return identity.charAt(17) == LAST[sum % 11];
    }
}
