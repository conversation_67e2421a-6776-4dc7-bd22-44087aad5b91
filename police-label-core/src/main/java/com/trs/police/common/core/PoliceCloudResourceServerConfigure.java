package com.trs.police.common.core;

import com.trs.police.common.core.converter.CustomResourceUserAuthenticationConverter;
import com.trs.police.common.core.handler.CommonAccessDeniedHandler;
import com.trs.police.common.core.handler.CommonAuthExceptionEntryPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;

/**
 * 资源服务器配置类
 *
 * <AUTHOR>
 * @date 2022/04/14
 */
@EnableResourceServer
@EnableAutoConfiguration(exclude = UserDetailsServiceAutoConfiguration.class)
public class PoliceCloudResourceServerConfigure extends ResourceServerConfigurerAdapter {

    private CommonAccessDeniedHandler accessDeniedHandler;
    private CommonAuthExceptionEntryPoint exceptionEntryPoint;
    private RemoteTokenServices remoteTokenServices;

    /**
     * @param accessDeniedHandler {@link CommonAccessDeniedHandler}
     */
    @Autowired(required = false)
    public void setAccessDeniedHandler(CommonAccessDeniedHandler accessDeniedHandler) {
        this.accessDeniedHandler = accessDeniedHandler;
    }

    /**
     * @param exceptionEntryPoint {@link CommonAuthExceptionEntryPoint}
     */
    @Autowired(required = false)
    public void setExceptionEntryPoint(CommonAuthExceptionEntryPoint exceptionEntryPoint) {
        this.exceptionEntryPoint = exceptionEntryPoint;
    }

    /**
     * @param remoteTokenServices {@link RemoteTokenServices}
     */
    @Autowired(required = false)
    public void setRemoteTokenServices(RemoteTokenServices remoteTokenServices) {
        this.remoteTokenServices = remoteTokenServices;
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) {
        if (exceptionEntryPoint != null) {
            resources.authenticationEntryPoint(exceptionEntryPoint);
        }
        if (accessDeniedHandler != null) {
            resources.accessDeniedHandler(accessDeniedHandler);
        }
        if (remoteTokenServices != null) {
            DefaultAccessTokenConverter tokenConverter = new DefaultAccessTokenConverter();
            tokenConverter.setUserTokenConverter(new CustomResourceUserAuthenticationConverter());
            remoteTokenServices.setAccessTokenConverter(tokenConverter);
        }
    }
}
