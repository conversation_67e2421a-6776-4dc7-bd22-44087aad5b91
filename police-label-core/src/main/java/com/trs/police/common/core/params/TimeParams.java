package com.trs.police.common.core.params;

import com.trs.police.common.core.constant.enums.TimeRangeEnum;
import com.trs.police.common.core.utils.TimeUtil;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/08/29
 */
@Data
@Validated
public class TimeParams implements Serializable {

    private static final long serialVersionUID = 2816916009428095387L;

    //TODO range改为枚举
    /**
     * 时间范围 {@link TimeRangeEnum}
     */
    private String range;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 获取yyyyMMddHHmmss的开始时间
     */
    private String noRegularBeginTime;

    /**
     * 获取yyyyMMddHHmmss的截至时间
     */
    private String noRegularEndTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * @return 按照时间范围返回开始时间
     */
    public LocalDateTime getBeginTime() {
        TimeRangeEnum rangeEnum = TimeRangeEnum.codeOf(range);
        if (Objects.isNull(rangeEnum)) {
            return null;
        }

        if (rangeEnum == TimeRangeEnum.CUSTOM) {
            return beginTime == null ? TimeUtil.DEFAULT_BEGIN_TIME : beginTime;
        } else {
            return TimeUtil.getBeginTime(rangeEnum, LocalDate.now());
        }
    }

    /**
     * @return 返回yyyyMMddHHmmss格式的时间
     */
    public String getNoRegularBeginTime() {
        LocalDateTime resultTime = getBeginTime();
        noRegularBeginTime = resultTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return noRegularBeginTime;
    }

    /**
     * @return 返回yyyyMMddHHmmss格式的时间
     */
    public String getNoRegularEndTime() {
        LocalDateTime resultTime = getEndTime();
        noRegularEndTime = resultTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return noRegularEndTime;
    }

    /**
     * @return 按照时间范围返回结束时间
     */
    public LocalDateTime getEndTime() {
        TimeRangeEnum rangeEnum = TimeRangeEnum.codeOf(range);
        if (Objects.isNull(rangeEnum)) {
            return null;
        }
        if (rangeEnum == TimeRangeEnum.CUSTOM) {
            return endTime == null ? TimeUtil.DEFAULT_END_TIME : endTime;
        } else {
            return TimeUtil.getEndTime(rangeEnum, LocalDate.now());
        }
    }

    /**
     * 构造函数
     *
     * @param range 参数
     */
    public TimeParams(TimeRangeEnum range) {
        this.range = range.getCode();
    }

    /**
     * 构造器：默认全部时间
     */
    public TimeParams() {
        this.range = "0";
    }

    /**
     * 构造器
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     */
    public TimeParams(LocalDateTime beginTime, LocalDateTime endTime) {
        this.range = TimeRangeEnum.CUSTOM.getCode();
        this.beginTime = beginTime;
        this.endTime = endTime;
    }

    /**
     * 回溯时间段
     *
     * @param period 回溯的距离
     * @return {@link TimeParams} 回溯后的时间段
     */
    public TimeParams backtrack(Period period) {
        TimeParams result = new TimeParams(TimeRangeEnum.CUSTOM);
        result.setBeginTime(this.getBeginTime().minus(period));
        result.setEndTime(this.getEndTime().minus(period));
        return result;
    }

    /**
     * 拆分时间段 用于折线图生成时间点，<1个月的时间段按照天拆分，>1个月的时间段按照月拆分
     *
     * @return 时间点
     */
    public List<LocalDateTime> split() {
        // 计算时间段
        Period period = Period.between(this.getBeginTime().toLocalDate(), this.getEndTime().toLocalDate());
        // 1天内时间段按照小时拆分，1个月内的时间段按照天拆分，>1个月的时间段按照月拆分
        if (period.getDays() <= 1) {
            return TimeUtil.split(this.getBeginTime(), this.getEndTime(), ChronoUnit.HOURS);
        } else if (period.getMonths() < 1) {
            return TimeUtil.split(this.getBeginTime(), this.getEndTime(), ChronoUnit.DAYS);
        } else if (period.getYears() < 1) {
            return TimeUtil.split(this.getBeginTime(), this.getEndTime(), ChronoUnit.MONTHS);
        }
        // >1年的时间段按照年拆分
        return TimeUtil.split(this.getBeginTime(), this.getEndTime(), ChronoUnit.YEARS);
    }

    /**
     * 获取时间段
     *
     * @return 时间段
     */
    public Duration generateDuration() {
        return Duration.between(this.getBeginTime(), this.getEndTime());
    }

    /**
     * 获取时间段
     *
     * @return 时间段
     */
    public Period generatePeriod() {
        if (Duration.between(this.getBeginTime(), this.getEndTime()).toDays() <= 0) {
            return Period.ofDays(1);
        }
        return Period.between(this.getBeginTime().toLocalDate(), this.getEndTime().toLocalDate());
    }

    /**
     * 是否是全部
     *
     * @return 布尔
     */
    public Boolean isAll() {
        TimeRangeEnum rangeEnum = TimeRangeEnum.codeOf(range);
        return rangeEnum == TimeRangeEnum.ALL;
    }

    /**
     * 获取时间范围名称
     *
     * @return 时间范围名称
     */
    public String getName() {
        TimeRangeEnum rangeEnum = TimeRangeEnum.codeOf(range);
        if (Objects.isNull(rangeEnum)) {
            return null;
        }
        return rangeEnum.getName();
    }
}
