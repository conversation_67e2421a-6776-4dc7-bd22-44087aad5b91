package com.trs.police.common.core.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * key-value vo
 *
 * <AUTHOR>
 * @since 2022/4/2 14:12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeyValueVO implements Serializable {

    private static final long serialVersionUID = -3533898074459075046L;
    private String key;
    private String value;

    /**
     * 获取value
     *
     * @param list 列表
     * @param key  键
     * @return value
     */
    public static String get(List<KeyValueVO> list, String key) {
        for (KeyValueVO vo : list) {
            if (vo.getKey().equals(key)) {
                return vo.getValue();
            }
        }
        return null;
    }
}
