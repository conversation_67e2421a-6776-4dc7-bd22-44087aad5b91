package com.trs.police.common.core.vo.permission;

import com.trs.police.common.core.entity.CurrentUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户信息
 *
 * <AUTHOR> yanghy
 * @date : 2022/7/29 15:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleUserVO implements Serializable {

    private static final long serialVersionUID = -5101258436340843366L;
    /**
     * 用户Id
     */
    @NotNull(message = "用户id不能为空！")
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户身份证（这里是第三方使用的，第三方没有用户id，需要通过这玩意来映射到我们系统的用户）
     */
    private String idCard;
    /**
     * 部门Id
     */
    @NotNull(message = "部门id不能为空！")
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 部门简称
     */
    private String deptShortName;

    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 用户手机号
     */
    private String tel;
    /**
     * 职务
     */
    private String duty;

    /**
     * 激活状态
     * 1：是
     */
    private Integer status;

    /**
     * 区域代码
     */
    private String districtCode;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 今日值班状态
     * 1：是
     */
    private Integer zbStatus;

    public SimpleUserVO(CurrentUser currentUser) {
        this(
                currentUser.getId(),
                currentUser.getRealName(),
                currentUser.getIdNumber(),
                currentUser.getDept().getId(),
                currentUser.getDept().getName(),
                currentUser.getDept().getShortName(),
                currentUser.getDept().getCode(),
                currentUser.getMobile(),
                currentUser.getDuty(),
                currentUser.getStatus(),
                currentUser.getDept().getDistrictCode(),
                currentUser.getPostName(),
                null
        );
    }

    /**
     * 拼接字符串
     *
     * @return 结果
     */
    public String toUserString() {
        return userName + "（" + deptShortName + "）";
    }

    /**
     * 转UserDeptVO
     *
     * @return UserDeptVO
     */
    public UserDeptVO toUserDeptVO() {
        return new UserDeptVO(userId, deptId);
    }

}
