package com.trs.police.common.core.flow;

import com.trs.web.builder.base.IKey;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.control.Either;

import java.util.List;
import java.util.Objects;

/**
 * 数据处理流程顶层接口
 *
 * @param <T> 输入的数据
 * @param <R> 返回结果
 *
 * @author:wen.wen
 * @create 2022-05-20 09:34
 **/
public interface IDataDealFlow<T, R> extends IKey {

    /**
     * 对数据处理流程的持久化
     *
     * @param inputDatas 输入的数据
     * @return 返回结果
     */
    R persist(List<T> inputDatas) throws Throwable;

    /**
     * 根据key 获取IDataDealFlow的实现
     *
     * @param key 唯一值
     * @return 结果
     */
    static IDataDealFlow dataDealFlow(String key) {
        //扫描所有实现 IDataDealFlow 接口的类
        Either<Throwable, Object[]> dataDealFlowEither = BeanFactoryHolder.getBeanArrOfType(IDataDealFlow.class);
        if (dataDealFlowEither.isRight()) {
            for (Object dataDealFlowObj : dataDealFlowEither.get()) {
                if (dataDealFlowObj instanceof IDataDealFlow) {
                    IDataDealFlow iDataDealFlow = (IDataDealFlow) dataDealFlowObj;
                    if (Objects.equals(key, iDataDealFlow.key())) {
                        return iDataDealFlow;
                    }
                }
            }
        }

        throw new IllegalArgumentException("未找到类型为" + key + "的IDataDealFlow的实现类");
    }
}
