package com.trs.police.common.core.constant;

import lombok.Getter;

/**
 * 数据库字段映射类型
 */
public enum DataBaseFieldMappingType {

    STRING(FieldTypeMapping.STRING.getName(), "字符串"),
    NUMBER(FieldTypeMapping.NUMBER.getName(), "数字"),
    DATETIME(FieldTypeMapping.DATETIME.getName(), "日期"),
    BOOLEAN(FieldTypeMapping.BOOLEAN.getName(), "布尔"),
    GEOMETRY(FieldTypeMapping.GEO.getName(), "地理位置"),;

    @Getter
    private String fieldType;

    @Getter
    private String typeName;

    DataBaseFieldMappingType(String fieldType, String typeName) {
        this.fieldType = fieldType;
        this.typeName = typeName;
    }

    /**
     * 获取类型名称
     *
     * @param fieldType type
     * @return 名称
     */
    public static String getTypeName(String fieldType) {
        if (fieldType == null) {
            return "";
        }
        for (DataBaseFieldMappingType type : DataBaseFieldMappingType.values()) {
            if (type.getFieldType().equals(fieldType)) {
                return type.getTypeName();
            }
        }
        return "";
    }

    /**
     * 获取类型
     *
     * @param fieldType type
     * @return 名称
     */
    public static DataBaseFieldMappingType getType(String fieldType) {
        if (fieldType == null) {
            return null;
        }
        for (DataBaseFieldMappingType type : DataBaseFieldMappingType.values()) {
            if (type.getFieldType().equals(fieldType)) {
                return type;
            }
        }
        return null;
    }
}
