package com.trs.police.common.core.vo.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/15 9:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDeptRelationVO {
    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 单位id
     */
    private Long deptId;

    /**
     * 职务代码
     */
    private String duty;

    /**
     * 武器库id数组
     */
    private List<Long> armoryIds;
}
