package com.trs.police.common.core.params;

import com.trs.police.common.core.utils.TimeUtil;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @date 2023/05/05
 */
@Data
@AllArgsConstructor
public class TimePoint {

    /**
     * 时间点
     */
    private LocalDateTime time;

    /**
     * 时间单位
     */
    private ChronoUnit unit;

    /**
     * 获取时间段结束时间
     *
     * @return 时间段结束时间
     */
    public LocalDateTime getEndTime() {
        return time.plus(1, unit);
    }

    /**
     * TimeParams 转换成时间点列表
     *
     * @param timeParams 时间参数
     * @return 时间点列表
     */
    public static List<TimePoint> of(TimeParams timeParams) {
        // 计算时间段
        LocalDateTime beginTime = timeParams.getBeginTime();
        LocalDateTime endTime = timeParams.getEndTime();
        Period period = Period.between(beginTime.toLocalDate(), endTime.toLocalDate());
        ChronoUnit unit = getUnit(period);
        List<LocalDateTime> times = TimeUtil.split(beginTime, endTime, unit);
        LocalDateTime now = LocalDateTime.now();
        return times.stream()
            .filter(time -> time.isBefore(now) && time.isBefore(endTime))
            .map(time -> new TimePoint(time, unit))
            .collect(Collectors.toList());
    }

    @NotNull
    private static ChronoUnit getUnit(Period period) {
        // 超过一年就以年为单位
        if (period.toTotalMonths() > 12) {
            return ChronoUnit.YEARS;
        }

        // 超过一个月就以月为单位
        if (period.toTotalMonths() > 1 || (period.getMonths() == 1 && period.getDays() > 0)) {
            return ChronoUnit.MONTHS;
        }

        // 超过一天就以天为单位
        if (period.getDays() > 1 || (period.getMonths() == 1 && period.getDays() == 0)) {
            return ChronoUnit.DAYS;
        }

        // 否则以小时为单位
        return ChronoUnit.HOURS;
    }

    /**
     * 获取时间轴显示文本
     *
     * @return 时间轴显示文本
     */
    public String getAxisText() {
        switch (unit) {
            case YEARS:
                return time.format(DateTimeFormatter.ofPattern("yyyy年"));
            case MONTHS:
                return time.format(DateTimeFormatter.ofPattern("MM月"));
            case HOURS:
                return time.format(DateTimeFormatter.ofPattern("HH时"));
            case DAYS:
            default:
                return time.format(DateTimeFormatter.ofPattern("MM-dd"));
        }
    }
}
