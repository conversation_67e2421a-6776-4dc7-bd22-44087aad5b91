package com.trs.police.common.core.dto;

import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.vo.TreeVO;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import lombok.Data;

import java.io.Serializable;

/**
 * 部门dto
 *
 * <AUTHOR>
 */
@Data
public class DeptDto implements TreeVO, Serializable {

    private static final long serialVersionUID = 4437403326727620797L;

    /**
     * 部门id
     */
    private Long id;
    /**
     * 机构代码
     */
    private String code;

    /**
     * 机构名称
     */
    private String name;

    /**
     * 机构名称简称
     */
    private String shortName;

    /**
     * 上级部门id
     */
    private Long pid;

    /**
     * 警种代码
     */
    private Long policeKind;

    /**
     * 排序使用
     */
    private Integer showOrder;

    /**
     * 部门签章图片位置
     */
    private Long signature;

    /**
     * 状态
     */
    private Short deleted;

    /**
     * 部门级别
     */
    private Integer level;

    /**
     * path
     */
    private String path;

    /**
     * 区域代码
     */
    private String districtCode;

    /**
     * 类别
     */
    private Long type;

    /**
     * 被@的单位类型
     */
    private String atDeptType;

    /**
     * 类别详情
     */
    private Long childType;

    private String districtName;

    private String districtShortName;

    /**
     * 区域主要名称
     */
    private String districtMainName;

    /**
     * 转simpleDeptVO
     *
     * @return SimpleDeptVO
     */
    public SimpleDeptVO toSimpleVO() {
        return new SimpleDeptVO(
                id,
                code,
                name,
                shortName,
                type,
                atDeptType,
                districtCode,
                districtName,
                districtShortName,
                districtMainName
        );
    }

    /**
     * 获取前缀code
     *
     * @return 前缀code
     */
    public String getPrefixCode() {
        return StringUtil.getPrefixCode(code);
    }

    /**
     * toDeptVO
     *
     * @return {@link DeptVO}
     */
    public DeptVO toDeptVO() {
        DeptVO deptVO = new DeptVO();
        deptVO.setDeptId(this.getId());
        deptVO.setDeptCode(this.getCode());
        deptVO.setDeptName(this.getName());
        deptVO.setShortName(this.getShortName());
        deptVO.setPid(this.getPid());
        return deptVO;
    }
}
