package com.trs.police.common.core.entity.node.comparable;

import org.jetbrains.annotations.NotNull;

/**
 * 空
 *
 * <AUTHOR>
 */
public class EmptyValue extends ComparableValue {

    @Override
    public String getValueString() {
        return "";
    }

    @Override
    public int compareTo(@NotNull String o) {
        return "".compareTo(o);
    }

    @Override
    public Boolean isNull() {
        return false;
    }

    @Override
    public Boolean isEmpty() {
        return true;
    }
}
