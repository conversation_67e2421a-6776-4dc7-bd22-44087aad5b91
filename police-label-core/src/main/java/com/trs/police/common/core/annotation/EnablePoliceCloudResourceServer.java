package com.trs.police.common.core.annotation;

import com.trs.police.common.core.PoliceCloudResourceServerConfigure;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(PoliceCloudResourceServerConfigure.class)
public @interface EnablePoliceCloudResourceServer {

}
