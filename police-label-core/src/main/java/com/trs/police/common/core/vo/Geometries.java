package com.trs.police.common.core.vo;

import com.trs.police.common.core.utils.GeoUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * geo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class Geometries {

    /**
     * 类型： circle polygon
     */
    private String type;

    /**
     * 值
     */
    private String geometry;

    /**
     * 属性
     */
    private Properties properties;

    /**
     * 节点属性
     */
    @Data
    @NoArgsConstructor
    public static class Properties {

        private String radius;
    }

    /**
     * 获取经度
     *
     * @return 经度
     */
    public String getLon() {
        if (!"circle".equals(type)) {
            return null;
        }
        // 提取POINT(104.068379 30.605988)格式
        String[] split = geometry.split("\\(")[1]
                .split("\\)")[0]
                .split(" ");
        return split[0];
    }

    /**
     * 获取纬度
     *
     * @return 纬度
     */
    public String getLat() {
        if (!"circle".equals(type)) {
            return null;
        }
        // 提取POINT(104.068379 30.605988)格式
        String[] split = geometry.split("\\(")[1]
                .split("\\)")[0]
                .split(" ");
        return split[1];
    }

    /**
     * 转换为多边形
     *
     * @return 多边形
     */
    public String convertToPolygon() {
        if (!"circle".equals(type)) {
            return geometry;
        }
        return GeoUtils.convertCircleToPolygon(this);
    }
}
