package com.trs.police.common.core.json;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.DefaultSerializerProvider;
import com.fasterxml.jackson.databind.ser.SerializerFactory;
import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * jackson空值序列化器
 *
 * <AUTHOR>
 * @date 2020/11/10
 **/
public class CustomNullSerializerProvider extends DefaultSerializerProvider {

    private static final long serialVersionUID = -8515057624811000161L;

    /**
     * 默认构造器
     */
    public CustomNullSerializerProvider() {
        super();
    }

    /**
     * 构造器
     *
     * @param provider {@link CustomNullSerializerProvider}
     * @param config   {@link SerializationConfig}
     * @param jsf      {@link SerializerFactory}
     */
    public CustomNullSerializerProvider(CustomNullSerializerProvider provider, SerializationConfig config,
        SerializerFactory jsf) {
        super(provider, config, jsf);
    }

    @Override
    public DefaultSerializerProvider createInstance(SerializationConfig serializationConfig,
        SerializerFactory serializerFactory) {
        return new CustomNullSerializerProvider(this, serializationConfig, serializerFactory);
    }

    @Override
    public JsonSerializer<Object> findNullValueSerializer(BeanProperty property) throws JsonMappingException {
        if (property.getType().getRawClass().isAssignableFrom(List.class) || property.getType().getRawClass()
            .isAssignableFrom(Set.class)) {
            return EmptyCollectionSerializer.INSTANCE;
        } else {
            return super.findNullValueSerializer(property);
        }
    }

    private static class EmptyCollectionSerializer extends JsonSerializer<Object> {

        public static final JsonSerializer<Object> INSTANCE = new EmptyCollectionSerializer();

        private EmptyCollectionSerializer() {
        }

        @Override
        public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
            throws IOException {
            jsonGenerator.writeStartArray();
            jsonGenerator.writeEndArray();
        }
    }
}
