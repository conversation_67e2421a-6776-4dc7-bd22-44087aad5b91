package com.trs.police.common.core.utils;

import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.vo.RatioItem;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 比例计算工具类
 * *@author:wen.wen
 * *@create 2024-07-09 21:32
 **/
public class RatioCalculationUtils {


    /**
     * 计算同比和环比
     * 同比：同比增长率＝（本期数－同期数）／同期数×100%
     * 环比：环比增长率 =（本期数－上期数）／上期数×100%
     *
     * @param currentDatas  当前时间周期数据
     * @param recentDatas   上一个统计周期的数据
     * @param lastYearDatas 历史同时期的数据
     * @param keyFnc        唯一key
     * @param nameFnc       nameFnc
     * @param countFnc      统计数量
     * @param timeParams    timeParams
     * @param <T>          泛型
     * @return Map
     */
    public static <T> Map<String, RatioItem> calculateRatio2Map(List<T> currentDatas,
                                                                List<T> recentDatas,
                                                                List<T> lastYearDatas,
                                                                Function<T, String> keyFnc,
                                                                Function<T, String> nameFnc,
                                                                Function<T, Long> countFnc,
                                                                TimeParams timeParams) {
        List<RatioItem> items = calculateRatio(currentDatas, recentDatas, lastYearDatas, keyFnc, nameFnc, countFnc, timeParams);
        if (CollectionUtils.isEmpty(items)) {
            return new HashMap<>();
        }
        return items.stream().collect(Collectors.toMap(RatioItem::getKey, o -> o, (o1, o2) -> o1, LinkedHashMap::new));
    }

    /**
     * 计算同比和环比
     * 同比：同比增长率＝（本期数－同期数）／同期数×100%
     * 环比：环比增长率 =（本期数－上期数）／上期数×100%
     *
     * @param currentDatas  当前时间周期数据
     * @param recentDatas   上一个统计周期的数据
     * @param lastYearDatas 历史同时期的数据
     * @param keyFnc        唯一key
     * @param nameFnc       nameFnc
     * @param countFnc      统计数量
     * @param timeParams    timeParams
     * @param <T>          泛型
     * @return 统计结果
     */
    public static <T> List<RatioItem> calculateRatio(List<T> currentDatas,
                                                     List<T> recentDatas,
                                                     List<T> lastYearDatas,
                                                     Function<T, String> keyFnc,
                                                     Function<T, String> nameFnc,
                                                     Function<T, Long> countFnc,
                                                     TimeParams timeParams) {
        if (CollectionUtils.isEmpty(currentDatas)) {
            return new ArrayList<>();
        }
        Map<String, T> recentDataGroup = new HashMap<>();
        if (!CollectionUtils.isEmpty(recentDatas)) {
            recentDataGroup = recentDatas.stream().collect(Collectors.toMap(keyFnc, (o) -> o, (o1, o2) -> o1));
        }

        Map<String, T> lastYearDataGroup = new HashMap<>();
        if (!CollectionUtils.isEmpty(lastYearDatas)) {
            lastYearDataGroup = lastYearDatas.stream().collect(Collectors.toMap(keyFnc, (o) -> o, (o1, o2) -> o1));
        }
        List<RatioItem> ratioItems = new ArrayList<>();
        for (T currentData : currentDatas) {
            String key = keyFnc.apply(currentData);
            //计算环比
            T recentData = recentDataGroup.get(key);
            //计算同比
            T lastYearData = lastYearDataGroup.get(key);
            ratioItems.add(computeRatio(currentData, recentData, lastYearData, keyFnc, nameFnc, countFnc));
        }

        return ratioItems;
    }

    /**
     * 计算同比和环比
     * 同比：同比增长率＝（本期数－同期数）／同期数×100%
     * 环比：环比增长率 =（本期数－上期数）／上期数×100%
     *
     * @param currentData  当前时间周期数据
     * @param recentData   上一个统计周期的数据
     * @param lastYearData 历史同时期的数据
     * @param keyFnc       keyFnc
     * @param nameFnc      nameFnc
     * @param countFnc     统计数量
     * @param <T>          泛型
     * @return 统计结果
     */
    public static <T> RatioItem computeRatio(T currentData, T recentData,
                                             T lastYearData,
                                             Function<T, String> keyFnc,
                                             Function<T, String> nameFnc,
                                             Function<T, Long> countFnc) {
        RatioItem ratioItem = new RatioItem();
        ratioItem.setKey(keyFnc.apply(currentData));
        ratioItem.setName(nameFnc.apply(currentData));
        // 记录数量，统计总数时用到
        ratioItem.setCount(Objects.isNull(currentData) ? 0 : countFnc.apply(currentData));
        ratioItem.setRecentCount(Objects.isNull(recentData) ? 0 : countFnc.apply(recentData));
        ratioItem.setLastYearCount(Objects.isNull(lastYearData) ? 0 : countFnc.apply(lastYearData));
        //计算环比
        ratioItem.setRatio(computeRatio(ratioItem.getCount(), ratioItem.getRecentCount()));
        //计算同比
        ratioItem.setYoy(computeRatio(ratioItem.getCount(), ratioItem.getLastYearCount()));
        return ratioItem;
    }


    /**
     * 计算环比/同比
     * 增长率 =（本期数－上期数(同期数)）／上期数（同期）×100%
     *
     * @param cCount 本期数
     * @param lCount 上期数
     * @return Double
     */
    public static Double computeRatio(Long cCount, Long lCount) {
        // 两者都为0，则返回0
        if (lCount == 0L && cCount == 0) {
            return 0D;
        }
        // 上期为0，则返回1
        if (lCount == 0L) {
            return 1D;
        }
        // 其余正常计算
        BigDecimal bcCount = new BigDecimal(cCount);
        BigDecimal byCount = new BigDecimal(lCount);
        BigDecimal growthAmount = bcCount.subtract(byCount);
        BigDecimal growthRate = growthAmount.divide(byCount, 4, RoundingMode.HALF_UP);
        return growthRate.doubleValue();
    }
}
