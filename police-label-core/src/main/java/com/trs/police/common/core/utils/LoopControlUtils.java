package com.trs.police.common.core.utils;

import com.trs.common.base.PreConditionCheck;
import com.trs.web.entity.PageInfo;
import com.trs.web.entity.PageList;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 循环控制相关工具类
 * *@author:wen.wen
 * *@create 2023-04-25 10:13
 **/
public class LoopControlUtils {

    public static final Integer DEFAULT_START_PAGE_NUM = 1;

    public static final Integer DEFAULT_PAGE_SIZE = 500;

    private LoopControlUtils() {
    }

    /**
     * do while行为工具
     *
     * @param pageListFuction 分页获取数据行为
     * @param action          操作行为
     * @param <T>             泛型
     */
    public static <T> void doWhile(Function<PageInfo, PageList<T>> pageListFuction,
                                   Consumer<List<T>> action) {

        doWhile(DEFAULT_START_PAGE_NUM, DEFAULT_PAGE_SIZE, pageListFuction, action);
    }

    /**
     * do while行为工具
     *
     * @param pageSize        页面大小
     * @param pageListFuction 分页获取数据行为
     * @param action          操作行为
     * @param <T>             泛型
     */
    public static <T> void doWhile(Integer pageSize,
                                   Function<PageInfo, PageList<T>> pageListFuction,
                                   Consumer<List<T>> action) {

        doWhile(DEFAULT_START_PAGE_NUM, pageSize, pageListFuction, action);
    }

    /**
     * do while行为工具
     *
     * @param startPageNum    其实页码,为空默认1
     * @param pageSize        页面大小,为空默认500
     * @param pageListFuction 分页获取数据行为
     * @param action          操作行为
     * @param <T>             泛型
     */
    public static <T> void doWhile(Integer startPageNum,
                                   Integer pageSize,
                                   Function<PageInfo, PageList<T>> pageListFuction,
                                   Consumer<List<T>> action) {
        doWhileV2(startPageNum, pageSize, pageListFuction, pageList -> action.accept(pageList.getContents()));
    }

    /**
     * do while行为工具
     *
     * @param startPageNum    其实页码,为空默认1
     * @param pageSize        页面大小,为空默认500
     * @param pageListFuction 分页获取数据行为
     * @param action          操作行为
     * @param <T>             泛型
     */
    public static <T> void doWhileV2(Integer startPageNum,
                                     Integer pageSize,
                                     Function<PageInfo, PageList<T>> pageListFuction,
                                     Consumer<PageList<T>> action) {
        PreConditionCheck.checkArgument(pageListFuction != null, "分页捞取数据行为不能为空");
        PreConditionCheck.checkArgument(action != null, "对数据的处理行为不能为空");
        startPageNum = startPageNum == null ? DEFAULT_START_PAGE_NUM : startPageNum;
        pageSize = pageSize == null ? DEFAULT_PAGE_SIZE : pageSize;
        Integer pageNum = startPageNum;
        Long total;
        do {
            PageList<T> pageList = pageListFuction.apply(PageInfo.newPage(pageNum, pageSize));
            if (pageList == null) {
                break;
            }
            action.accept(pageList);
            total = pageList.getTotal();
            if (total == null || total == 0) {
                break;
            }
            pageNum++;
        } while (onCondition(startPageNum, pageNum, pageSize, total));
    }

    /**
     * 分页检索条件
     *
     * @param pageNum      页码
     * @param startPageNum 起始页码
     * @param pageSize     页面大小
     * @param total        总数
     * @return true/false
     */
    private static boolean onCondition(Integer startPageNum, Integer pageNum, Integer pageSize, Long total) {
        if (Objects.equals(startPageNum, 1)) {
            return (long) (pageNum - 1) * pageSize < total;
        }

        return (long) pageNum * pageSize < total;
    }
}
