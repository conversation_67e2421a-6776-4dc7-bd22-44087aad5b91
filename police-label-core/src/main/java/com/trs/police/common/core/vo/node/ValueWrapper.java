package com.trs.police.common.core.vo.node;

import com.trs.police.common.core.constant.ValueType;
import com.trs.police.common.core.entity.node.*;
import com.trs.police.common.core.entity.node.comparable.EmptyValue;
import com.trs.police.common.core.entity.node.comparable.NumberValue;
import com.trs.police.common.core.entity.node.comparable.StringValue;
import com.trs.police.common.core.entity.node.comparable.TimeValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ValueWrapper {

    /**
     * 值类型 {@link ValueType}
     */
    private String type;

    /**
     * 值
     */
    private String[] value;

    /**
     * 来源节点
     */
    private String fromNode;

    public ValueWrapper(String type, String[] value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取目标值
     *
     * @param index 数组下标
     * @return 目标值
     */
    public Value getTargetValue(int index) {
        String v = Objects.nonNull(value) && value.length > index ? value[index] : null;
        // 日期函数返回具体日期 yyyy-MM-dd HH:mm:ss
        List<String> times = Arrays.asList(ValueType.A_MONTH_AGO, ValueType.THREE_MONTHS_AGO, ValueType.A_FEW_DAYS_AGO, ValueType.TIME_ENTERED);
        if (times.contains(type)) {
            return new TimeValue(type, v);
        }
        // 数值函数
        if (ValueType.NUMBER.equals(type)) {
            return new NumberValue(v);
        }
        // 文本函数
        if (ValueType.STRING_VALUE.equals(type)) {
            return new StringValue(v);
        }
        // 正则
        if (ValueType.REGULAR.equals(type)) {
            return new RegularValue(v);
        }
        // 布尔
        if (ValueType.TRUE_VALUE.equals(type) || ValueType.FALSE_VALUE.equals(type)) {
            return new BooleanValue(v);
        }
        // 空值
        if (ValueType.NULL_VALUE.equals(type)) {
            return new NullValue();
        }
        if (ValueType.EMPTY.equals(type)) {
            return new EmptyValue();
        }
        // 控制参数
        if (ValueType.CONTROL_PARAM.equals(type)) {
            return new ControlValue(v);
        }
        // 其他字段
        if (ValueType.FIELD_TYPE.equals(type)) {
            return new TableFieldValue(v, fromNode);
        }
        // 多边形
        if (ValueType.GEO_CONTAINS.equals(type)) {
            return new Polygon(v);
        }
        return new StringValue(v);
    }
}
