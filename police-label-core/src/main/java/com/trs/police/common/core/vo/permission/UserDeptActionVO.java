package com.trs.police.common.core.vo.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 记录用户及操作时间
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDeptActionVO {

    /**
     * 操作时间
     */
    private LocalDateTime time;
    /**
     * 用户信息
     */
    private UserDeptVO userInfo;
    /**
     * 审批状态
     */
    private Integer status;
    /**
     * 审批意见
     */
    private String reason;

}
