package com.trs.police.common.core.valication;

import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import com.trs.police.common.core.utils.ValidationUtil;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;


/**
 * 18位身份证号码验证
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, PARAMETER})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = Identity.IdentityValidator.class)
public @interface Identity {

    /**
     * 错误提示
     *
     * @return 错误提示信息
     */
    String message() default "{identity.invalid}";

    /**
     * 校验组
     *
     * @return 校验组
     */
    Class<?>[] groups() default {};

    /**
     * 有效负载
     *
     * @return 有效负载
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 身份证校验器
     */
    class IdentityValidator implements ConstraintValidator<Identity, String> {

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            return ValidationUtil.validateIdentity(value);
        }
    }
}
