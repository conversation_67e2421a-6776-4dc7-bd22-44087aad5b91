package com.trs.police.common.core.entity.node.comparable;

import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 数值类型
 *
 * <AUTHOR>
 */
public class NumberValue extends ComparableValue {

    private String value;

    public NumberValue(String value) {
        this.value = value;
    }

    @Override
    public String getValueString() {
        return value;
    }

    @Override
    public int compareTo(@NotNull String o) {
        if (Objects.isNull(value)) {
            return -1;
        }
        return BigDecimal.valueOf(Double.parseDouble(value)).compareTo(BigDecimal.valueOf(Double.parseDouble(o)));
    }

    @Override
    public Boolean isNull() {
        return null == value || value.isEmpty();
    }

    @Override
    public Boolean isEmpty() {
        return false;
    }
}
