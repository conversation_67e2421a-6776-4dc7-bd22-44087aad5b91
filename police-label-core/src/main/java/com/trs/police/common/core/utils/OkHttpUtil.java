package com.trs.police.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import okhttp3.Request.Builder;
import okhttp3.logging.HttpLoggingInterceptor;
import okhttp3.logging.HttpLoggingInterceptor.Level;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * okhttp工具类
 *
 * <AUTHOR>
 */
public class OkHttpUtil {

    OkHttpClient okHttpClient;

    Logger logger = LoggerFactory.getLogger(OkHttpUtil.class);

    private static final long CONNECT_TIMEOUT = 30L;

    private static final long READ_TIMEOUT = 30L;

    private static final long WRITE_TIMEOUT = 30L;

    private static final MediaType MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");

    private static final Integer OK = 200;

    private static final String OKHTTP_ERR_MSG = "okhttp接口访问失败！";


    private OkHttpUtil() {
        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor(s -> {
            try {
                logger.info("OkHttp ----- {}", s);
            } catch (Exception e) {
                logger.error("OkHttp ----- {}", s, e);
            }
        });

        //ssl
        final TrustManager[] trustAllCerts = createTrustManager();
        SSLSocketFactory sslSocketFactory = createSocketFactory(trustAllCerts);
        assert sslSocketFactory != null;

        interceptor.setLevel(Level.BODY);
        okHttpClient = new OkHttpClient().newBuilder()
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                .hostnameVerifier((s, sslSession) -> Boolean.TRUE)
                .sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0])
                .addNetworkInterceptor(interceptor)
                .build();

    }

    private SSLSocketFactory createSocketFactory(TrustManager[] trustAllCerts) {
        try {
            // Install the all-trusting trust manager
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception exception) {
            return null;
        }
    }

    private TrustManager[] createTrustManager() {
        // Create a trust manager that does not validate certificate chains
        return new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return new java.security.cert.X509Certificate[]{};
                    }
                }
        };
    }

    /**
     * okHttpUtil
     *
     * @return {@link OkHttpUtil}
     */
    public static OkHttpUtil getInstance() {
        return OkHttp3Holder.INSTANCE;
    }

    /**
     * 获取图片等二进制数据
     *
     * @param url 请求url
     * @return byte[]
     */
    public byte[] getBytes(String url) {
        Request request = new Builder().get().url(url).build();
        return getBytes(request);
    }

    @Nullable
    private byte[] getBytes(Request request) {
        try (ResponseBody responseBody = doRequest(request)) {
            if (Objects.isNull(responseBody)) {
                return null;
            }
            return responseBody.bytes();
        } catch (Exception e) {
            logger.error(OKHTTP_ERR_MSG, e);
            return null;
        }
    }

    private static class OkHttp3Holder {
        private static final OkHttpUtil INSTANCE = new OkHttpUtil();
    }

    /**
     * get方法（带header）
     *
     * @param url    请求url
     * @param header 请求头
     * @return response
     */
    public String getData(String url, Map<String, String> header) {
        Request request = new Builder().get().url(url).headers(addHeaders(header)).build();
        return getString(request);
    }

    /**
     * get方法（带header）
     *
     * @param jsonBody 请求参数
     * @param url      请求url
     * @param header   请求头
     * @return response
     */
    public String getData(String url, String jsonBody, Map<String, String> header) {
        final JSONObject json = JSON.parseObject(jsonBody);
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
        for (Map.Entry<String, Object> param : json.entrySet()) {
            if (Objects.nonNull(param.getValue())) {
                urlBuilder.addQueryParameter(param.getKey(), param.getValue().toString());
            }
        }
        Request request = new Builder()
                .get()
                .url(urlBuilder.build()).headers(addHeaders(header))
                .build();
        return getString(request);
    }

    /**
     * get方法
     *
     * @param url 请求url
     * @return response
     */
    public String getData(String url) {
        return getData(url, null);
    }

    @Nullable
    private String getString(Request request) {
        try (ResponseBody responseBody = doRequest(request)) {
            if (Objects.isNull(responseBody)) {
                return null;
            }
            return responseBody.string();
        } catch (Exception e) {
            logger.error(OKHTTP_ERR_MSG, e);
            return null;
        }
    }

    /**
     * post方法（带header）
     *
     * @param url         请求url
     * @param bodyJsonStr json字符串
     * @param header      请求头
     * @return response
     */
    public String postData(String url, String bodyJsonStr, Map<String, String> header) {
        Request request = new Builder()
                .post(setRequestBody(bodyJsonStr))
                .url(url)
                .headers(addHeaders(header))
                .build();
        return getString(request);
    }

    /**
     * post方法
     *
     * @param url         请求url
     * @param bodyJsonStr json字符串
     * @return response
     */
    public String postData(String url, String bodyJsonStr) {
        return postData(url, bodyJsonStr, null);
    }

    /**
     * put 方法
     *
     * @param url         地址
     * @param bodyJsonStr 参数
     * @param header      请求头
     * @return 结果
     */
    public String putData(String url, String bodyJsonStr, Map<String, String> header) {
        Request request = new Builder()
                .put(setRequestBody(bodyJsonStr))
                .url(url)
                .headers(addHeaders(header))
                .build();
        return getString(request);
    }

    private ResponseBody doRequest(Request request) {
        logger.info(" ---------------- remote http request: ---------------- ");
        Call call = okHttpClient.newCall(request);
        try {
            Response response = call.execute();
            if (response.body() == null) {
                logger.error("okhttp接口访问失败！response为空！,request:{}", request.body());
                return null;
            }

//            if (response.code() != OK) {
//                String errorMessage = String.format("okhttp接口访问失败！response: %s,request: %s", response.body().string(), request.body());
//                logger.error(errorMessage);
//                return null;
//            }
            return response.body();
        } catch (IOException e) {
            logger.error(OKHTTP_ERR_MSG, e);
            logger.error("request:{}", request.body());
            return null;
        }
    }

    private static RequestBody setRequestBody(String bodyJsonStr) {
        return StringUtils.isNotBlank(bodyJsonStr) ? RequestBody.create(MEDIA_TYPE, bodyJsonStr) : RequestBody.create(MEDIA_TYPE, new byte[0]);
    }

    private static Headers addHeaders(Map<String, String> header) {
        Headers.Builder builder = new Headers.Builder();
        if (header != null && !header.isEmpty()) {
            header.forEach(builder::add);
        }
        return builder.build();
    }
}
