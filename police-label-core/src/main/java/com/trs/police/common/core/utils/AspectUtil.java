package com.trs.police.common.core.utils;

import java.lang.reflect.Method;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;

/**
 * 切面工具方法
 *
 * <AUTHOR>
 */
public class AspectUtil {

    AspectUtil() {
    }

    /**
     * 抽取方法参数
     *
     * @param joinPoint {@link JoinPoint}
     * @return 参数map
     */
    public static Map<String, Object> generateParams(JoinPoint joinPoint) {
        Map<String, Object> params = new LinkedHashMap<>();
        // 通过joinPoint获取被注解方法
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        //获取参数值
        Object[] args = joinPoint.getArgs();
        //获取运行时参数的名称
        DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
        String[] parameterNames = discoverer.getParameterNames(method);
        for (int i = 0; i < Objects.requireNonNull(parameterNames).length; i++) {
            params.put(parameterNames[i], args[i]);
        }
        return params;
    }
}
