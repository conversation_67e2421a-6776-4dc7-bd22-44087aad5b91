package com.trs.police.common.core.utils;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2021/07/14
 */
@Slf4j
public class DateUtil {

    private DateUtil() {

    }

    /**
     * 时间戳转时间
     *
     * @param utc 时间戳
     * @return 时间对象
     */
    public static LocalDateTime utcToLocalDateTime(long utc) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(utc), ZoneId.systemDefault());
    }

    /**
     * 字符串转时间
     *
     * @param utc 时间戳
     * @return 时间对象
     */
    public static LocalDateTime utcToLocalDateTime(String utc) {
        return utcToLocalDateTime(utc, true);
    }

    /**
     * utcToLocalDateTime<BR>
     *
     * @param utc          参数
     * @param needCheckStr 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:36
     */
    public static LocalDateTime utcToLocalDateTime(String utc, Boolean needCheckStr) {
        if (!TimeUtils.isValid(utc)) {
            PreConditionCheck.checkArgument(!needCheckStr, "时间格式不合法");
            return null;
        }
        return utcToLocalDateTime(TimeUtils.stringToDate(utc).getTime());
    }

    /**
     * 时间戳转日期
     *
     * @param utc 时间戳
     * @return 日期对象
     */
    public static LocalDate utcToLocalDate(long utc) {
        return utcToLocalDateTime(utc).toLocalDate();
    }

    /**
     * 字符串转日期
     *
     * @param utc 时间戳
     * @return 日期对象
     */
    public static LocalDate utcToLocalDate(String utc) {
        return utcToLocalDateTime(utc, true).toLocalDate();
    }

    /**
     * 字符串转日期<BR>
     *
     * @param utc          参数
     * @param needCheckStr 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:35
     */
    public static LocalDate utcToLocalDate(String utc, Boolean needCheckStr) {
        return utcToLocalDateTime(utc, needCheckStr).toLocalDate();
    }

    /**
     * 时间转时间戳
     *
     * @param dateTime 时间
     * @return 时间戳
     */
    public static Long dateTimeToUtc(LocalDateTime dateTime) {
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 日期转时间戳
     *
     * @param date 日期
     * @return 时间戳
     */
    public static Long dateToUtc(LocalDate date) {
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 时间转字符串
     *
     * @param dateTime 时间
     * @return {@link String}
     */
    public static String dateTimeToString(LocalDateTime dateTime) {
        return dateTimeToString(dateTime, TimeUtils.YYYYMMDD_HHMMSS);
    }

    /**
     * 时间转字符串
     *
     * @param dateTime   时间
     * @param dateFormat 时间格式
     * @return {@link String}
     */
    public static String dateTimeToString(LocalDateTime dateTime, String dateFormat) {
        return TimeUtils.longToString(DateUtil.dateTimeToUtc(dateTime), dateFormat);
    }

    /**
     * 字符串转dateTime
     *
     * @param dateStr 时间字符串
     * @return LocalDateTime
     */
    public static LocalDateTime stringToDateTime(String dateStr) {
        return stringToDateTime(dateStr, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 字符串转dateTime
     *
     * @param dateStr    时间字符串
     * @param dateFormat 时间格式
     * @return LocalDateTime
     */
    public static LocalDateTime stringToDateTime(String dateStr, String dateFormat) {
        if (StringUtils.isNullOrEmpty(dateStr)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        return LocalDateTime.parse(dateStr, formatter);
    }

    /**
     * 根据出生年月日计算年龄
     *
     * @param birthday 生日
     * @return 年龄
     */
    public static int getAgeByBirth(LocalDate birthday) {
        if (Objects.isNull(birthday)) {
            return 0;
        }
        // 获取当前时间
        Calendar cal = Calendar.getInstance();

        // 获取出生日期的Calendar对象
        Calendar bir = Calendar.getInstance();
        bir.set(birthday.getYear(), birthday.getMonthValue(), birthday.getDayOfMonth());
        // 如果出生日期大于当前日期，则返回0
        if (cal.before(birthday)) {
            return 0;
        }
        // 取出当前年月日
        int nowYear = cal.get(Calendar.YEAR);
        int nowMonth = cal.get(Calendar.MONTH);
        int nowDay = cal.get(Calendar.DAY_OF_MONTH);

        // 取出出生日期的年月日
        int birthYear = bir.get(Calendar.YEAR);
        int birthMonth = bir.get(Calendar.MONTH);
        int birthDay = bir.get(Calendar.DAY_OF_MONTH);

        // 计算年份
        int age = nowYear - birthYear;

        // 计算月份和日，看看是否大于当前月日，如果小于则减去一岁
        if (nowMonth < birthMonth || (nowMonth == birthMonth && nowDay < birthDay)) {
            age--;
        }
        return age;
    }

    /**
     * 数字转时间格式字符串
     *
     * @param dateStr    时间字符串
     * @param dateFormat 时间格式
     * @return {@link String}
     */
    public static String numberToString(String dateStr, String dateFormat) {
        // 判断是否数字
        if (StringUtils.isNotEmpty(dateStr)) {
            if (dateStr.contains("-")) {
                return dateStr;
            }
            try {
                long l = Long.parseLong(dateStr);
                // 判断是否是秒还是毫秒
                if (l < 10000000000L) {
                    l *= 1000;
                }
                return TimeUtils.dateToString(new Date(l), dateFormat);
            } catch (NumberFormatException e) {
            }
        }

        return dateStr;
    }

    /**
     * Date转LocalDateTime
     *
     * @param date Date
     * @return LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * LocalDateTime转Date
     *
     * @param localDateTime localDateTime
     * @return Date
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当前月有多少天
     *
     * @return 当前月的天数
     */
    public static Integer getCurrentMonthDayNumber() {
        Calendar calendar = Calendar.getInstance();
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取指定日期的星期几（中文）
     *
     * @param date 日期字符串，格式为 "yyyy-MM-dd"
     * @return 星期几的中文名称
     */
    public static String getDayOfWeek(String date) {
        LocalDate localDate = LocalDate.parse(date);
        // 获取该日期对应的星期几，返回类型为 DayOfWeek 枚举
        java.time.DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        // 将 DayOfWeek 转换为中文的名称
        return dayOfWeek.getDisplayName(TextStyle.FULL, Locale.CHINESE);
    }
}
