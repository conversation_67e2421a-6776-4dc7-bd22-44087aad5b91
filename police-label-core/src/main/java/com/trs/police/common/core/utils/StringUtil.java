package com.trs.police.common.core.utils;

import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.regex.Pattern.compile;

/**
 * 字符串工具类
 *
 * <AUTHOR> yanghy
 * @date : 2022/8/25 9:44
 */
public class StringUtil {

    private StringUtil() {
    }

    public static final Integer LENGTH = 12;

    public static final String DEFAULT_STRING = "- -";

    public static final Pattern VALIDATE_ID_NUMBER_PATTERN = compile("^[0-9]{17}[0-9Xx]$");
    /**
     * 尾数校验位
     */
    private static final char[] LAST = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
    /**
     * 身份证前17位系数
     */
    private static final int[] COEFFICIENT = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

    /**
     * 黑色高亮
     */
    public static final String BLACK_HIGHLIGHT_FORMATTER= "<span style=\"color:#333333;\">%s</span>";


    /**
     * 判断是否为空
     *
     * @param str 待判断的字符串，不能为空
     * @return {@link boolean}
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 去除字符串中的特殊字符（防止sql注入）
     *
     * @param targetString 需要去除特殊字符的字符串
     * @return {@link String}
     */
    public static String removeSpecialCharacters(String targetString) {
        if (StringUtils.isBlank(targetString)) {
            return null;
        }
        return targetString.replace("\\", "\\\\")
            .replace("%", "\\%")
            .replace("_", "\\_");
    }

    /**
     * 获取派出所code前缀(去0)
     *
     * @param text 部门编号
     * @return 部门前缀
     */
    public static String getPrefixCode(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        if (compile("^(00)+$").matcher(text).find()) {
            return text;
        }
        return compile("(00)+$").matcher(text).replaceAll("");
    }

    /**
     * 增加派出所code后缀(在末尾加0)
     *
     * @param text 部门编号
     * @return 部门前缀
     */
    public static String addSuffixCode(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        } else if (text.length() < LENGTH) {
            StringBuilder textBuilder = new StringBuilder(text);
            textBuilder.append("0".repeat(Math.max(0, LENGTH - textBuilder.length())));
            text = textBuilder.toString();
        }
        return text;
    }

    /**
     * 判断字符串是否为空，为空返回 - -
     *
     * @param source 原字符串
     * @return {@link String}
     */
    public static String blank2Default(String source) {
        return StringUtils.isBlank(source) ? DEFAULT_STRING : source;
    }

    /**
     * 判断字符串是否为空，为空返回目标字符串
     *
     * @param source 原字符串
     * @param target 目标字符串
     * @return {@link String}
     */
    public static String blank2Target(String source, String target) {
        return StringUtils.isBlank(source) ? target : source;
    }

    /**
     * 将map的key转换为小写
     *
     * @param map 待转换的map
     * @return 小写key的map
     */
    public static Map<String, Object> toLowercaseKeyMap(Map<String, Object> map) {
        if (map == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> lowercaseMap = new HashMap<>(map.size());
        map.keySet().forEach(key -> lowercaseMap.put(key.toLowerCase(), map.get(key)));
        return lowercaseMap;
    }

    /**
     * 两个list 是否相等
     *
     * @param list1 list1
     * @param list2 list2
     * @param <T>   泛型
     * @return 是否相等
     */
    public static <T> Boolean equalList(List<T> list1, List<T> list2) {
        return (list1.size() == list2.size()) && list1.containsAll(list2) && list2.containsAll(list1);
    }

    /**
     * 判断child是否是parent的子部门
     *
     * @param parent 父
     * @param child  子
     * @return 布尔
     */
    public static Boolean isChildDept(String parent, String child) {
        if (StringUtils.isAnyEmpty(parent, child) || parent.equals(child)) {
            return false;
        }
        String parentPrefixCode = StringUtil.getPrefixCode(parent);
        String childPrefixCode = StringUtil.getPrefixCode(child);
        return childPrefixCode.startsWith(parentPrefixCode);
    }

    /**
     * 富文本简化
     *
     * @param richText 富文本字符串
     * @return 结果
     */
    public static String simplifyRichText(String richText) {
        if (StringUtils.isBlank(richText)) {
            return "";
        } else {
            return richText.replaceAll("<[^/].*?>", "")
                .replaceAll("</.*?>", "\n")
                .replaceAll("\n+", "\n")
                .replace("&nbsp;", " ");
        }
    }

    /**
     * 将数组转为 'x','x','x' 格式
     *
     * @param collection 集合
     * @return 返回格式化后的字符串
     */
    public static String listToString(Collection<?> collection) {
        return collection.stream().filter(Objects::nonNull).map(s -> "'" + s + "'")
                .collect(Collectors.joining(", "));
    }

    /**
     * 给集合中的元素加入前缀
     *
     * @param collection 集合
     * @param prefix     前缀
     * @return 加入前缀后的集合
     */
    public static List<String> addPrefix(Collection<?> collection, String prefix) {
        if (Objects.isNull(collection)) {
            return List.of();
        }
        return collection.stream().filter(Objects::nonNull).map(e -> prefix + e).collect(Collectors.toList());
    }

    /**
     * 判断是否合法手机号
     *
     * @param phone 手机号
     * @return 是否合法
     */
    public static boolean isLegalMobilePhone(String phone) {
        return StringUtils.isNotBlank(phone)
                && phone.matches("^1(3\\d|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8\\d|9[0-35-9])\\d{8}$");
    }
    /**
     * 判断身份证号码是否满足要求
     *
     * @param value 输入字符串
     * @return boolean 是否满足要求
     */
    public static boolean checkIdNumberPattern(String value) {

        final int lastIndex = 17;

        if (StringUtils.isBlank(value)
            || value.length() != 18
            || !VALIDATE_ID_NUMBER_PATTERN.matcher(value).matches()) {
            // 非18位直接返回false
            return false;
        }
        // 前17位加权和
        int sum = 0;
        for (int i = 0; i < lastIndex; i++) {
            if (!Character.isDigit(value.charAt(i))) {
                // 前17位必须是数字,如果不是直接返回false
                return false;
            }
            // 对前17位进行加权求和
            sum += Integer.parseInt(value.charAt(i) + "") * COEFFICIENT[i];
        }

        return value.charAt(lastIndex) == LAST[sum % LAST.length];
    }

    /**
     * 黑色高亮
     *
     * @param target 目标字符串
     * @return {@link String}
     */
    public static String blackHighlight(String target){
        return String.format(BLACK_HIGHLIGHT_FORMATTER,target);
    }

    /**
     * 黑色高亮
     *
     * @param target 目标字符串
     * @return {@link String}
     */
    public static String blackHighlight(Integer target){
        return String.format(BLACK_HIGHLIGHT_FORMATTER,target);
    }

    /**
     * 驼峰转下划线
     *
     * @param str str
     * @return {@link String}
     */
    public static String convertToUnderscore(String str) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isUpperCase(c)) {
                sb.append("_").append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 下划线转驼峰
     *
     * @param input input
     * @return {@link String}
     */
    public static String underlineToCamel(String input) {
        StringBuilder result = new StringBuilder();
        String[] words = input.split("_");
        for (int i = 0; i < words.length; i++) {
            String word = words[i];
            if (i == 0) {
                result.append(word.toLowerCase());
            } else {
                result.append(word.substring(0, 1).toUpperCase())
                        .append(word.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

    /**
     * 下划线转驼峰
     *
     * @param input input
     * @return {@link String}
     */
    public static String underlineToHump(String input) {
        StringBuilder result = new StringBuilder();
        if (!input.contains("_")) {
            return input;
        }
        String[] words = input.split("_");
        for (int i = 0; i < words.length; i++) {
            String word = words[i];
            if (i == 0) {
                result.append(word.toLowerCase());
            } else {
                result.append(word.substring(0, 1).toUpperCase())
                        .append(word.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

    /**
     * 字符串转为Integer列表
     *
     * @param commaSeparatedString commaSeparatedString
     * @return {@link List}<{@link Integer}>
     */
    public static List<Integer> convertToIntegerList(String commaSeparatedString) {
        return Arrays.stream(commaSeparatedString.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 获取地域短码
     *
     * @param areaCode 地域编码
     * @return {@link String}
     */
    public static String getShortAreaCode(String areaCode) {
        if (areaCode == null || areaCode.isEmpty()) {
            return "";
        }
        if (areaCode.length() > 6) {
            areaCode = areaCode.substring(0, 6);
        }
        return StringUtil.getPrefixCode(areaCode);
    }

    /**
     * 去除HTML标签
     *
     * @param html html
     * @return {@link String}
     */
    public static String stripHtmlTags(String html) {
        if (html == null || html.isEmpty()) {
            return html;
        }
        // Unescape HTML entities to their respective characters
        html = StringEscapeUtils.unescapeHtml4(html);

        // Regular expression to match HTML tags
        String regex = "<[^>]+>";

        // Replace all HTML tags with an empty string
        return html.replaceAll(regex, "");
    }

    /**
     * 取两个字符串的最长公共前缀长度
     * 可以通过两个deptCode判断是否具有上下级关系
     *
     * @param str1 第一个字符串
     * @param str2 第二个字符串
     * @return 最长公共前缀长度
     */
    public static int longestCommonPrefixLength(String str1, String str2) {
        int len1 = str1.length();
        int len2 = str2.length();
        int maxCycle = Math.min(len1, len2);
        for (int i = 0; i < maxCycle; i++) {
            if (str1.charAt(i) != str2.charAt(i)) {
                return i;
            }
        }
        return maxCycle;
    }

    /**
     * 取两个字符串的最长公共前缀
     *
     * @param str1 第一个字符串
     * @param str2 第二个字符串
     * @return 最长公共前缀
     */
    public static String longestCommonPrefix(String str1, String str2) {
        return str1.substring(longestCommonPrefixLength(str1, str2));
    }

    /**
     * 使用分号拆分字符串并返回集合
     *
     * @param splitStr 拆分字符串
     * @return 集合
     */
    public static List<Long> splitToLongList(String splitStr) {
        if (org.apache.commons.lang3.StringUtils.isBlank(splitStr)) {
            return Collections.emptyList();
        } else {
            return Arrays.stream(splitStr.split(";")).map(Long::parseLong).collect(Collectors.toList());
        }
    }

    /**
     * 使用分号拆分字符串并返回集合
     *
     * @param splitStr 拆分字符串
     * @return 集合
     */
    public static Map<String, String> splitToMap(String splitStr) {
        Map<String, String> map = new HashMap<>();
        if (isEmpty(splitStr)) {
            return Map.of();
        } else {
            // 根据特定分隔符解析字符串并填充map，例如使用“,”分隔键值对，使用“:”分隔键和值
            String[] pairs = splitStr.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    map.put(keyValue[0], keyValue[1]);
                }
            }
            return map;
        }
    }
}
