package com.trs.police.common.core.datasource;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DataPermissionInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.trs.police.common.core.datasource.handler.MybatisMetaObjectHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 */
@Configuration
public class DataSourceAutoConfigure {

    /**
     * 注册数据权限
     *
     * @return {@link DataPermissionInterceptor}
     */
    @Bean
    @Order(-1)
    public DataPermissionInterceptor dataPermissionInterceptor() {
        return new DataPermissionInterceptor();
    }

    /**
     * MyBatisPlus拦截器（用于分页）
     *
     * @return {@link MybatisPlusInterceptor}
     */
    @Bean
    @Order(-2)
    @ConditionalOnMissingBean(MybatisPlusInterceptor.class)
    public MybatisPlusInterceptor paginationInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }

    /**
     * Mybatis 自动填充（用于审计）
     *
     * @return {@link MybatisMetaObjectHandler}
     */
    @Bean
    @Order(-3)
    @ConditionalOnMissingBean(MetaObjectHandler.class)
    public MybatisMetaObjectHandler metaObjectHandler() {
        return new MybatisMetaObjectHandler();
    }
}
