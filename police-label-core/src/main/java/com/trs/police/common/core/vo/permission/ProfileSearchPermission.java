package com.trs.police.common.core.vo.permission;

import com.alibaba.fastjson.JSONArray;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 档案的数据权限
 *
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
public class ProfileSearchPermission implements Serializable {

    private String dbFileName;

    private List<Long> labelIds;

    private String labelIdStr;

    public ProfileSearchPermission(String dbFileName, List<Long> labelIds) {
        this.dbFileName = dbFileName;
        this.labelIds = labelIds;
        this.labelIdStr = Objects.isNull(labelIds) ? "[]" : JSONArray.toJSONString(labelIds);
    }
}
