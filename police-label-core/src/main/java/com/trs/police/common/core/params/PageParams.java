package com.trs.police.common.core.params;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分页请求参数类
 *
 * <AUTHOR>
 * @date 2021/07/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageParams implements Serializable {

    private static final long serialVersionUID = 6294573429165753649L;
    /**
     * 页号
     */
    @NotNull(message = "页码不能为空！")
    private Integer pageNumber = 1;

    /**
     * 页尺寸
     */
    @NotNull(message = "页大小不能为空！")
    private Integer pageSize = 10;


    /**
     * @return 偏移
     */
    @JsonIgnore
    public int getOffset() {
        return pageSize * (pageNumber - 1);
    }


    /**
     * pageParams转换Page
     *
     * @param <T> page类型
     * @return {@link Page}
     */
    public <T> Page<T> toPage() {
        Page<T> page = new Page<>(pageNumber, pageSize);
        //关闭自动sql优化
        page.setOptimizeCountSql(false);
        return page;
    }

    /**
     * 无需指定分页参数时，默认分页参数
     *
     * @return 默认分页参数
     */
    public static PageParams getDefaultPageParams() {
        return new PageParams(1, 20);
    }

    /**
     * 查询前10000条
     *
     * @return 默认分页参数
     */
    public static PageParams getAll() {
        return new PageParams(1, 10000);
    }
}
