package com.trs.police.common.core.utils;


import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池工具类
 */
public class ThreadPoolExecutorServiceUtil {

    //默认核心池数量
    final static private int DEFAULT_CORE_POOL_SIZE = 50;

    //默认最大线程数量
    final static private int DEFAULT_MAX_POOL_SIZE = 200;

    //默认空闲线程存货时间
    final static private long DEFAULT_ALIVE_TIME_MILLS = 2_000;

    //默认最大任务队列
    final static private int DEFAULT_QUEUE_TASK_SIZE = 500;


    //超过核心线程池后,超出的线程池如果空闲时间超过2秒,将会被线程池回收
    //默认拒绝策略:任务操作队列要求时,放弃任务队列中最老的任务
    private static final ThreadPoolExecutor DEFAULT_EXECUTOR = new ThreadPoolExecutor(DEFAULT_CORE_POOL_SIZE, DEFAULT_MAX_POOL_SIZE
            , DEFAULT_ALIVE_TIME_MILLS, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(DEFAULT_QUEUE_TASK_SIZE)
            , new ThreadPoolExecutor.DiscardOldestPolicy());

    /**
     * Description: 获取默认的线程池 <BR>
     *
     * @return {@link ThreadPoolExecutor}
     * <AUTHOR>    2020/7/10 10:04
     */
    public static ThreadPoolExecutor getInstance() {
        return DEFAULT_EXECUTOR;
    }

}
