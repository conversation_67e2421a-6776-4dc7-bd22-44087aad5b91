package com.trs.police.common.core;

import com.trs.police.common.core.handler.CommonAccessDeniedHandler;
import com.trs.police.common.core.handler.CommonAuthExceptionEntryPoint;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;

/**
 * <AUTHOR>
 * @date 2022/04/14
 */

@Configuration
public class PoliceCloudSecurityAutoConfigure {

    /**
     * 403
     *
     * @return {@link CommonAccessDeniedHandler}
     */
    @Bean
    @ConditionalOnMissingBean(value = AccessDeniedHandler.class)
    public CommonAccessDeniedHandler accessDeniedHandler() {
        return new CommonAccessDeniedHandler();
    }

    /**
     * 401
     *
     * @return {@link CommonAuthExceptionEntryPoint}
     */
    @Bean
    @ConditionalOnMissingBean(value = AuthenticationEntryPoint.class)
    public CommonAuthExceptionEntryPoint authenticationEntryPoint() {
        return new CommonAuthExceptionEntryPoint();
    }
}
