package com.trs.police.common.core.handler;

import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 通用的403类异常处理器
 *
 * <AUTHOR>
 * @date 2022/02/14
 */
@Slf4j
@Component
public class CommonAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
        AccessDeniedException accessDeniedException) throws IOException {
        String requestUri = request.getRequestURI();
        String message = "权限不合法！";
        log.error("客户端访问{}请求失败: {}", requestUri, message);
        ResponseMessage responseMessage = new ResponseMessage().success(false);
        CommonUtils.makeResponse(response, MediaType.APPLICATION_JSON_VALUE, HttpServletResponse.SC_FORBIDDEN,
            responseMessage.message("没有权限访问该资源"));
    }
}
