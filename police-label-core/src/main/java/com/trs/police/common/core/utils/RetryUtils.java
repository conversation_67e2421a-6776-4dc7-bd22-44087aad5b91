package com.trs.police.common.core.utils;

import com.trs.police.common.core.excpetion.ServiceException;

import java.util.concurrent.Callable;

/**
 * 重试工具类
 * *@author:wen.wen
 * *@create 2024-05-16 17:30
 **/
public class RetryUtils {


    /**
     * 当获取到的数据为空时执行一次重试
     *
     * @param callable 操作行为
     * @param delay    休眠时间
     * @param <T>      T
     * @return 数据
     * @throws ServiceException Exception
     */
    public static <T> T retryOnceWhenNull(Callable<T> callable, Integer delay) throws ServiceException {
        try {
            T data = callable.call();
            if (data == null) {
                Thread.sleep(delay);
                return callable.call();
            }
            return data;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
