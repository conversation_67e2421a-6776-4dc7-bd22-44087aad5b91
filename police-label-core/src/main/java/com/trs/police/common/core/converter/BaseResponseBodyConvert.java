package com.trs.police.common.core.converter;

import com.trs.police.common.core.annotation.SkipResponseBodyAdvice;
import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.utils.JsonUtil;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.Arrays;

import static com.trs.police.common.core.constant.FeignConstant.FEIGN_REQUEST_ID;

/**
 * 统一包装返回值
 *
 * <AUTHOR>
 * @since 2020/11/27 14:34
 */
public class BaseResponseBodyConvert implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType,
                            @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
        // 已经封装过了就不需要再封装一次
        return Arrays.stream(returnType.getMethodAnnotations())
                .noneMatch(s -> s.annotationType().isAssignableFrom(SkipResponseBodyAdvice.class))
                && !returnType.getParameterType().equals(ResponseMessage.class)
                && !returnType.getParameterType().getName().equals("com.trs.web.builder.base.RestfulResults")
                && !returnType.getParameterType().getName().equals("com.trs.web.builder.base.RestfulResultsV2");
    }

    @Override
    public Object beforeBodyWrite(
            Object o,
            @NonNull MethodParameter returnType,
            @NonNull MediaType selectedContentType,
            @NonNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
            @NonNull ServerHttpRequest request,
            @NonNull ServerHttpResponse response) {
        int status = ((ServletServerHttpResponse) response).getServletResponse().getStatus();
        if (status == HttpStatus.UNAUTHORIZED.value()) {
            return ResponseMessage.unauthorized(o);
        } else if (o instanceof ResponseMessage) {
            return o;
        }
        //如果是通过openfeign调用则不包装
        else if (request.getHeaders().containsKey(FEIGN_REQUEST_ID)) {
            return o;
        } else if (o instanceof String) {
            return JsonUtil.toJsonString(ResponseMessage.okWithStatus(status, o));
        } else {
            return ResponseMessage.okWithStatus(status, o);
        }
    }
}
