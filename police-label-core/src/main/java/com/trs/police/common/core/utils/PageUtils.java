package com.trs.police.common.core.utils;

import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/10/31 18:16
 * @since 1.0
 */
public class PageUtils {

    /**
     * subList<BR>
     *
     * @param pageNum     参数
     * @param pageSize    参数
     * @param doSomething 参数
     * @param <T>         参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:16
     */
    public static <T> List<T> subList(Integer pageNum, Integer pageSize, Supplier<List<T>> doSomething) {
        return subList(pageNum, pageSize, doSomething.get());
    }

    /**
     * subList<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param list     参数
     * @param <T>      参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:17
     */
    public static <T> List<T> subList(Integer pageNum, Integer pageSize, List<T> list) {
        if (list == null) {
            return Collections.emptyList();
        }
        int total = list.size();
        int start = (pageNum - 1) * pageSize;
        int end = pageNum * pageSize;
        if (start >= total) {
            return Collections.emptyList();
        } else {
            return list.subList(start, Math.min(end, total));
        }
    }
}
