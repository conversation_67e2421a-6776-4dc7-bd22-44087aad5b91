package com.trs.police.common.core.datasource.encrypt;

import com.trs.web.builder.base.IKey;

import java.util.List;

/**
 * 批量加密接口
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
public interface MutliEncrypt extends I<PERSON>ey {

    /**
     * 对字符串进行加密存储
     *
     * @param src 源
     * @return 返回加密后的密文
     * @throws RuntimeException 算法异常
     */
    String encrypt(String src);

    /**
     * 对已加密的字符串进行解密
     *
     * @param encryptList 已加密的字符串
     * @return 返回解密后的原文
     * @throws Exception e
     */
    List<String> decrypt(List<String> encryptList) throws Exception;
}
