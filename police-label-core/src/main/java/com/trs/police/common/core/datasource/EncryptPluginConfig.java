package com.trs.police.common.core.datasource;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.trs.police.common.core.datasource.encrypt.MutliEncrypt;
import com.trs.police.common.core.datasource.handler.MyDecryptReadInterceptor;
import com.trs.police.common.core.datasource.handler.MySensitiveAndEncryptWriteInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 数据库加密
 *
 * <AUTHOR>
 * @date 2024/8/1
 */
@Configuration
@ConditionalOnExpression("!'${com.trs.database.encrypt.type:}'.equals('')")
public class EncryptPluginConfig {
    @Value("${com.trs.database.encrypt.type}")
    private String encryptType;

    private List<MutliEncrypt> mutliEncryptList;

    public EncryptPluginConfig(List<MutliEncrypt> mutliEncryptList) {
        this.mutliEncryptList = mutliEncryptList;
    }

    //加密方式
    @Bean
    MutliEncrypt encryptor() throws Exception{
        MutliEncrypt multiEncrypt =  mutliEncryptList.stream().filter(encrypt -> encrypt.key().equals(encryptType)).findFirst().orElseThrow(() -> new Exception("未找到加密方式,"+encryptType));
        return multiEncrypt;
    }

    //配置插件
    @Bean
    ConfigurationCustomizer configurationCustomizer() throws Exception{
        MyDecryptReadInterceptor decryptReadInterceptor = new MyDecryptReadInterceptor(encryptor());
        MySensitiveAndEncryptWriteInterceptor sensitiveAndEncryptWriteInterceptor = new MySensitiveAndEncryptWriteInterceptor(encryptor());

        return (configuration) -> {
            configuration.addInterceptor(decryptReadInterceptor);
            configuration.addInterceptor(sensitiveAndEncryptWriteInterceptor);
        };
    }
}
