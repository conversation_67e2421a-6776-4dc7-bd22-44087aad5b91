package com.trs.police.common.core.constant;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *  * 字段类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum FieldTypeMapping {

    NUMBER("数字", "number"),
    STRING("字符串", "string"),
    DATETIME("时间", "datetime"),
    GEO("地理位置", "geo"),
    BOOLEAN("布尔", "boolean");

    private final String description;

    private String name;

    private static final Map<String, FieldTypeMapping> TYPE_MAPPING = new HashMap<>();

    static {
        // 数字类型映射
        TYPE_MAPPING.put("int", NUMBER);
        TYPE_MAPPING.put("integer", NUMBER);
        TYPE_MAPPING.put("bigint", NUMBER);
        TYPE_MAPPING.put("tinyint", NUMBER);
        TYPE_MAPPING.put("smallint", NUMBER);
        TYPE_MAPPING.put("decimal", NUMBER);
        TYPE_MAPPING.put("float", NUMBER);
        TYPE_MAPPING.put("double", NUMBER);

        // 字符串类型映射
        TYPE_MAPPING.put("char", STRING);
        TYPE_MAPPING.put("varchar", STRING);
        TYPE_MAPPING.put("text", STRING);
        TYPE_MAPPING.put("longtext", STRING);

        // 时间类型映射
        TYPE_MAPPING.put("date", DATETIME);
        TYPE_MAPPING.put("datetime", DATETIME);
        TYPE_MAPPING.put("timestamp", DATETIME);
        TYPE_MAPPING.put("time", DATETIME);

        TYPE_MAPPING.put("geo", GEO);
        TYPE_MAPPING.put("boolean", BOOLEAN);
    }

    FieldTypeMapping(String description, String name) {
        this.description = description;
        this.name = name;
    }

    /**
     * 根据类型名称获取标准字段类型
     *
     * @param fieldType 类型名称
     * @return 标准字段类型
     */
    public static FieldTypeMapping fromType(String fieldType) {
        String type = fieldType.toLowerCase();
        return TYPE_MAPPING.getOrDefault(type, STRING);
    }

    /**
     * 根据字段类型名称获取字段类型
     *
     * @param name 字段类型名称
     * @return 字段类型
     */
    public static FieldTypeMapping fromName(String name) {
        for (FieldTypeMapping value : values()) {
            if (value.name.equals(name)) {
                return value;
            }
        }
        return STRING;
    }
}
