package com.trs.police.common.core.utils;

import com.trs.police.common.core.constant.SystemConstant;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/7 17:25
 * @since 1.0
 */
public class FieldUtils {

    /**
     * reBuildFilter<BR>
     *
     * @param it 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 14:57
     */
    public static KeyValueTypeVO reBuildFilter(KeyValueTypeVO it) {
        switch (it.getType()) {
            case SystemConstant.SELECT:
            case SystemConstant.CHECKBOX:
                return new KeyValueTypeVO(
                        it.getKey(),
                        parseValueToList(it.getValue()),
                        SystemConstant.IN
                );
            case SystemConstant.ARRAY_FIND_IN_SET:
                return new KeyValueTypeVO(
                        it.getKey(),
                        parseValueToList(it.getValue()),
                        SystemConstant.FIND_IN_SET
                );
            default:
                return it;
        }
    }

    /**
     * parseValueToList<BR>
     *
     * @param value 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 10:08
     */
    private static Collection<?> parseValueToList(Object value) {
        if (value instanceof Collection) {
            return (Collection) value;
        } else if (value instanceof String) {
            return JsonUtil.parseArray(value.toString(), String.class);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * parseValueFromFilter<BR>
     *
     * @param filterParams 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 13:54
     */
    public static String parseValueFromFilter(List<KeyValueTypeVO> filterParams) {
        if (CollectionUtils.isNotEmpty(filterParams) && filterParams.size() == 1) {
            KeyValueTypeVO vo = filterParams.get(0);
            Object v = vo.getProcessedValue();
            if (v instanceof String) {
                return (String) v;
            } else if (v instanceof List) {
                var c = (List) v;
                if (CollectionUtils.isNotEmpty(c) && c.size() == 1) {
                    return Optional.ofNullable(c.get(0))
                            .map(Object::toString)
                            .orElse(null);
                }
            }
        }
        return null;
    }
}
