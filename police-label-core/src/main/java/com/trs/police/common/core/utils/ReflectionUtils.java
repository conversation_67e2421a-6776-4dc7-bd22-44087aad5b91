package com.trs.police.common.core.utils;

import com.trs.common.utils.TimeUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * @author: dingkeyu
 * @date: 2024/05/08
 * @description: 反射工具类
 */
public class ReflectionUtils {

    /**
     * getAllProperties
     *
     * @param object object
     * @param consumer consumer
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public static Map<String, Object> getAllProperties(Object object, Consumer<Map<String, Object>> consumer) {
        Map<String, Object> propertiesMap = new HashMap<>();
        Class<?> clazz = object.getClass();

        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    Object value = field.get(object);
                    if (Modifier.isStatic(field.getModifiers())) {
                        continue;
                    }
                    if (value instanceof LocalDateTime) {
                        value = ((LocalDateTime) value).format(DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS));
                    }
                    //if (value instanceof Boolean) {
                    //    value = ((Boolean) value) ? 1 : 0;
                    //}
                    propertiesMap.put(StringUtil.convertToUnderscore(field.getName()), value);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            clazz = clazz.getSuperclass();
        }
        if (consumer != null) {
            consumer.accept(propertiesMap);
        }
        return propertiesMap;
    }
}
