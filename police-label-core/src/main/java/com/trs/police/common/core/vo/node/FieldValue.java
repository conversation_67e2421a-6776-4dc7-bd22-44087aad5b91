package com.trs.police.common.core.vo.node;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 属性值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FieldValue {

    /**
     * 字段信息 (亨元模式，复用表头的)
     */
    @JsonIgnore
    private FieldInfoVO fieldInfo;

    /**
     * 值（均以字符串展示）
     */
    private String value;


    public FieldValue(String value, FieldInfoVO fieldInfo) {
        this.value = value;
        this.fieldInfo = fieldInfo;
    }

    /**
     * 唯一值
     *
     * @return 唯一值id
     */
    public String getId() {
        return fieldInfo.getId();
    }

    /**
     * 字段中文名称
     *
     * @return 中文
     */
    @JsonIgnore
    public String getEnName() {
        return fieldInfo.getEnName();
    }

    /**
     * 字段类型
     *
     * @return 类型编码
     */
    @JsonIgnore
    public String getTypeCode() {
        return fieldInfo.getTypeCode();
    }

    /**
     * 来源节点
     *
     * @return 来源节点
     */
    @JsonIgnore
    public String getFromNode() {
        return fieldInfo.getFromNode();
    }
}
