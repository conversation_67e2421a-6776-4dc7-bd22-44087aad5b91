package com.trs.police.common.core.vo.permission;

import com.trs.police.common.core.entity.CurrentUser;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * 用户-部门vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UserDeptVO implements Serializable {

    private static final long serialVersionUID = 893190810755736808L;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 角色id
     */
    private Long role;

    /**
     * 部门名称
     */
    private String deptName;

    public UserDeptVO(Long userId, Long deptId) {
        this.userId = userId;
        this.deptId = deptId;
    }

    /**
     * 转换
     *
     * @param currentUser {@link CurrentUser}
     * @return {@link UserDeptVO}
     */
    public static UserDeptVO of(CurrentUser currentUser) {
        if (currentUser != null) {
            return new UserDeptVO(currentUser.getId(), currentUser.getDeptId());
        }
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UserDeptVO)) {
            return false;
        }
        UserDeptVO that = (UserDeptVO) o;
        return Objects.equals(getUserId(), that.getUserId()) && Objects.equals(getDeptId(),
            that.getDeptId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getUserId(), getDeptId());
    }
}
