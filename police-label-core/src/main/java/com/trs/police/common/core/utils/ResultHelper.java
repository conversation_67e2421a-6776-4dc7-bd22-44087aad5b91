package com.trs.police.common.core.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.web.builder.base.RestfulResultsV2;

/**
 * 返回结果处理类
 *
 * <AUTHOR>
 * @date 2023/11/16
 */
public class ResultHelper {

    /**
     * 将分页结果转换为 RestfulResultsV2 对象
     *
     * @param <T>  数据类型
     * @param from 分页结果对象，包含分页数据和分页信息
     * @return 转换后的 RestfulResultsV2 对象，包含分页数据和分页元信息
     */
    public static <T> RestfulResultsV2<T> pageConvert2RestfulResults(IPage<T> from) {
        // 创建 RestfulResultsV2 对象并设置记录数据
        RestfulResultsV2<T> resultsV2 = RestfulResultsV2.ok(from.getRecords());
        // 设置当前页码
        resultsV2.addPageNum(Math.toIntExact(from.getCurrent()));
        // 设置每页记录数
        resultsV2.addPageSize(Math.toIntExact(from.getSize()));
        // 设置总记录数
        resultsV2.addTotalCount(from.getTotal());
        // 返回转换结果
        return resultsV2;
    }

}
