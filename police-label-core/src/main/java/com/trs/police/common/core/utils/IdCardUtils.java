package com.trs.police.common.core.utils;

import com.trs.common.utils.TimeUtils;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/25 16:27
 * @since 1.0
 */
public class IdCardUtils {
    /**
     * 从身份证号码中提取出生日期
     *
     * @param idCard 身份证号码
     * @return 出生日期，格式为YYYY-MM-DD
     */
    public static String getBirthDate(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return null;
        }
        String birthDate = idCard.substring(6, 14);
        return TimeUtils.stringToString(birthDate, TimeUtils.YYYYMMDD);
    }

    /**
     * 从身份证号码中判断性别
     *
     * @param idCard 身份证号码
     * @return 性别，"男"或"女"
     */
    public static String getGender(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return null;
        }
        char genderChar = idCard.charAt(16);
        int genderCode = Character.getNumericValue(genderChar);
        return (genderCode % 2 == 1) ? "男" : "女";
    }
}
