package com.trs.police.common.core.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/08/01
 * @description:
 */
public class GeoHashUtils {
    public final static double MAX_LAT = 90;
    public final static double MIN_LAT = -90;
    public final static double MAX_LNG = 180;
    public final static double MIN_LNG = -180;
    /**
     * 纬度二值串长度
     */
    private static int latLength;
    /**
     * 经度二值串长度
     */
    private static int lngLength;
    private final double latUnit = (MAX_LAT - MIN_LAT) / (1 << latLength);
    private final double lngUnit = (MAX_LNG - MIN_LNG) / (1 << lngLength);

    private final static String[] BASE32LOOKUP = {
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "b", "c", "d", "e", "f", "g", "h", "j", "k",
            "m", "n", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"
    };
    // 基准32个字符
    private static final char[] BASE32 = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b', 'c', 'd', 'e', 'f', 'g',
            'h', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};

    /**
     * 二值化：对经纬度二分逼近，大于中间值的为1，小于中间值的为0，将其转为长度为length的二值串
     *
     * @param min   区间最小值
     * @param max   区间最大值
     * @param value 经度或纬度
     * @param count 二分次数
     * @param list  二值串
     */
    private static void convert(double min, double max, double value, int count, List<Character> list) {
        if (list.size() > (count - 1)) {
            return;
        }
        double mid = (max + min) / 2;
        if (value < mid) {
            list.add('0');
            convert(min, mid, value, count, list);
        } else {
            list.add('1');
            convert(mid, max, value, count, list);
        }
    }

    /**
     * 将合并的二值串转为base32串
     *
     * @param str 合并的二值串
     * @return base32串
     */
    private static String base32Encode(final String str) {
        String unit = "";
        StringBuilder sb = new StringBuilder();
        for (int start = 0; start < str.length(); start = start + 5) {
            unit = str.substring(start, start + 5);
            sb.append(BASE32LOOKUP[convertToIndex(unit)]);
        }
        return sb.toString();
    }

    /**
     * 每五个一组将二进制转为十进制
     *
     * @param str 五个为一个unit
     * @return 十进制数
     */
    private static int convertToIndex(String str) {
        int length = str.length();
        int result = 0;
        for (int index = 0; index < length; index++) {
            result += str.charAt(index) == '0' ? 0 : 1 << (length - 1 - index);
        }
        return result;
    }

    /**
     * 经纬度二值串合并：偶数位放经度，奇数位放纬度，把2串编码组合生成新串
     *
     * @param lat 纬度
     * @param lng 经度
     * @return {@link String}
     */
    public static String encode(double lat, double lng) {
        if (latLength < 1 || lngLength < 1) {
            return "";
        }
        List<Character> latList = new ArrayList<>(latLength);
        List<Character> lngList = new ArrayList<>(lngLength);
        // 获取维度二值串
        convert(MIN_LAT, MAX_LAT, lat, latLength, latList);
        // 获取经度二值串
        convert(MIN_LNG, MAX_LNG, lng, lngLength, lngList);
        StringBuilder sb = new StringBuilder();
        for (int index = 0; index < latList.size(); index++) {
            sb.append(lngList.get(index)).append(latList.get(index));
        }
        // 如果二者长度不一样，说明要求的精度为奇数，经度长度比纬度长度大1
        if (lngLength != latLength) {
            sb.append(lngList.get(lngList.size() - 1));
        }

        return base32Encode(sb.toString());
    }

    /**
     * 根据精度获取GeoHash串
     *
     * @param precise 精度
     * @param lat     纬度
     * @param lng     经度
     * @return GeoHash串
     */
    public static String getGeoHash(double lat, double lng, int precise) {
        if (precise < 1 || precise > 9) {
            return "";
        }
        latLength = (precise * 5) / 2;
        if (precise % 2 == 0) {
            lngLength = latLength;
        } else {
            lngLength = latLength + 1;
        }
        return encode(lat, lng);
    }

    /**
     * 解析geoHash获取经纬度范围
     *
     * @param geoHash geoHash
     * @return 经纬度范围 double[0] 维度 double[1] 经度
     */
    public static double[] decode(String geoHash) {
        boolean isEven = true;
        double latMin = -90, latMax = 90;
        double lonMin = -180, lonMax = 180;

        for (char c : geoHash.toCharArray()) {
            int cd = -1;
            for (int i = 0; i < BASE32.length; i++) {
                if (BASE32[i] == c) {
                    cd = i;
                    break;
                }
            }

            // 初始化 mask 为 16 (即 2^4)，因为每个字符代表 5 位
            int mask = 16; // 这里是关键的修正
            for (int j = 0; j < 5; j++, mask >>= 1) {
                if (isEven) { // 经度
                    if ((cd & mask) != 0) {
                        lonMin = (lonMin + lonMax) / 2;
                    } else {
                        lonMax = (lonMin + lonMax) / 2;
                    }
                } else { // 纬度
                    if ((cd & mask) != 0) {
                        latMin = (latMin + latMax) / 2;
                    } else {
                        latMax = (latMin + latMax) / 2;
                    }
                }
                isEven = !isEven;
            }
        }

        return new double[]{(latMin + latMax) / 2, (lonMin + lonMax) / 2};
    }

}

