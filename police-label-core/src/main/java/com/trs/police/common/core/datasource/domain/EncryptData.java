package com.trs.police.common.core.datasource.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.reflection.MetaObject;

/**
 * 待解密的数据
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
@Data
@NoArgsConstructor
public class EncryptData {
    /**
     * MetaObject 对象
     */
    private MetaObject object;
    /**
     * 字段名称
     */
    private String field;
    /**
     * 解密list中取的字段开始位置
     */
    private Integer start;
    /**
     * 解密list中取的字段结束位置
     */
    private Integer end;

    public EncryptData(MetaObject object, String field) {
        this.object = object;
        this.field = field;
    }
}
