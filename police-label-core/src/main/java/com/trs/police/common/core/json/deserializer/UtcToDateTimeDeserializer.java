package com.trs.police.common.core.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.trs.police.common.core.utils.DateUtil;
import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 时间戳转时间类反序列化器
 *
 * <AUTHOR>
 * @date 2020/7/8
 **/
public class UtcToDateTimeDeserializer extends JsonDeserializer<LocalDateTime> implements ContextualDeserializer {

    private JavaType valueType;

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext context, BeanProperty property) {
        JavaType javaType = property.getType();
        UtcToDateTimeDeserializer deserializer = new UtcToDateTimeDeserializer();
        deserializer.valueType = javaType;
        return deserializer;
    }

    @Override
    public LocalDateTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
        throws IOException {
        long utc = jsonParser.getValueAsLong();
        if (valueType.getRawClass().equals(LocalDateTime.class)) {
            return DateUtil.utcToLocalDateTime(utc);
        } else {
            return null;
        }
    }
}
