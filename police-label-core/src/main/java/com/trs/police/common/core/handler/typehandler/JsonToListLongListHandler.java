package com.trs.police.common.core.handler.typehandler;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * json转List Long
 *
 * <AUTHOR>
 * @date 2025/01/14 10:00
 */
@Slf4j
public class JsonToListLongListHandler extends JacksonTypeHandler {

    @Override
    protected List<List<Long>> parse(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            List<List<Long>> result = objectMapper.readValue(json, new TypeReference<List<List<Long>>>() {});
            return result;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public JsonToListLongListHandler(Class<?> type) {
        super(type);
    }

    @Override
    public List<List<Long>> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return rs.getString(columnName) == null ? new ArrayList<>() : parse(rs.getString(columnName));
    }

    @Override
    public List<List<Long>> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return rs.getString(columnIndex) == null ? new ArrayList<>() : parse(rs.getString(columnIndex));
    }

    @Override
    public List<List<Long>> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return cs.getString(columnIndex) == null ? new ArrayList<>() : parse(cs.getString(columnIndex));
    }
}
