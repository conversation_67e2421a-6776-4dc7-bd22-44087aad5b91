package com.trs.police.common.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;

/**
 * @author: dingkeyu
 * @date: 2025/01/21
 * @description:
 */
@Slf4j
public class ImageUtil {

    /**
     * 根据图片url获取图片base64编码
     *
     * @param imageUrl 图片url
     * @return {@link String}
     */
    public static String getBase64FromImageUrl(String imageUrl) {
        if (StringUtil.isEmpty(imageUrl)) {
            return null;
        }
        String base64String = null;
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            // 将输入流转换为字节数组
            byte[] imageBytes = inputStream.readAllBytes();
            // 将字节数组编码为 Base64 字符串
            base64String = Base64.getEncoder().encodeToString(imageBytes);
        } catch (IOException e) {
            log.error("【{}】获取图片base64编码失败:", imageUrl, e);
        }
        return base64String;
    }
}
