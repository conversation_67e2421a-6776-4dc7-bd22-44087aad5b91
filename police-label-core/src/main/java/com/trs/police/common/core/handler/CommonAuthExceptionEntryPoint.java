package com.trs.police.common.core.handler;

import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 通用的401类异常处理器
 *
 * <AUTHOR>
 * @date 2022/02/14
 */
@Slf4j
@Component
public class CommonAuthExceptionEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
        AuthenticationException authException) throws IOException {
        String requestUri = request.getRequestURI();
        String message = "未授权的请求！";
        log.error("客户端访问{}请求失败: {}", requestUri, message);
        ResponseMessage responseMessage = new ResponseMessage().success(false);
        CommonUtils.makeResponse(response, MediaType.APPLICATION_JSON_VALUE, HttpServletResponse.SC_UNAUTHORIZED,
            responseMessage.message(message));
    }
}
