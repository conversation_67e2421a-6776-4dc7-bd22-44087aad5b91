package com.trs.police.common.core.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：对double类型四舍五入取整
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/10/8 18:49
 * @since 1.0
 */
public class RoundingDoubleSerializer extends JsonSerializer<Double> {

    private final int scale;

    public RoundingDoubleSerializer() {
        this(0);
    }

    public RoundingDoubleSerializer(int scale) {
        this.scale = scale;
    }

    @Override
    public void serialize(Double value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        if (Objects.isNull(value)) {
            gen.writeString("0");
        } else {
            gen.writeString(BigDecimal.valueOf(value).setScale(scale, RoundingMode.HALF_UP).toString());
        }
    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2024</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2024/11/28 22:37
     * @since 1.0
     */
    public static class RoundingScale1 extends RoundingDoubleSerializer {
        public RoundingScale1() {
            super(1);
        }
    }

}
