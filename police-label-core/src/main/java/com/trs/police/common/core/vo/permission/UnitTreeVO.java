package com.trs.police.common.core.vo.permission;

import com.trs.common.pojo.BaseVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * UnitTreeVO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/12/20 17:06
 * @since 1.0
 */
@Data
public class UnitTreeVO extends BaseVO {

    private String type;

    private String value;

    private String name;

    private List<UnitTreeVO> children = new ArrayList<>(0);

    /**
     * addChildren<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/20 17:10
     */
    public UnitTreeVO addChildren(UnitTreeVO vo) {
        if (vo != null) {
            List<UnitTreeVO> tmp = getChildren();
            tmp.add(vo);
            // 排序
            tmp.sort(Comparator.comparing(UnitTreeVO::getValue));
            setChildren(tmp);
        }
        return this;
    }

    /**
     * of<BR>
     *
     * @param type  参数
     * @param value 参数
     * @param name  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/2 19:09
     */
    public static UnitTreeVO of(String type, String value, String name) {
        UnitTreeVO vo = new UnitTreeVO();
        vo.setType(type);
        vo.setValue(value);
        vo.setName(name);
        return vo;
    }
}
