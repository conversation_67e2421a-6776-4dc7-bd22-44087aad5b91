package com.trs.police.common.core.datasource.encrypt;

import com.trs.police.common.core.utils.WcspsdkUtils;
import io.vavr.control.Try;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * wscpsdk加密
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
@Component
public class WcspsdkEncrypt implements MutliEncrypt{
    @SneakyThrows
    @Override
    public String encrypt(String src) {
        return WcspsdkUtils.sm4DataLightEncrypt(src);
    }

    @Override
    public List<String> decrypt(List<String> encryptList) throws Exception {
        // 兼容未解密的数据
        return encryptList.stream().map(encrypt ->
                        Try.of(() -> WcspsdkUtils.sm4DataLightDecrypt(encrypt)).getOrElse(encrypt))
                        .collect(Collectors.toList());
    }

    @Override
    public String key() {
        return "wcspsdk";
    }

    @Override
    public String desc() {
        return "wcspsdk";
    }
}
