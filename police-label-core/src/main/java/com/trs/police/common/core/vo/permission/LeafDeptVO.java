package com.trs.police.common.core.vo.permission;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.trs.police.common.core.vo.TreeVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 部门树包含所属区域路径和部门类型路径
 *
 * <AUTHOR> yanghy
 * @date : 2022/7/29 17:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeafDeptVO implements TreeVO, Serializable {

    private static final long serialVersionUID = -8866620994912090100L;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 简称
     */
    private String shortName;
    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 部门类型
     */
    private Long deptType;

    /**
     * 部门的子类别（业务类别）
     */
    private String childType;

    /**
     * 部门类型路径
     */
    private String areaCode;
    /**
     * 父类型
     */
    private Long pid;
    /**
     * 警种
     */
    private Long policeKind;
    /**
     * 路径
     */
    private String path;
    /**
     * 级别
     */
    private Integer level;
    /**
     * 子节点
     */
    private List<LeafDeptVO> children;

    private Boolean hasChild = false;

    private Boolean hasParent = false;

    private Boolean isLeaf = true;

    private Integer showOrder;

    private String districtName;

    /**
     * 部门所拥有的人员
     */
    private List<UserVO> userList;

    /**
     * 区域简称
     */
    private String districtShortName;

    /**
     * 区域主要名称
     */
    private String districtMainName;

    @Override
    public Object getId() {
        return this.getDeptId();
    }

    @Override
    public Long getPid() {
        return pid;
    }

    @Override
    public String getName() {
        return deptName;
    }
}
