package com.trs.police.common.core.valication;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;

/**
 * 枚举验证
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = Enum.EnumValidator.class)
public @interface Enum {

    /**
     * 校验失败提示信息
     *
     * @return 提示信息
     */
    String message() default "{enum.invalid}";

    /**
     * 校验组
     *
     * @return 校验组
     */
    Class<?>[] groups() default {};

    /**
     * 允许的值 为空将允许所有值
     *
     * @return 允许的枚举值
     */
    String[] allowValues() default {};

    /**
     * 是否忽略大小写
     *
     * @return 是否
     */
    boolean ignoreCase() default false;

    /**
     * 有效负载
     *
     * @return 有效负载
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 枚举值校验器
     */
    class EnumValidator implements ConstraintValidator<Enum, String> {

        private Set<String> allowValueSet;
        private boolean ignoreCase = false;

        @Override
        public void initialize(Enum constraintAnnotation) {

            ignoreCase = constraintAnnotation.ignoreCase();
            if (ignoreCase) {
                allowValueSet = Arrays.stream(constraintAnnotation.allowValues()).map(String::toUpperCase)
                    .collect(Collectors.toSet());

            } else {
                allowValueSet = Arrays.stream(constraintAnnotation.allowValues()).collect(Collectors.toSet());
            }

        }

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {

            if (ignoreCase) {
                return allowValueSet.contains(value.toUpperCase());
            } else {
                return allowValueSet.contains(value);
            }
        }
    }


}
