package com.trs.police.common.core.entity.node;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class TableFieldValue implements Value {

    private String filedName;

    private String fromNode;

    public TableFieldValue(String filedName, String fromNode) {
        this.filedName = filedName;
        this.fromNode = fromNode;
    }

    @Override
    public String getValueString() {
        return filedName;
    }

    @Override
    public Boolean isNull() {
        return false;
    }

    @Override
    public Boolean isEmpty() {
        return false;
    }
}
