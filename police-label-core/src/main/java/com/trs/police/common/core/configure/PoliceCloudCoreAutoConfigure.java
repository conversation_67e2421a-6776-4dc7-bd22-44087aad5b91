package com.trs.police.common.core.configure;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.trs.police.common.core.handler.CustomExceptionHandler;
import com.trs.police.common.core.utils.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @date 2022/04/18
 */
@MapperScan("com.trs.police.**.mapper")
@Configuration
@Slf4j
public class PoliceCloudCoreAutoConfigure {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 用Bean方式注入BeanUtil
     *
     * @return {@link BeanUtil}
     */
    @Bean
    public BeanUtil beanUtil() {
        return new BeanUtil();
    }

    /**
     * 用bean方式注入ExceptionHandler
     *
     * @return {@link CustomExceptionHandler}
     */
    @Bean
    public CustomExceptionHandler customExceptionHandler() {
        return new CustomExceptionHandler();
    }


    /**
     * 时间参数序列化/反序列化工具
     *
     * @return {@link Jackson2ObjectMapperBuilderCustomizer}
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            builder.serializerByType(LocalDateTime.class, new LocalDateTimeSerializer());
            builder.deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer());
        };
    }

    /**
     * kafka Kerberos认证配置
     *
     * @return KafkaKerberosAutoConfigure
     */
    @Bean
    public KafkaKerberosAutoConfigure kafkaKerberosAutoConfigure() {
        return new KafkaKerberosAutoConfigure(applicationContext.getEnvironment());
    }

    /**
     * 序列化
     *
     * <AUTHOR>
     */
    public static class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {

        @Override
        public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
            if (value != null) {
                long timestamp = value.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                gen.writeNumber(timestamp);
            }
        }
    }

    /**
     * 反序列化
     *
     * <AUTHOR>
     */
    public static class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            long timestamp = p.getValueAsLong();
            if (timestamp > 0) {
                return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
            } else {
                return null;
            }
        }
    }
}
