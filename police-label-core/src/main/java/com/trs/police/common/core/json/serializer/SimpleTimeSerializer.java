package com.trs.police.common.core.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.trs.police.common.core.utils.TimeUtil;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/18 14:08
 */
public class SimpleTimeSerializer extends JsonSerializer<LocalDateTime> {


    @Override
    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers)
        throws IOException {
            gen.writeString(Objects.isNull(value)? "- -": TimeUtil.getSimpleTime(value));
    }
}
