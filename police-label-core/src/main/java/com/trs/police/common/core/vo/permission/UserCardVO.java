package com.trs.police.common.core.vo.permission;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.CurrentUser;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息卡片vo
 *
 * <AUTHOR>
 */
@Data
public class UserCardVO implements Serializable {

    private static final long serialVersionUID = -3728005314865328277L;
    /**
     * 用户姓名
     */
    private String name;
    /**
     * 部门列表
     */
    private List<String> depts;
    /**
     * 联系方式
     */
    private String tel;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 转换
     *
     * @param dto {@link UserDto}
     * @return {@link UserCardVO}
     */
    public static UserCardVO of(UserDto dto) {
        UserCardVO cardVO = new UserCardVO();
        cardVO.setName(dto.getRealName());
        cardVO.setTel(dto.getTel());
        if (dto.getDeptList() != null) {
            cardVO.setDepts(dto.getDeptList().stream().map(DeptDto::getName).collect(Collectors.toList()));
        }
        return cardVO;
    }

    /**
     * 转换
     *
     * @param currentUser {@link CurrentUser}
     * @return {@link UserCardVO}
     */
    public static UserCardVO of(CurrentUser currentUser) {
        UserCardVO cardVO = new UserCardVO();
        cardVO.setName(currentUser.getRealName());
        cardVO.setDepts(List.of(currentUser.getDept().getName()));
        cardVO.setTel(currentUser.getMobile());
        return cardVO;
    }
}
