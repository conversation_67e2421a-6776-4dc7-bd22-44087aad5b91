package com.trs.police.common.core.utils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/21 15:14
 */
public class ListUtil {

    private ListUtil() {
    }

    /**
     * 合并两个list并去重
     *
     * @param list1 list
     * @param list2 list
     * @param <T>   泛型
     * @return 合并后的list
     */
    public static <T> List<T> mergeListAndRemoveDuplicates(List<T> list1, List<T> list2) {
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return new ArrayList<>();
        } else if (CollectionUtils.isEmpty(list1)) {
            return list2;
        } else if (CollectionUtils.isEmpty(list2)) {
            return list1;
        } else {
            list1.addAll(list2);
            return list1.stream().distinct().collect(Collectors.toList());
        }
    }


    /**
     * 根据对象属性字段排重
     *
     * @param keyExtractor 排重function
     * @param <T>          泛型
     * @return Predicate
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

}
