package com.trs.police.common.core.entity.node.comparable;

import org.jetbrains.annotations.NotNull;

import java.util.Objects;

/**
 * 数值类型
 *
 * <AUTHOR>
 */
public class StringValue extends ComparableValue {

    private String value;

    public StringValue(String value) {
        this.value = value;
    }

    @Override
    public String getValueString() {
        return value;
    }

    @Override
    public int compareTo(@NotNull String o) {
        if (Objects.isNull(value)) {
            return -1;
        }
        return value.compareTo(o);
    }

    @Override
    public Boolean isNull() {
        return null == value;
    }

    @Override
    public Boolean isEmpty() {
        return value.isEmpty();
    }
}
