package com.trs.police.common.core.entity.node.comparable;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.ValueType;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

import static com.trs.common.utils.TimeUtils.YYYYMMDD_HHMMSS;

/**
 *  时间值
 *
 * <AUTHOR>
 */
public class TimeValue extends ComparableValue {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private String type;

    private String value;

    public TimeValue(String type, String value) {
        this.type = type;
        this.value = value;
    }

    @Override
    public String getValueString() {
        if (ValueType.A_MONTH_AGO.equals(type)) {
            return LocalDateTime.now().minusMonths(1).format(TIME_FORMATTER);
        }
        if (ValueType.THREE_MONTHS_AGO.equals(type)) {
            return LocalDateTime.now().minusMonths(3).format(TIME_FORMATTER);
        }
        // n天前
        if (ValueType.A_FEW_DAYS_AGO.equals(type)) {
            int day = Integer.parseInt(value);
            return LocalDateTime.now().minusDays(day).format(TIME_FORMATTER);
        }
        // 自定义时间 验证格式是否正确 不正确抛出异常 正确直接返回
        return TimeUtils.stringToString(value, YYYYMMDD_HHMMSS);
    }

    @Override
    public int compareTo(@NotNull String o) {
        if (Objects.isNull(getValueString())) {
            return -1;
        }
        return LocalDateTime.parse(getValueString(), TIME_FORMATTER).compareTo(LocalDateTime.parse(o, TIME_FORMATTER));
    }

    @Override
    public Boolean isNull() {
        return value == null || value.isEmpty();
    }

    @Override
    public Boolean isEmpty() {
        return false;
    }
}
