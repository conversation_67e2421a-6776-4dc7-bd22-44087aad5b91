package com.trs.police.common.core.utils;

import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.text.StringSubstitutor;

import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2025/1/7 9:33
 */
@Slf4j
public class CloudControlUtil {

    public static final String UNIFIED_PATH = "/linkageServicePoint/bkldfw";

    private static final String CONFIG_PROPERTIES = "com.trs.subscription.st.cloud.control.";

    private static final String APP_ID = "app_id";

    private static final String APP_SECRET = "app_secret";

    private static final String ENDPOINT = "endpoint";

    private static final String URL_PREFIX = "urlPrefix";

    /**
     * status json path
     */
    private static final String STATUS_JSON_PATH = "$.header.status";

    /**
     * message json path
     */
    private static final String MESSAGE_JSON_PATH = "$.header.message";

    /**
     * token json path
     */
    private static final String TOKEN_JSON_PATH = "$.payload.token";


    private static final String SUCCESS_CODE = "200";

    private static final String SUCCESS_MESSAGE = "成功";


    private static OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
            .connectTimeout(400, TimeUnit.SECONDS)
            .readTimeout(400, TimeUnit.SECONDS)
            .writeTimeout(400, TimeUnit.SECONDS)
            .build();


    /**
     * 获取token
     * 通用省厅云控获取token实现
     *
     * @param urlPrefix 示例 http://ip:port
     * @param appId appId
     * @param appSecret appSecret
     *
     * @return 返回的token
     */
    public static String getToken(String urlPrefix, String appId, String appSecret) {
        if (urlPrefix == null) {
            log.error("未配置" + URL_PREFIX + " 使用默认值");
            urlPrefix = "http://************:7979";
        }
        if (appId == null) {
            log.error("未配置" + APP_ID + " 使用默认值");
            appId = "LDBK510002";
        }
        if (appSecret == null) {
            log.error("未配置" + APP_SECRET + " 使用默认值");
            appSecret = "APPSECLDBK510002";
        }
        String getTokenUrl = new StringSubstitutor(
                ImmutableMap.<String, String>builder()
                        .put(APP_ID, appId)
                        .put(APP_SECRET, appSecret)
                        .build()
        ).replace(
                urlPrefix + UNIFIED_PATH + "?" + APP_ID + "=${" + APP_ID + "}&" + APP_SECRET + "=${" + APP_SECRET + "}");
        final Request request =
                new Request.Builder()
                        .url(getTokenUrl)
                        .get()
                        .build();
        try (Response result = okHttpClient.newCall(request).execute();
             ResponseBody responseBody = result.body()) {
            final int responseCode = result.code();
            final String responseBodyTxt;
            if (responseBody != null) {
                responseBodyTxt = responseBody.string();
            } else {
                throw new RuntimeException("请求云控接口获取token okhttp错误,responseBody为空");
            }
            //检查推送结果
            String status = String.valueOf(JSONPath.read(responseBodyTxt, STATUS_JSON_PATH));
            String message = String.valueOf(JSONPath.read(responseBodyTxt, MESSAGE_JSON_PATH));
            if (!SUCCESS_CODE.equals(status) || !SUCCESS_MESSAGE.equals(message)) {
                log.error(
                        "尝试从云控平台获取token出错,url:{} ,responseCode:{} ,responseBody:{}",
                        getTokenUrl,
                        responseCode,
                        responseBodyTxt);
                throw new RuntimeException(
                        MessageFormat.format(
                                "尝试从云控平台获取token出错,http 状态码:{0} ,响应内容:{1}, 请求参数:{2}", responseCode, responseBodyTxt, getTokenUrl));
            }
            log.info("获取到的token 为 {}", JSONPath.read(
                    responseBodyTxt,
                    TOKEN_JSON_PATH));
            return String.valueOf(
                    JSONPath.read(
                            responseBodyTxt,
                            TOKEN_JSON_PATH));
        } catch (Exception e) {
            log.error("获取token失败", e);
            throw new RuntimeException("获取token失败");
        }
    }
}
