package com.trs.police.common.core.entity.node;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 控制参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ControlValue implements Value {

    /**
     * 控制参数名称
     */
    private String name;

    public ControlValue(String name) {
        this.name = name;
    }

    @Override
    public String getValueString() {
        return name;
    }

    @Override
    public Boolean isNull() {
        return false;
    }

    @Override
    public Boolean isEmpty() {
        return false;
    }
}
