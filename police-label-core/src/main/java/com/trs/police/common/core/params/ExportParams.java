package com.trs.police.common.core.params;

import com.trs.police.common.core.request.ListParamsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 布控列表导出
 *
 * <AUTHOR>
 * @date 2022/7/3 9:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportParams {

    /**
     * 布控id
     */
    private List<Long> ids;
    /**
     * 导出字段
     */
    private List<String> fieldNames;
    /**
     * 是否导出全部
     */
    private Boolean isAll = false;
    /**
     * 我的/全部
     */
    private String type;
    /**
     * 查询条件
     */
    private ListParamsRequest listParamsRequest;

    /**
     * 警种类型
     */
    private Integer policeKind;
}
