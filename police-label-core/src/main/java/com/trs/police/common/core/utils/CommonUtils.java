package com.trs.police.common.core.utils;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.entity.CurrentUser;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/02/14
 */
@Slf4j
public class CommonUtils {

    private CommonUtils() {

    }

    /**
     * 设置响应
     *
     * @param response    HttpServletResponse
     * @param contentType content-type
     * @param status      http状态码
     * @param value       响应内容
     * @throws IOException IOException
     */
    public static void makeResponse(HttpServletResponse response, String contentType, int status, Object value)
            throws IOException {
        response.setContentType(contentType);
        response.setStatus(status);
        response.getOutputStream().write(JsonUtil.toJsonString(value).getBytes());
    }

    /**
     * 填充实体审计字段
     *
     * @param baseEntity      实体
     * @param currentUser     用户信息
     * @param onlyUpdateField 是否只填充更新字段
     */
    public static void fillAuditField(AbstractBaseEntity baseEntity, CurrentUser currentUser, boolean onlyUpdateField) {
        if (Objects.isNull(baseEntity)) {
            log.info("填充审计字段失败，baseEntity为空！");
            return;
        }
        if (Objects.isNull(currentUser)) {
            log.info("填充审计字段失败，currentUser为空！");
            return;
        }
        baseEntity.setUpdateUserId(currentUser.getId());
        baseEntity.setUpdateDeptId(currentUser.getDeptId());
        if (onlyUpdateField) {
            return;
        }
        baseEntity.setCreateUserId(currentUser.getId());
        baseEntity.setCreateDeptId(currentUser.getDeptId());
    }

    /**
     * 拼接host跟path<BR>
     *
     * @param host 参数
     * @param path 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/24 17:51
     */
    public static String concatHostAndPath(String host, String path) {
        if (StringUtils.isEmpty(host) && StringUtils.isEmpty(path)) {
            return "";
        }
        // host为空时直接返回path
        if (StringUtils.isEmpty(host)) {
            return path;
        }
        // path为空时直接返回host
        if (StringUtils.isEmpty(path)) {
            return host;
        }
        // 如果path已经http://或https://开头时直接返回
        if (path.startsWith(StringUtils.STRING_PROTOCOL_HTTP)
                || path.startsWith(StringUtils.STRING_PROTOCOL_HTTPS)) {
            return path;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(host);
        if (!host.endsWith(StringUtils.STRING_DIVIDE_FLAG)) {
            sb.append(StringUtils.STRING_DIVIDE_FLAG);
        }
        if (path.startsWith(StringUtils.STRING_DIVIDE_FLAG)) {
            sb.append(path.substring(1));
        } else {
            sb.append(path);
        }
        return sb.toString();
    }
}
