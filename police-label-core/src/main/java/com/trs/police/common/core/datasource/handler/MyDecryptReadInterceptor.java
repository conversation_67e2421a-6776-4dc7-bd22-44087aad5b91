package com.trs.police.common.core.datasource.handler;

import com.chenhaiyang.plugin.mybatis.sensitive.annotation.EncryptField;
import com.chenhaiyang.plugin.mybatis.sensitive.annotation.SensitiveBinded;
import com.chenhaiyang.plugin.mybatis.sensitive.annotation.SensitiveEncryptEnabled;
import com.chenhaiyang.plugin.mybatis.sensitive.type.SensitiveType;
import com.chenhaiyang.plugin.mybatis.sensitive.type.SensitiveTypeRegisty;
import com.chenhaiyang.plugin.mybatis.sensitive.utils.PluginUtils;
import com.trs.police.common.core.datasource.domain.EncryptData;
import com.trs.police.common.core.datasource.encrypt.MutliEncrypt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ResultMap;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;


/**
 * 对响应结果进行拦截处理,对需要解密的字段进行解密
 *
 * <AUTHOR>
 */
@Intercepts({
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {java.sql.Statement.class})
})
@Slf4j
public class MyDecryptReadInterceptor implements Interceptor {

    private static final String MAPPED_STATEMENT="mappedStatement";

    private MutliEncrypt encrypt;
    public MyDecryptReadInterceptor(MutliEncrypt encrypt) {
        Objects.requireNonNull(encrypt,"encrypt should not be null!");
        this.encrypt = encrypt;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        final List<Object> results = (List<Object>)invocation.proceed();

        if (results.isEmpty()) {
            return results;
        }

        final ResultSetHandler statementHandler = PluginUtils.realTarget(invocation.getTarget());
        final MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        final MappedStatement mappedStatement = (MappedStatement)metaObject.getValue(MAPPED_STATEMENT);
        final ResultMap resultMap = mappedStatement.getResultMaps().isEmpty() ? null : mappedStatement.getResultMaps().get(0);

        Object result0 = results.get(0);
        if (result0 == null) {
            return results;
        }
        
        SensitiveEncryptEnabled sensitiveEncryptEnabled = result0.getClass().getAnnotation(SensitiveEncryptEnabled.class);
        if(sensitiveEncryptEnabled == null || !sensitiveEncryptEnabled.value()){
            return results;
        }

        final Map<String, EncryptField> sensitiveFieldMap = getSensitiveByResultMap(resultMap);
        final Map<String, SensitiveBinded> sensitiveBindedMap = getSensitiveBindedByResultMap(resultMap);

        if (sensitiveBindedMap.isEmpty() && sensitiveFieldMap.isEmpty()) {
            return results;
        }

        List<EncryptData> encryptDatas = new ArrayList<>();
        for (Object obj: results) {
            final MetaObject objMetaObject = mappedStatement.getConfiguration().newMetaObject(obj);
            for (Map.Entry<String, EncryptField> entry : sensitiveFieldMap.entrySet()) {
                String property = entry.getKey();
                Object value = objMetaObject.getValue(property);
                if (value != null) {
                    encryptDatas.add(new EncryptData(objMetaObject, property));
                }
            }
            for (Map.Entry<String, SensitiveBinded> entry : sensitiveBindedMap.entrySet()) {

                String property = entry.getKey();

                SensitiveBinded sensitiveBinded = entry.getValue();
                String bindPropety = sensitiveBinded.bindField();
                SensitiveType sensitiveType = sensitiveBinded.value();
                try {
                    String value = (String) objMetaObject.getValue(bindPropety);
                    String resultValue =  SensitiveTypeRegisty.get(sensitiveType).handle(value);
                    objMetaObject.setValue(property,resultValue);
                }catch (Exception e){
                    //ignore it;
                }
            }
        }
        multiDecrypt(encryptDatas);

        return results;
    }

    /**
     * 批量解密
     *
     * @param encryptDatas 带解密的数据
     */
    private void multiDecrypt(List<EncryptData> encryptDatas) throws Exception {
        // 封装要解密的数据
        List<String> encryptStrList = new ArrayList<>();
        int i = 0;
        for (EncryptData encryptData : encryptDatas) {
            Object value = encryptData.getObject().getValue(encryptData.getField());
            if (value instanceof CharSequence) {
                if (StringUtils.isEmpty((String) value)) {
                    continue;
                }
                encryptStrList.add((String) value);
                encryptData.setStart(i++);
                encryptData.setEnd(i);
            } else if (value instanceof List) {
                List list = (List) value;
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                if (!(list.get(0) instanceof CharSequence)) {
                    continue;
                }
                encryptData.setStart(i++);
                for (Object item : list) {
                    String itemStr = (String) item;
                    if (StringUtils.isEmpty(itemStr)) {
                        continue;
                    }
                    encryptStrList.add(itemStr);
                    encryptData.setEnd(i++);
                }
                if (encryptData.getEnd() == null) {
                    encryptData.setStart(null);
                } else {
                    i--;
                }
            }
        }
        // 批量解密
        List<String> decryptStrList = encrypt.decrypt(encryptStrList);
        // 赋值解密结果
        for (EncryptData encryptData : encryptDatas) {
            if (encryptData.getStart() != null && encryptData.getEnd() != null) {
                MetaObject object = encryptData.getObject();
                String field = encryptData.getField();
                Object value = encryptData.getObject().getValue(field);
                if (value instanceof CharSequence) {
                    object.setValue(field, decryptStrList.subList(encryptData.getStart(), encryptData.getEnd()).get(0));
                } else if (value instanceof List) {
                    object.setValue(field, decryptStrList.subList(encryptData.getStart(), encryptData.getEnd()));
                }
            }
        }
    }

    private Map<String,SensitiveBinded> getSensitiveBindedByResultMap(ResultMap resultMap) {
        if (resultMap == null) {
            return new HashMap<>(16);
        }
        Map<String, SensitiveBinded> sensitiveBindedMap = new HashMap<>(16);
        Class<?> clazz = resultMap.getType();
        for (Field field: clazz.getDeclaredFields()) {
            SensitiveBinded sensitiveBinded = field.getAnnotation(SensitiveBinded.class);
            if (sensitiveBinded != null) {
                sensitiveBindedMap.put(field.getName(), sensitiveBinded);
            }
        }
        return sensitiveBindedMap;
    }

    private Map<String, EncryptField> getSensitiveByResultMap(ResultMap resultMap) {
        if (resultMap == null) {
            return new HashMap<>(16);
        }

        return getSensitiveByType(resultMap.getType());
    }

    private Map<String, EncryptField> getSensitiveByType(Class<?> clazz) {
        Map<String, EncryptField> sensitiveFieldMap = new HashMap<>(16);

        for (Field field: clazz.getDeclaredFields()) {
            EncryptField sensitiveField = field.getAnnotation(EncryptField.class);
            if (sensitiveField != null) {
                sensitiveFieldMap.put(field.getName(), sensitiveField);
            }
        }
        return sensitiveFieldMap;
    }

    @Override
    public Object plugin(Object o) {
        return Plugin.wrap(o, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // ignore
    }
}
