package com.trs.police.common.core.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.io.WKTReader;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 区域几何形状VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class GeometryVO implements Serializable {

    private static final long serialVersionUID = -4023958996587366684L;

    /**
     * 类型 point polygon circle linestring
     */
    @NotBlank(message = "类型不能为空")
    private String type;

    /**
     * wkt
     */
    @NotBlank(message = "wkt不能为空")
    private String geometry;


    /**
     * 扩展属性
     */
    private Properties properties;

    /**
     * 封装属性字段
     */
    @Data
    public static class Properties implements Serializable {

        private static final long serialVersionUID = 6808842480876447978L;

        /**
         * 圆形半径（米）
         */
        private Double radius;
        /**
         * 线段宽度（米）
         */
        private Integer width;
    }


    /**
     * 转换为moye 的wkt格式
     *
     * @return wkt
     */
    public List<String> toMoyeString() {
        String circle = "Circle";
        String geometryCollection = "GeometryCollection";
        if (circle.equalsIgnoreCase(type)) {
            return toCircle();
        } else if (geometryCollection.equalsIgnoreCase(type)) {
            return toPolygonList();
        } else {
            return List.of(geometry);
        }
    }

    /**
     * 特殊处理Circle类型，处理成自定义的 CIRCLE(:lat :lng :radius) 格式WKT字符串
     *
     * @return wkt
     */
    @NotNull
    private List<String> toCircle() {
        Double radius = properties.getRadius();
        return List.of(geometry.replace("POINT", "CIRCLE").replace(")", String.format(",%s)", radius)));
    }

    /**
     * 特殊处理GeometryCollection类型，处理成Polygon数组格式wkt字符串
     *
     * @return wkt
     */
    private List<String> toPolygonList() {
        List<String> result = new ArrayList<>();
        try {
            WKTReader reader = new WKTReader(new GeometryFactory());
            Geometry geometryCollection = reader.read(geometry);
            for (int i = 0; i < geometryCollection.getNumGeometries(); i++) {
                Geometry multiPolygon = geometryCollection.getGeometryN(i);
                for (int j = 0; j < multiPolygon.getNumGeometries(); j++) {
                    Geometry polygon = multiPolygon.getGeometryN(j);
                    result.add(polygon.toString());
                }
            }
            return result;
        } catch (Exception exception) {
            return List.of();
        }
    }
}