package com.trs.police.common.core.entity.node;

/**
 * 正则
 *
 * <AUTHOR>
 */
public class RegularValue implements Value {

    private String regular;

    public RegularValue(String regular) {
        this.regular = regular;
    }

    @Override
    public String getValueString() {
        return regular;
    }

    @Override
    public Boolean isNull() {
        return false;
    }

    @Override
    public Boolean isEmpty() {
        return false;
    }
}
