package com.trs.police.common.core.utils;

import com.trs.police.common.core.vo.GeometryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.locationtech.proj4j.*;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/10/08
 */
@Slf4j
public class GeoUtils {

    /**
     * 创建CRS对象
     */
    private static final CoordinateReferenceSystem WGS_84 = new CRSFactory().createFromName("epsg:4326");
    private static final CoordinateReferenceSystem MERCATOR = new CRSFactory().createFromName("epsg:3857");

    /**
     * 创建Geometry工厂对象
     */
    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();

    private static GeoUtils instance;

    private final Set<String> chinaWkts;

    private GeoUtils() {
        this.chinaWkts = loadChinaWkts();
    }

    /**
     * getInstance<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 11:42
     */
    public static GeoUtils getInstance() {
        if (instance == null) {
            synchronized (GeoUtils.class) {
                if (instance == null) {
                    instance = new GeoUtils();
                }
            }
        }
        return instance;
    }

    /**
     * makePointByLongitudeLatitude<BR>
     *
     * @param jd 参数
     * @param wd 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/9/15 16:06
     */
    public static String makePointByLongitudeLatitude(Double jd, Double wd) {
        if (jd == null || wd == null) {
            return null;
        }
        return "POINT(" + jd + " " + wd + ")";
    }

    /**
     * 判断点是否在区域内
     *
     * @param point    点wkt字符串
     * @param polygons 区域wkt字符串集合
     * @return 点是否在多边形内
     * @throws ParseException wkt解析异常
     */
    public static Boolean isPointInGeometry(String point, String polygons) {
        return isPointInGeometry(point, Collections.singleton(polygons));
    }

    /**
     * 判断点是否在区域内
     *
     * @param point    点wkt字符串
     * @param polygons 区域wkt字符串集合
     * @return 点是否在多边形内
     * @throws ParseException wkt解析异常
     */
    public static Boolean isPointInGeometry(String point, Set<String> polygons) {
        if (StringUtils.isEmpty(point)) {
            return Boolean.FALSE;
        }
        try {
            WKTReader wktReader = new WKTReader();
            for (String polygon : polygons) {
                if (StringUtils.startsWith(polygon, "CIRCLE")) {
                    String center = StringUtils.substringBetween(polygon, "(", ",");
                    String pointStr = StringUtils.substringBetween(point, "(", ")");
                    double distance = getDistance(
                            Double.parseDouble(center.split(" ")[1]),
                            Double.parseDouble(center.split(" ")[0]),
                            Double.parseDouble(pointStr.split(" ")[1]),
                            Double.parseDouble(pointStr.split(" ")[0]));
                    double radius = Double.parseDouble(StringUtils.substringBetween(polygon, ",", ")"));
                    if (distance < radius) {
                        return Boolean.TRUE;
                    }
                } else {
                    final Geometry polygonGeometry = wktReader.read(polygon);
                    if (polygonGeometry.isWithinDistance(wktReader.read(point), 0.0)) {
                        return Boolean.TRUE;
                    }
                }
            }
        } catch (Throwable t) {
            log.error("计算[{}],[{}]异常", point, polygons, t);
        }
        return Boolean.FALSE;
    }

    /**
     * getDistance<BR>
     *
     * @param lat1 参数
     * @param lon1 参数
     * @param lat2 参数
     * @param lon2 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 11:07
     */
    public static double getDistance(Double lat1, Double lon1, Double lat2, Double lon2) {
        double latOne = toRadians(lat1);
        double latTwo = toRadians(lat2);
        double latDiff = toRadians(lat2 - lat1);
        double lonDiff = toRadians(lon2 - lon1);

        double a =
                Math.sin(latDiff / 2) * Math.sin(latDiff / 2) + Math.cos(latOne) * Math.cos(latTwo) * Math.sin(lonDiff / 2)
                        * Math.sin(lonDiff / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        DecimalFormat format = new DecimalFormat("0.00");
        return Double.parseDouble(format.format(6371e3 * c));
    }

    /**
     * wgs84转Mercator
     *
     * @param wkt wkt字符串
     * @return mercator字符串
     */
    public static String wgs84ToMercator(String wkt) {
        try {
            // 解析WKT字符串并创建CRS对象
            Point srcPoint = (Point) new WKTReader(GEOMETRY_FACTORY).read(wkt);
            Coordinate converted = convert(new Coordinate(srcPoint.getX(), srcPoint.getY()), WGS_84, MERCATOR);
            return WKTWriter.toPoint(converted);
        } catch (ParseException e) {
            log.error("point parse error! wkt:{}", wkt, e);
        }
        return null;
    }

    /**
     * Mercator转wgs84
     *
     * @param wkt wkt字符串
     * @return 结果
     * @throws ParseException 转换异常
     */
    public static String mercatorToWgs84(String wkt) throws ParseException {
        // 解析WKT字符串并创建CRS对象
        Point srcPoint = (Point) new WKTReader(GEOMETRY_FACTORY).read(wkt);
        Coordinate converted = convert(new Coordinate(srcPoint.getX(), srcPoint.getY()), MERCATOR, WGS_84);
        return WKTWriter.toPoint(converted);
    }

    @NotNull
    private static Coordinate convert(Coordinate srcPoint, CoordinateReferenceSystem srcCrs,
                                      CoordinateReferenceSystem targetCrs) {
        // 创建坐标转换对象
        CoordinateTransform transform = new BasicCoordinateTransform(srcCrs, targetCrs);
        // 定义原始WGS84坐标点
        ProjCoordinate srcCoordinate = new ProjCoordinate(srcPoint.getX(), srcPoint.getY());
        // 目标Mercator坐标点
        ProjCoordinate targetCoordinate = new ProjCoordinate();
        // 进行坐标转换
        transform.transform(srcCoordinate, targetCoordinate);
        // 输出WKT字符串
        return new Coordinate(doubleScale(targetCoordinate.x), doubleScale(targetCoordinate.y));
    }

    /**
     * 折线处理为线段
     *
     * @param lineString 折线
     * @return 线段数组
     */
    public static List<List<Coordinate>> lineStringToCoordinate(LineString lineString) {
        List<List<Coordinate>> segments = new ArrayList<>();
        for (int i = 0; i < lineString.getNumPoints() - 1; i++) {
            List<Coordinate> segment = List.of(lineString.getCoordinateN(i), lineString.getCoordinateN(i + 1));
            segments.add(segment);
        }
        return segments;
    }

    /**
     * wkt转点位
     *
     * @param wkt wkt
     * @return 结果
     */
    public static Coordinate wktToCoordinate(String wkt) {
        try {
            Point srcPoint = (Point) new WKTReader(GEOMETRY_FACTORY).read(wkt);
            return new Coordinate(srcPoint.getX(), srcPoint.getY());
        } catch (ParseException e) {
            log.error("wkt read to point error! string: {}", wkt);
            return new Coordinate(Coordinate.NULL_ORDINATE, Coordinate.NULL_ORDINATE);
        }
    }

    /**
     * 线段端点生成矩形
     *
     * @param p1     端点1
     * @param p2     端点2
     * @param height 宽度
     * @return 结果
     */
    public static Polygon lineToRectangle(Coordinate p1, Coordinate p2, Integer height) {

        String p1Str = wgs84ToMercator("POINT(" + p1.getX() + " " + p1.getY() + ")");
        String p2Str = wgs84ToMercator("POINT(" + p2.getX() + " " + p2.getY() + ")");
        // 矩形
        Coordinate c1 = wktToCoordinate(p1Str);
        Coordinate c2 = wktToCoordinate(p2Str);
        Coordinate[] coordinates = lineToRectangleCoordinates(c1, c2, height);
        return GEOMETRY_FACTORY.createPolygon(
                Arrays.stream(coordinates).map(c -> convert(c, MERCATOR, WGS_84)).toArray(Coordinate[]::new));
    }

    /**
     * 线段拓展矩形
     *
     * @param p1     线段顶点1
     * @param p2     线段顶点2
     * @param height 线段宽度
     * @return 矩形顶点坐标
     */
    public static Coordinate[] lineToRectangleCoordinates(Coordinate p1, Coordinate p2, Integer height) {

        // 计算线段的长度和方向向量
        double length = p1.distance(p2);
        double dx = (p2.x - p1.x) / length;
        double dy = (p2.y - p1.y) / length;

        // 计算垂线方向向量
        double nx = -dy;
        double ny = dx;

        // 计算矩形的四个顶点坐标
        Coordinate vertexA = new Coordinate((p1.getX() + nx * height), (p1.getY() + ny * height));
        Coordinate vertexB = new Coordinate((p1.getX() - nx * height), (p1.getY() - ny * height));
        Coordinate vertexC = new Coordinate((p2.getX() + nx * height), (p2.getY() + ny * height));
        Coordinate vertexD = new Coordinate((p2.getX() - nx * height), (p2.getY() - ny * height));

        //构造矩形
        return new Coordinate[]{vertexA, vertexC, vertexD, vertexB, vertexA};
    }

    /**
     * 计算double
     *
     * @param number double
     * @return 结果
     */
    public static double doubleScale(Double number) {
        return BigDecimal.valueOf(number).setScale(6, RoundingMode.HALF_EVEN).doubleValue();
    }

    /**
     * Polygon list to MultiPolygon
     *
     * @param polygons 列表
     * @return 结果
     */
    public static MultiPolygon toMultiPolygon(List<Polygon> polygons) {
        return new MultiPolygon(polygons.toArray(new Polygon[0]), GEOMETRY_FACTORY);
    }

    /**
     * 折线转换为矩形列表
     *
     * @param vo GeometryVO
     * @return Polygon
     */
    public static List<Polygon> lineVoToPolygonList(GeometryVO vo) {
        if (vo.getProperties() == null || vo.getProperties().getWidth() == null || vo.getProperties().getWidth() <= 0) {
            log.error("线段宽度有误！geometry：{}", vo);
            return Collections.emptyList();
        }
        try {
            WKTReader reader = new WKTReader(GEOMETRY_FACTORY);
            LineString lineString = (LineString) reader.read(vo.getGeometry());
            List<List<Coordinate>> linePoints = GeoUtils.lineStringToCoordinate(lineString);
            return linePoints.stream().map(linePoint ->
                            GeoUtils.lineToRectangle(linePoint.get(0), linePoint.get(1), vo.getProperties().getWidth()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("线段转换矩形失败！", e);
        }
        return Collections.emptyList();
    }

    /**
     * toRadians<BR>
     *
     * @param angle 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 11:06
     */
    public static double toRadians(Double angle) {
        double result = 0L;
        if (angle != null) {
            result = angle * Math.PI / 180;
        }
        return result;
    }

    /**
     * loadChinaWkts<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 11:48
     */
    private Set<String> loadChinaWkts() {
        var buf = new BufferedReader(new InputStreamReader(Objects.requireNonNull(
                getClass().getClassLoader().getResourceAsStream("wkts/china.txt"),
                "加载内置wkt出错"
        )));
        return buf.lines().collect(Collectors.toSet());
    }

    /**
     * pointInGeometry<BR>
     *
     * @param point                  参数
     * @param convertOnInGeometry    参数
     * @param convertOnNotInGeometry 参数
     * @param <T>                    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 11:41
     */
    public <T> T pointInInlineGeometry(String point, Function<String, T> convertOnInGeometry, Function<String, T> convertOnNotInGeometry) {
        return isPointInGeometry(point, chinaWkts) ? convertOnInGeometry.apply(point) : convertOnNotInGeometry.apply(point);
    }

    /**
     * pointInGeometry<BR>
     *
     * @param point                  参数
     * @param convertOnNotInGeometry 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 11:41
     */
    public String pointInInlineGeometry(String point, Function<String, String> convertOnNotInGeometry) {
        return pointInInlineGeometry(point, it -> it, convertOnNotInGeometry);
    }

    /**
     * pointInGeometry<BR>
     *
     * @param point 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 12:01
     */
    public String pointInInlineGeometry(String point) {
        return pointInInlineGeometry(point, it -> it, it -> "");
    }
}
