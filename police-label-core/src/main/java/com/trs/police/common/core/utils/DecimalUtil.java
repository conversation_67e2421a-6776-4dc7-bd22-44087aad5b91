package com.trs.police.common.core.utils;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/18 18:37
 */
public class DecimalUtil {

    private DecimalUtil(){

    }

    private static final DecimalFormat DECIMAL_FORMATTER;

    static {
        DECIMAL_FORMATTER = new DecimalFormat("0.0000");
        DECIMAL_FORMATTER.setRoundingMode(RoundingMode.DOWN);
    }

    /**
     * double字符串保留4位小数
     *
     * @param source 字符串
     * @return {@link String}
     */
    public static String keepFourDecimals(String source) {
        return keepFourDecimals(Double.parseDouble(source));
    }

    /**
     * double字符串保留4位小数
     *
     * @param source double类型
     * @return {@link String}
     */
    public static String keepFourDecimals(Double source) {
        return DECIMAL_FORMATTER.format(source);
    }


    /**
     * 数字转中文数字
     *
     * @param section 数字
     * @return {@link String}
     */
    public static String num2Chinese(int section) {
        if (section >= 10 && section < 20)
            return "十" + num2Chinese(section % 10);
        String[] chnNumChar = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] chnUnitChar = {"", "十", "百", "千"};
        StringBuilder chnStr = new StringBuilder();
        StringBuilder strIns = new StringBuilder();
        int unitPos = 0;
        boolean zero = true;
        while (section > 0) {
            int v = section % 10;
            if (v == 0) {
                if (!zero) {
                    zero = true;
                    chnStr.append(chnNumChar[v]).append(chnStr);
                }
            } else {
                zero = false;
                strIns.delete(0, strIns.length());
                strIns.append(chnNumChar[v]);
                strIns.append(chnUnitChar[unitPos]);
                chnStr.insert(0, strIns);
            }
            unitPos++;
            section = (int) Math.floor(section / 10f);
        }
        return chnStr.toString();
    }
}
