package com.trs.police.common.core.configure;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import java.io.File;
import java.io.IOException;
import java.util.stream.Collectors;

/**
 * kafka 启用Kerberos认证的启动类
 * *@author:wen.wen
 * *@create 2024-08-30 15:18
 **/
@Slf4j
public class KafkaKerberosAutoConfigure {

    private final Environment env;

    public KafkaKerberosAutoConfigure(Environment env) {
        this.env = env;
        initSecurity();
    }


    /**
     * 初始化Kerberos相关认证信息到环境变量中
     */
    private void initSecurity() {
        if (StringUtils.isNotBlank(getKrb5Path())
                && StringUtils.isNotBlank(getKeytabPath())
                && StringUtils.isNotBlank(getPrincipal())) {

            String jaasContent = Lists.newArrayList(
                    "KafkaClient {",
                    "com.sun.security.auth.module.Krb5LoginModule required",
                    "useKeyTab=true",
                    "keyTab=\"" + getKeytabPath() + "\"",
                    "principal=\"" + getPrincipal() + "\"",
                    "useTicketCache=false",
                    "storeKey=true",
                    "debug=true;",
                    "};"
            ).stream().collect(Collectors.joining(System.getProperty("line.separator")));

            log.info("jaasContent is {}", jaasContent);

            final String jaasPath =
                    System.getProperty("java.io.tmpdir") + File.separator + System.getProperty("user.name") + ".jaas.conf";
            try {
                FileUtils.write(new File(jaasPath), jaasContent, "UTF-8", false);
            } catch (IOException e) {
                throw new RuntimeException("创建jaas文件失败", e);
            }
            System.setProperty("java.security.auth.login.config", jaasPath);
            System.setProperty("java.security.krb5.conf", getKrb5Path());
        } else {
            log.info("当前环境不需要初始化 kafka 安全配置");
        }
    }

    /**
     * 获取krb5.conf文件
     *
     * @return krb5.conf文件
     */
    private String getKrb5Path() {
        String property = env.getProperty("kafka.kerberos.krb5Path");
        return com.trs.common.utils.StringUtils.isEmpty(property)
                ? System.getProperty("kafka_kerberos_krb5Path")
                : property;
    }

    /**
     * 获取user.keytab文件
     *
     * @return user.keytab文件
     */
    private String getKeytabPath() {
        String property = env.getProperty("kafka.kerberos.userKeytabPath");
        return com.trs.common.utils.StringUtils.isEmpty(property)
                ? System.getProperty("kafka_kerberos_userKeytabPath")
                : property;
    }

    /**
     * Kafka的principal
     *
     * @return principal
     */
    private String getPrincipal() {
        String property = env.getProperty("kafka.kerberos.principal");
        return com.trs.common.utils.StringUtils.isEmpty(property)
                ? System.getProperty("kafka_kerberos_principal")
                : property;
    }
}
