package com.trs.police.common.core.utils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;

/**
 * 计算时间工具类
 *
 * <AUTHOR>
 * @date 2022/6/29 18:23
 */
public class MonitorTimeUtil {

    private MonitorTimeUtil() {}

    /**
     * 默认显示时间
     */
    public static final String DEFAULT_TIME = "- -";

    public static final String SIGN_OVERDUE_HOUR_SUFFIX = "小时后签收逾期！";
    public static final String SIGN_OVERDUE_MINUTE_SUFFIX = "分钟后签收逾期！";

    public static final String JUDGE_OVERDUE_HOUR_SUFFIX = "小时后研判逾期！";
    public static final String JUDGE_OVERDUE_MINUTE_SUFFIX = "分钟后研判逾期！";

    public static final String REPLY_OVERDUE_HOUR_SUFFIX = "小时后反馈逾期！";
    public static final String REPLY_OVERDUE_MINUTE_SUFFIX = "分钟后反馈逾期！";

    public static final String SIGN_OVERDUE_NOTICE = "签收已逾期！";
    public static final String JUDGE_OVERDUE_NOTICE = "研判已逾期！";
    public static final String REPLY_OVERDUE_NOTICE = "反馈已逾期！";

    /**
     * 计算目标时间到现在的时差(小时)
     *
     * @param target 目标时间
     * @return java.lang.Long 时差
     **/
    public static Long hoursToTargetTime(LocalDateTime target) {
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(now, target);
        return duration.toHours();
    }

    /**
     * 计算目标时间到现在的时差(分钟)
     *
     * @param target 目标时间
     * @return java.lang.Long 时差
     **/
    public static Long minutesToTargetTime(LocalDateTime target) {
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(now, target);
        return duration.toMinutes();
    }

    /**
     * 计算目标时间到现在的时差(天)
     *
     * @param target 目标时间
     * @return java.lang.Long 时差
     **/
    public static Long daysToDeadLine(LocalDateTime target) {
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(now, target);
        return duration.toDays();
    }

    /**
     * 计算days天后的时间
     *
     * @param days 天数
     * @return java.time.LocalDateTime days天后的时间
     **/
    public static LocalDateTime daysAfterNow(Integer days) {
        return LocalDateTime.now().minus(-days, ChronoUnit.DAYS);
    }

    /**
     * 计算到期时间
     *
     * @param time 时间
     * @return {@link String}
     */
    public static String calculateDeadLine(LocalDateTime time) {
        if (time == null || time.isBefore(LocalDateTime.now())) {
            return DEFAULT_TIME;
        }
        Long hours = hoursToTargetTime(time);
        if (hours < 24) {
            return time + "小时后";
        }
        Long days = daysToDeadLine(time);
        return days + "天后";
    }

    /**
     * 计算失效时间
     *
     * @param hours 时间
     * @return {@link String}
     */
    public static String calculateDaysAndHours(int hours) {
        if (hours <= 0){
            return "已失效";
        }
        int days = hours / 24;
        int remainingHours = hours % 24;
        if (days == 0){
            return remainingHours + "小时后";
        }
        if (remainingHours == 0){
            return days + "天后";
        }
        return days + "天" + remainingHours + "小时后";
    }

    /**
     * 计算到期时间
     *
     * @param time 时间
     * @return {@link String}
     */
    public static String calculateCreateTime(LocalDateTime time) {
        if (time == null) {
            return DEFAULT_TIME;
        }
        LocalDateTime toDay = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime yesterday = toDay.plusDays(-1);
        LocalDateTime theDayBeforeYesterday = toDay.plusDays(-2);
        LocalDateTime thisYear = LocalDateTime.of(
            LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        String format = time.format(DateTimeFormatter.ofPattern("HH:mm"));
        if (time.isAfter(toDay)) {
            return format;
        } else if (time.isAfter(yesterday)) {
            return "昨天" + format;
        } else if (time.isAfter(theDayBeforeYesterday)) {
            return "前天" + format;
        } else if (time.isAfter(thisYear)) {
            return time.format(DateTimeFormatter.ofPattern("MM-dd HH:mm"));
        } else {
            return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        }
    }
}
