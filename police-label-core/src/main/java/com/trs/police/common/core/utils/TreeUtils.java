package com.trs.police.common.core.utils;
import com.trs.police.common.core.vo.Tree;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 构造树形结构的工具类。
 *
 * <p>该工具类提供了一组方法，用于构建树形结构，解析树形结构的路径等操作。</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022-03-09
 */
public class TreeUtils{
    /**
     * 检索线索连接符。
     */
    public static final String JOIN_CHARACTER = "-";


    /**
     * 通过节点列表构建树形结构。
     *
     * @param data 节点列表，不能为空
     * @param <T>  节点类型，必须实现 Tree 接口
     * @return 树形结构的节点列表
     */
    public static <T extends Tree> List<T> buildTree(List<T> data) {
        return buildTree(data, null, null, null);
    }
    /**
     * 通过节点列表构建树形结构。
     *
     * @param data                     树的节点列表
     * @param sort                     同一个父节点下的元素如何排序
     * @param whenLinkParentAndChildren 当连接父子节点时的回调
     * @param itemFilter               元素过滤器
     * @param <T>                      节点类型，必须实现 Tree 接口
     * @return 树形结构的节点列表
     */
    private static <T extends Tree> List<T> buildTree(
            List<T> data,
            Comparator<T> sort,
            BiConsumer<T, List<T>> whenLinkParentAndChildren,
            Predicate<T> itemFilter) {
        if (null == data || data.isEmpty()) {
            return new ArrayList<>();
        }
        // 通过parentId分组(实际就是构造了各个最小子树)
        Map<Long, List<T>> groupByParent = data.stream()
                .collect(Collectors.groupingBy(Tree::getParentId));
        // 子节点排序
        groupByParent.values().forEach(list -> {
            if (null == sort) {
                // a.子节点默认按照id升序排序
                list.sort((n1, n2) -> Integer.compare(n1.getId(), n2.getId()));
            } else {
                // b.自定义的子节点排序方式
                list.sort(sort);
            }
        });
        // id快速定位到vo
        Map<Long, T> idVo = new HashMap<>();
        for (T vo : data) {
            idVo.put(Long.valueOf(vo.getId()), vo);
        }
        Function<Long, T> findVoById = id -> idVo.get(id);
        // 构造树状结构
        List<T> roots = new ArrayList<>();
        for (Long parentId : groupByParent.keySet()) {
            T parent = findVoById.apply(parentId);
            List<T> children = groupByParent.get(parentId);
            if (null != children && null != itemFilter) {
                children = children.stream()
                        .filter(itemFilter::test)
                        .collect(Collectors.toList());
            }
            if (null == parent) {
                // 没找到父节点说明已经到了根节点了
                roots.addAll(children);
                continue;
            }
            // 回调操作
            if (null != whenLinkParentAndChildren) {
                whenLinkParentAndChildren.accept(parent, children);
            }
            //  关联父子节点的方式
            parent.setChildren(children);
        }
        return roots;
    }
    /**
     * 创建树构造器，用于构建树形结构。
     *
     * @param data 节点列表，不能为空
     * @param <T>  节点类型，必须实现 Tree 接口
     * @return 树构造器
     */
    public static <T extends Tree> TreeBuilder<T> treeBuilder(List<T> data) {
        return new TreeBuilder<>(data);
    }

    /**
     * 构造树的路径枚举。
     *
     * @return 树的路径枚举
     */
    public static String buildKey() {
        return JOIN_CHARACTER;
    }

    /**
     * 根据父节点构造当前节点的路径枚举。
     *
     * @param parent 父节点
     * @param <T>    节点类型，必须实现 Tree 接口
     * @return 当前节点的路径枚举，从根节点id到父节点id的路径 {@link TreeUtils#JOIN_CHARACTER} 连接
     */
    public static <T extends Tree> String buildkey(T parent) {
        if (null == parent) {
            return buildKey();
        }
        StringBuilder builder = new StringBuilder(parent.key())
                .append(parent.getId())
                .append(JOIN_CHARACTER);
        return builder.toString();
    }

    /**
     * 获取仅查询某个节点下（不包括该节点）数据的mysql like查询关键字。
     *
     * @param t   待查询的父节点
     * @param <T> 节点类型，必须实现 Tree 接口
     * @return mysql like查询关键字
     */
    public static <T extends Tree> String searchChildSql(T t) {
        StringBuilder builder = new StringBuilder(t.key())
                .append(t.getId())
                .append(JOIN_CHARACTER)
                // mysql 模糊查询
                .append("%");
        return builder.toString();
    }

    /**
     * 解析路径枚举，获取指定下标位置的id。
     *
     * @param searchKey 路径枚举
     * @param index     分割

    后的下标
     * @return 路径枚举上指定位置的id
     */
    public static Optional<String> searchId(String searchKey, int index) {
        if (null == searchKey || searchKey.isEmpty()) {
            return Optional.empty();
        }
        String[] split = searchKey.split(JOIN_CHARACTER);
        if (index >= split.length) {
            return Optional.empty();
        }
        return Optional.ofNullable(split[index]);
    }

    /**
     * 解析路径枚举，截取从头到指定位置的连接符。
     *
     * @param searchKey 路径枚举
     * @param index     保留指定个数的连接符
     * @return 截取后的路径枚举
     */
    public static Optional<String> submit(String searchKey, int index) {
        if (null == searchKey || searchKey.isEmpty()) {
            return Optional.empty();
        }
        StringBuilder builder = new StringBuilder();
        int count = 0;
        for (char c : searchKey.toCharArray()) {
            if (String.valueOf(c).equals(JOIN_CHARACTER)) {
                count++;
            }
            builder.append(c);
            if (count == index) {
                return Optional.ofNullable(builder.toString());
            }
        }
        return Optional.empty();
    }

    /**
     * 获取父节点路径枚举，从指定父节点处截断。
     *
     * @param searchKey 路径枚举
     * @param parentId  截断的位置
     * @return 包含截断位置之前的父节点路径的字符串数组
     */
    public static String[] getParentIds(String searchKey, String parentId) {
        if (!StringUtil.isEmpty(parentId)
                && !StringUtil.isEmpty(searchKey)
                && !JOIN_CHARACTER.equals(searchKey)
                && searchKey.contains(JOIN_CHARACTER + parentId + JOIN_CHARACTER)
        ) {
            searchKey = searchKey.substring(searchKey.indexOf(parentId) - 1, searchKey.length());
        } else {
            throw new IllegalArgumentException("路径枚举中没有此父节点");
        }
        return getParentIds(searchKey);
    }

    /**
     * 获取父节点路径枚举。
     *
     * @param searchKey 路径枚举
     * @return 包含父类id的字符串数组
     */
    public static String[] getParentIds(String searchKey) {
        if (null == searchKey || searchKey.isEmpty()) {
            return new String[0];
        }
        String[] split = searchKey.split(JOIN_CHARACTER);
        return Arrays.stream(split)
                .filter(id -> null != id && !id.isEmpty())
                .toArray(String[]::new);
    }

    /**
     * 树构造器。
     *
     * @param <T> 节点类型，必须实现 Tree 接口
     */
    public static class TreeBuilder<T extends Tree> {
        /**
         * 数据。
         */
        private List<T> data;

        /**
         * 子节点排序规则。
         */
        private Comparator<T> sort;

        /**
         * 当连接父子节点时的回调。
         */
        private BiConsumer<T, List<T>> whenLinkParentAndChildren;

        /**
         * 元素过滤。
         */
        private Predicate<T> itemFilter;

        TreeBuilder(List<T> data) {
            this.data = data;
        }

        /**
         * 设置子节点排序规则。
         *
         * @param sort 子节点排序规则
         * @return 树构造器
         */
        public TreeBuilder<T> sort(Comparator<T> sort) {
            this.sort = sort;
            return this;
        }

        /**
         * 设置连接父子节点时的回调。
         *
         * @param whenLinkParentAndChildren 连接父子节点时的回调
         * @return 树构造器
         */
        public TreeBuilder<T> whenLinkParentAndChildren(BiConsumer<T, List<T>> whenLinkParentAndChildren) {
            this.whenLinkParentAndChildren = whenLinkParentAndChildren;
            return this;
        }

        /**
         * 设置元素过滤器。
         *
         * @param itemFilter 元素过滤器
         * @return 树构造器
         */
        public TreeBuilder<T> itemFilter(Predicate<T> itemFilter) {
            this.itemFilter = itemFilter;
            return this;
        }

        /**
         * 构建树形结构。
         *
         * @return 树形结构的节点列表
         */
        public List<T> build() {
            return TreeUtils.buildTree(data, sort, whenLinkParentAndChildren, itemFilter);
        }
    }
}