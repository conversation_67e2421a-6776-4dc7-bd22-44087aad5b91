package com.trs.police.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.BiConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * vo参数构造器
 *
 * <AUTHOR>
 */
@Slf4j
public class VoParameterConstructor {

    /**
     * 构造一个builder
     *
     * @param <VO> v
     * @param voList 数据列表
     * @return builder
     */
    public static <VO> Builder<VO> of(List<VO> voList) {
        Builder<VO> builder = new Builder<>(voList);
        return builder;
    }

    /**
     * builder父类
     *
     * @param <VO> v
     */

    public static class Builder<VO> {

        private List<VO> voList;

        private List<Builder<VO>> builderList = new ArrayList<>();


        private Builder() {
        }

        private Builder(List<VO> voList) {
            this.voList = voList;
        }

        /**
         * 单值匹配器
         *
         * @param <R> 查找的数据据类型
         * @param findData 查找数据
         * @param equal equal
         * @return builder
         */
        public <R> SingleValueBuilder<VO, R> singleValueMatcher(Function<List<VO>, List<R>> findData, BiPredicate<VO, R> equal) {
            SingleValueBuilder<VO, R> builder = new SingleValueBuilder<>(getRoot(), findData, equal);
            getRoot().builderList.add(builder);
            return builder;
        }

        /**
         * 多值匹配器
         *
         * @param <R> 查找的数据据类型
         * @param findData 查找数据
         * @param equal equal
         * @return builder
         */

        public <R> MultiValueBuilder<VO, R> multiValueMatcher(Function<List<VO>, List<R>> findData, BiPredicate<VO, R> equal) {
            MultiValueBuilder<VO, R> builder =  new MultiValueBuilder<>(getRoot(), findData, equal);
            getRoot().builderList.add(builder);
            return builder;
        }

        /**
         * build
         */
        public void build() {
            for (Builder<VO> builder : getRoot().builderList) {
                builder.doBuild();
            }
        }

        /**
         * build的具体实现
         */
        protected void doBuild() {
        }

        /**
         * 获取根节点
         *
         * @return 跟节点
         */
        protected Builder<VO> getRoot() {
            return this;
        }

    }

    /**
     * 字段构造
     *
     * @param <VO> v
     * @param <R> 根据字段查询的数据列表
     */
    public abstract static class FieldBuilder<VO, R> extends Builder<VO> {

        protected Builder<VO> rootBuilder;

        protected Function<List<VO>, List<R>> findData;


        protected BiPredicate<VO, R> equal;

        public FieldBuilder(Builder<VO> builder, Function<List<VO>, List<R>> findData, BiPredicate<VO, R> equal) {
            this.rootBuilder = builder;
            this.findData = findData;
            this.equal = equal;
        }

        protected abstract void consumerData(VO vo, List<R> dataList);

        @Override
        protected Builder<VO> getRoot() {
            return rootBuilder;
        }

        @Override
        public void doBuild() {
            if (CollectionUtils.isEmpty(rootBuilder.voList)) {
                return;
            }
            List<R> list = findData.apply(rootBuilder.voList);
            List<R> listNotNull = Objects.isNull(list) ? new ArrayList<>() : list;
            for (VO vo : rootBuilder.voList) {
                List<R> dataList = listNotNull.stream()
                        .filter(r -> equal.test(vo, r))
                        .collect(Collectors.toList());
                consumerData(vo, dataList);
            }
        }
    }

    /**
     * 单值
     *
     * @param <VO> v
     * @param <R> r
     */
    public static class SingleValueBuilder<VO, R> extends FieldBuilder<VO, R> {
        private BiConsumer<VO, R> consumer;

        public SingleValueBuilder(Builder<VO> builder, Function<List<VO>, List<R>> findData, BiPredicate<VO, R> equal) {
            super(builder, findData, equal);
        }

        /**
         * 添加消费者
         *
         * @param consumer consumer
         * @return builder
         */
        public SingleValueBuilder<VO, R> consumer(BiConsumer<VO, R> consumer) {
            this.consumer = consumer;
            return this;
        }

        /**
         * 转换并且消费
         *
         * @param mapper mapper
         * @param mapConsumer consumer
         * @param <R_MAP> 转换的目标类型
         * @return builder
         */
        public <R_MAP> SingleValueBuilder<VO, R> mapAndConsumer(Function<R, R_MAP> mapper, BiConsumer<VO, R_MAP> mapConsumer) {
            this.consumer = (v, d) -> mapConsumer.accept(v, mapper.apply(d));
            return this;
        }

        @Override
        protected void consumerData(VO vo, List<R> dataList) {
            if (Objects.isNull(vo) || CollectionUtils.isEmpty(dataList)) {
                return;
            }
            if (dataList.size() > 1) {
                log.warn("对象：{}匹配到多个值：{}, 仅仅使用第一个值", JSON.toJSONString(vo), JSON.toJSONString(dataList));
            }
            consumer.accept(vo, dataList.get(0));
        }
    }


    /**
     * 多值构造器
     *
     * @param <VO> v
     * @param <R> r
     */
    public static class MultiValueBuilder<VO, R> extends FieldBuilder<VO, R> {
        private BiConsumer<VO, List<R>> consumer;

        public MultiValueBuilder(Builder<VO> builder, Function<List<VO>, List<R>> findData, BiPredicate<VO, R> equal) {
            super(builder, findData, equal);
        }


        /**
         * 消费
         *
         * @param consumer consumer
         * @return builder
         */
        public MultiValueBuilder<VO, R> consumer(BiConsumer<VO, List<R>> consumer) {
            this.consumer = consumer;
            return this;
        }

        /**
         * 转换并且消费
         *
         * @param mapper mapper
         * @param mapConsumer consumer
         * @param <R_MAP> 目标对象
         * @return builder
         */
        public <R_MAP> MultiValueBuilder<VO, R> mapAndConsumer(Function<R, R_MAP> mapper, BiConsumer<VO, List<R_MAP>> mapConsumer) {
            this.consumer = (v, dataList) -> {
                List<R_MAP> collect = dataList.stream()
                        .map(mapper)
                        .collect(Collectors.toList());
                mapConsumer.accept(v, collect);
            };
            return this;
        }

        /**
         * 转换成单值并且消费
         *
         * @param mapper mapper
         * @param mapSingleConsumer consumer
         * @param <R_MAP> 转换成目标对象
         * @return builder
         */
        public <R_MAP> MultiValueBuilder<VO, R> mapSingleAndConsumer(Function<List<R>, R_MAP> mapper, BiConsumer<VO, R_MAP> mapSingleConsumer) {
            this.consumer = (v, dataList) -> mapSingleConsumer.accept(v, mapper.apply(dataList));
            return this;
        }

        @Override
        protected void consumerData(VO vo, List<R> dataList) {
            if (Objects.isNull(vo)) {
                return;
            }
            consumer.accept(vo, dataList);
        }
    }
}
