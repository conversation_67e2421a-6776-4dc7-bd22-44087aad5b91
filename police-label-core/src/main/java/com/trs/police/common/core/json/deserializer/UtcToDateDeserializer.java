package com.trs.police.common.core.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.trs.police.common.core.utils.DateUtil;
import java.io.IOException;
import java.time.LocalDate;

/**
 * jackson时间戳转日期的反序列化器
 *
 * <AUTHOR> lai.yi
 * @date : 2020/7/8
 **/
public class UtcToDateDeserializer extends JsonDeserializer<LocalDate> implements ContextualDeserializer {

    private JavaType valueType;

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext context, BeanProperty property) {
        JavaType javaType = property.getType();
        UtcToDateDeserializer deserializer = new UtcToDateDeserializer();
        deserializer.valueType = javaType;
        return deserializer;
    }

    @Override
    public LocalDate deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
        throws IOException {
        long utc = jsonParser.getValueAsLong();
        if (valueType.getRawClass().equals(LocalDate.class)) {
            return DateUtil.utcToLocalDateTime(utc).toLocalDate();
        } else {
            return null;
        }
    }
}
