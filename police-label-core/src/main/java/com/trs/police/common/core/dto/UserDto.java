package com.trs.police.common.core.dto;

import com.chenhaiyang.plugin.mybatis.sensitive.annotation.EncryptField;
import com.chenhaiyang.plugin.mybatis.sensitive.annotation.SensitiveEncryptEnabled;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 登录用户信息
 *
 * <AUTHOR>
 */
@SensitiveEncryptEnabled
@Data
public class UserDto implements Serializable {

    private static final long serialVersionUID = -3185126461094468050L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号码
     */
    private String idNumber;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 性别
     */
    private String gender;

    /**
     * 手机号码
     */
    private String tel;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 警号
     */
    private String policeCode;

    /**
     * 账号有效期限,默认三年
     */
    private LocalDateTime validDate;

    /**
     * 个人签章图片id
     */
    private Long signature;

    /**
     * 个人头像图片位置
     */
    private String avatar;

    /**
     * 职务
     */
    private String duty;

    /**
     * 账户状态
     */
    private Integer status;

    /**
     * 部门信息
     */
    private List<DeptDto> deptList;

    /**
     * 身份证号码
     */
    @EncryptField
    private String zjhm;

    /**
     * mac
     */
    private String mac;

    /**
     * 岗位代码
     */
    private Long postCode;
}
