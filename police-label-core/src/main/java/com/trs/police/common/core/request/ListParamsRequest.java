package com.trs.police.common.core.request;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 列表查询参数
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class ListParamsRequest {

    /**
     * 分页参数
     */
    private PageParams pageParams;

    /**
     * 文本检索参数
     */
    private SearchParams searchParams;

    /**
     * 排序参数
     */
    private SortParams sortParams;

    /**
     * 动态参数
     */
    private List<KeyValueTypeVO> filterParams;

    /**
     * 部门代码
     */
    private String organCode;

    public ListParamsRequest() {
        this.searchParams = new SearchParams();
        this.filterParams = new ArrayList<>();
        this.sortParams = new SortParams();
    }

    /**
     * 只分页的列表查询请求
     *
     * @param pageParams 分页
     */
    public ListParamsRequest(PageParams pageParams) {
        this.pageParams = pageParams;
        this.searchParams = new SearchParams();
        this.filterParams = new ArrayList<>();
        this.sortParams = new SortParams();
    }

    /**
     * 构建参数
     *
     * @param filterParams 动态参数
     */
    public ListParamsRequest(List<KeyValueTypeVO> filterParams) {
        this.pageParams = new PageParams();
        this.searchParams = new SearchParams();
        this.filterParams = filterParams;
        this.sortParams = new SortParams();
    }

    /**
     * 查找相关参数
     *
     * @param key 参数key
     * @return 参数
     */
    public Optional<KeyValueTypeVO> findNotNullParams(String key) {
        if (null == key || key.isEmpty()) {
            return Optional.empty();
        }
        return getFilterParams().stream()
                .filter(it -> key.equals(it.getKey()))
                .filter(it -> Objects.nonNull(it.getValue()))
                .findAny();
    }

    /**
     * 参数空检查
     */
    public void nullCheck() {
        if (null == pageParams) {
            pageParams = new PageParams();
        }
        if (null == searchParams) {
            searchParams = new SearchParams();
        }
        if (null == filterParams) {
            filterParams = new ArrayList<>();
        }
        if (null == sortParams) {
            sortParams = new SortParams();
        }
    }
}
