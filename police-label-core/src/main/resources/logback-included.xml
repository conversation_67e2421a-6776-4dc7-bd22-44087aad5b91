<included>
    <contextName>nacos</contextName>
    <!--定义key-value对    后续使用使用${PATTERN}来获取同样的value -->
    <property name="CONSOLE_PATTERN"
        value="%yellow(%d{yyyy-MM-dd HH:mm:ss}) %highlight(%-5level) %red([%t]) %cyan([%C{30}:%L]): %magenta(%msg) %n %throwable"/>

    <property name="FILE_PATTERN"
        value="%d{yyyy-MM-dd HH:mm:ss} %-5level [%t] [%C{30}:%L]: %msg %n %throwable"/>

    <!--ConsoleAppender 用于在控制台/屏幕输出日志 日志输出器-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!--定义了一个过滤器,在配置级别之下的日志输出不会被打印出来-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <!-- encoder 默认配置为PatternLayoutEncoder -->
        <!--定义控制台输出格式-->
        <encoder>
            <pattern>${CONSOLE_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--文件输出器1，为配置级别过滤-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--定义文件输出路径 为项目根目录下-->
        <file>logs/info.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--定义文件滚动时的文件名的格式-->
            <fileNamePattern>logs/info-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--收集kafka日志-->
    <appender name="kafka" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/kafka.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--定义文件滚动时的文件名的格式-->
            <fileNamePattern>logs/kafka-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>

    <!--alibaba下日志直接到文件，里面涉及使用到的nacos 会持续发送心跳检测info级别，控制台不输出-->
    <logger name="com.alibaba" level="warn" additivity="false">
    </logger>
</included>